<?php

use Illuminate\Database\Seeder;

class PostTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $post = \App\Models\Post::create([
            'description'     	=> 'التعليم هو عملية تسهيل التعلم',
            'short_description' => ' اكتساب المعرفة والمهارات والقيم والعادات. تشمل الأساليب التعليمية التدريس والتدريب ورواية القصص',
        ]);

        $post = \App\Models\Post::create([
            'description'     	=> 'التعليم هو عملية تسهيل التعلم',
            'short_description' => ' اكتساب المعرفة والمهارات والقيم والعادات. تشمل الأساليب التعليمية التدريس والتدريب ورواية القصص',
        ]);

        $post = \App\Models\Post::create([
            'description'     	=> 'التعليم هو عملية تسهيل التعلم',
            'short_description' => ' اكتساب المعرفة والمهارات والقيم والعادات. تشمل الأساليب التعليمية التدريس والتدريب ورواية القصص',
        ]);

        $post = \App\Models\Post::create([
            'description'     	=> 'التعليم هو عملية تسهيل التعلم',
            'short_description' => ' اكتساب المعرفة والمهارات والقيم والعادات. تشمل الأساليب التعليمية التدريس والتدريب ورواية القصص',
        ]);

        $post = \App\Models\Post::create([
            'description'     	=> 'التعليم هو عملية تسهيل التعلم',
            'short_description' => ' اكتساب المعرفة والمهارات والقيم والعادات. تشمل الأساليب التعليمية التدريس والتدريب ورواية القصص',
        ]);

        $post = \App\Models\Post::create([
            'description'     	=> 'التعليم هو عملية تسهيل التعلم',
            'short_description' => ' اكتساب المعرفة والمهارات والقيم والعادات. تشمل الأساليب التعليمية التدريس والتدريب ورواية القصص',
        ]);
    }
}
