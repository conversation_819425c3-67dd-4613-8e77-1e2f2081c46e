@extends('layouts.dashboard.app')

@section('content')
<div class="content-wrapper">
    <section class="content-header">
        <div class="header-content">
            <h1 style="color: #3c8dbc; font-weight: 600;">
                <i class="fa fa-edit"></i> تعديل الدورة: {{ $course->name }}
            </h1>
            <p style="color: #666; margin-top: 5px;">تحديث معلومات الدورة التدريبية</p>
        </div>

        <ol class="breadcrumb">
            <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> لوحة التحكم</a></li>
            <li><a href="{{ route('dashboard.courses.index') }}"><i class="fa fa-graduation-cap"></i> الدورات</a></li>
            <li class="active"><i class="fa fa-edit"></i> تعديل الدورة</li>
        </ol>
    </section>

    <section class="content">
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route('dashboard.courses.update', $course->id) }}" method="post" enctype="multipart/form-data" id="courseEditForm">
            {{ csrf_field() }}
            {{ method_field('put') }}

            <div class="row">
                <!-- القسم الأول: المعلومات الأساسية -->
                <div class="col-md-8">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-info-circle"></i> المعلومات الأساسية للدورة
                            </h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="name">
                                            <i class="fa fa-graduation-cap text-primary"></i> اسم الدورة <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" name="name" id="name" class="form-control"
                                               value="{{ old('name', $course->name) }}" placeholder="أدخل اسم الدورة التدريبية" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="url">
                                            <i class="fa fa-link text-info"></i> رابط الدورة
                                        </label>
                                        <input type="url" name="url" id="url" class="form-control"
                                               value="{{ old('url', $course->url) }}" placeholder="https://example.com/course">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="categories_id">
                                            <i class="fa fa-folder text-warning"></i> القسم <span class="text-danger">*</span>
                                        </label>
                                        <select name="categories_id" id="categories_id" class="form-control select2" required>
                                            <option value="">اختر القسم</option>
                                            @foreach ($categories as $categorie)
                                                <option value="{{ $categorie->id }}" {{ old('categories_id', $course->categories_id) == $categorie->id ? 'selected' : '' }}>
                                                    {{ $categorie->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="Short_description">
                                    <i class="fa fa-file-text-o text-success"></i> الوصف المختصر <span class="text-danger">*</span>
                                </label>
                                <textarea name="Short_description" id="Short_description" class="form-control" rows="2"
                                          placeholder="وصف مختصر وجذاب للدورة (سيظهر في قائمة الدورات)" required>{{ old('Short_description', $course->Short_description) }}</textarea>
                                <small class="text-muted">يُنصح بألا يتجاوز 150 حرف</small>
                            </div>

                            <div class="form-group">
                                <label for="description">
                                    <i class="fa fa-align-left text-primary"></i> الوصف التفصيلي <span class="text-danger">*</span>
                                </label>
                                <textarea name="description" id="description" class="form-control" rows="5"
                                          placeholder="وصف تفصيلي شامل للدورة ومحتواها" required>{{ old('description', $course->description) }}</textarea>
                            </div>
                        </div>
                    </div>
                    <!-- القسم الثاني: الأهداف والفئة المستهدفة -->
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-bullseye"></i> أهداف الدورة والفئة المستهدفة
                            </h3>
                        </div>
                        <div class="box-body">
                            <div class="form-group">
                                <label for="course_objectives">
                                    <i class="fa fa-check-circle text-success"></i> أهداف الدورة
                                </label>
                                <textarea name="course_objectives" id="course_objectives" class="form-control" rows="4"
                                          placeholder="أدخل أهداف الدورة (مثال: ✅ إكساب المشاركين مهارات...)">{{ old('course_objectives', $course->course_objectives ?? '') }}</textarea>
                                <small class="text-muted">استخدم رموز ✅ أو • لتنسيق الأهداف</small>
                            </div>

                            <div class="form-group">
                                <label for="target_audience">
                                    <i class="fa fa-users text-info"></i> الفئة المستهدفة
                                </label>
                                <textarea name="target_audience" id="target_audience" class="form-control" rows="4"
                                          placeholder="أدخل الفئة المستهدفة (مثال: ✔️ مدراء المشتريات...)">{{ old('target_audience', $course->target_audience ?? '') }}</textarea>
                                <small class="text-muted">استخدم رموز ✔️ أو • لتنسيق الفئات</small>
                            </div>
                        </div>
                    </div>

                    <!-- القسم الثالث: المدربين -->
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-users"></i> مدربي الدورة
                            </h3>
                        </div>
                        <div class="box-body">
                            <!-- المدربين الحاليين -->
                            @if($course->instructors && $course->instructors->count() > 0)
                                <div class="current-instructors mb-3">
                                    <h5><i class="fa fa-check-circle text-success"></i> المدربين الحاليين:</h5>
                                    <div class="current-instructors-list">
                                        @foreach($course->instructors as $instructor)
                                            <div class="instructor-item current" style="background: #d4edda; padding: 8px; margin: 5px 0; border-radius: 5px; border-left: 3px solid #28a745;">
                                                <i class="fa fa-user text-success"></i>
                                                {{ $instructor->name }}
                                                @if($instructor->rating)
                                                    (⭐ {{ $instructor->rating }}/5)
                                                @endif
                                                - {{ $instructor->email }}
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <div class="form-group">
                                <label for="instructors">
                                    <i class="fa fa-user text-primary"></i> تحديث المدربين
                                </label>
                                <select name="instructors[]" id="instructors" class="form-control select2" multiple>
                                    @foreach($instructors as $instructor)
                                        <option value="{{ $instructor->id }}"
                                            {{ in_array($instructor->id, old('instructors', $course->instructors->pluck('id')->toArray())) ? 'selected' : '' }}>
                                            {{ $instructor->name }}
                                            @if($instructor->rating)
                                                (⭐ {{ $instructor->rating }}/5)
                                            @endif
                                            - {{ $instructor->email }}
                                        </option>
                                    @endforeach
                                </select>
                                <small class="text-muted">يمكنك إضافة أو إزالة المدربين من الدورة</small>
                            </div>

                            @if($instructors->count() == 0)
                                <div class="alert alert-warning">
                                    <i class="fa fa-exclamation-triangle"></i>
                                    لا يوجد مدربين في النظام.
                                    <a href="{{ route('dashboard.users.create') }}" target="_blank">إضافة مدرب جديد</a>
                                </div>
                            @endif

                            <!-- معاينة المدربين المختارين -->
                            <div id="selected-instructors-preview" class="selected-instructors-preview" style="display: none;">
                                <h5><i class="fa fa-eye"></i> المدربين المحدثين:</h5>
                                <div id="instructors-list" class="instructors-list"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الشريط الجانبي -->
                <div class="col-md-4">
                    <!-- معلومات التسعير والتقييم -->
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-money"></i> التسعير والتقييم
                            </h3>
                        </div>
                        <div class="box-body">
                            <div class="form-group">
                                <label for="price">
                                    <i class="fa fa-tag text-success"></i> السعر (دينار جزائري) <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="number" name="price" id="price" class="form-control"
                                           value="{{ old('price', $course->price) }}" placeholder="0" min="0" step="0.01" required>
                                    <span class="input-group-addon">دج</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="time">
                                    <i class="fa fa-clock-o text-primary"></i> عدد الساعات <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="number" name="time" id="time" class="form-control"
                                           value="{{ old('time', $course->time) }}" placeholder="0" min="1" required>
                                    <span class="input-group-addon">ساعة</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="rating">
                                    <i class="fa fa-star text-warning"></i> التقييم
                                </label>
                                <select name="rating" id="rating" class="form-control">
                                    @for ($i = 1; $i <= 5; $i++)
                                        <option value="{{ $i }}" {{ old('rating', $course->rating) == $i ? 'selected' : '' }}>
                                            {{ $i }} {{ $i == 1 ? 'نجمة' : 'نجوم' }}
                                        </option>
                                    @endfor
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- الملفات والوسائط -->
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-file-o"></i> الملفات والوسائط
                            </h3>
                        </div>
                        <div class="box-body">
                            <!-- الصورة الحالية -->
                            <div class="form-group">
                                <label>الصورة الحالية:</label>
                                <div class="current-image-preview">
                                    <img src="{{ $course->image_path }}" style="width: 100%; max-width: 200px; border-radius: 8px;"
                                         class="img-thumbnail" alt="صورة الدورة الحالية">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="image">
                                    <i class="fa fa-image text-success"></i> تغيير صورة الدورة
                                </label>
                                <input type="file" name="image" id="image" class="form-control" accept="image/*">
                                <small class="text-muted">الصيغ المدعومة: JPG, PNG, JPEG</small>
                            </div>

                            <!-- الفيديو الحالي -->
                            @if($course->demo_video)
                            <div class="form-group">
                                <label>الفيديو التعريفي الحالي:</label>
                                <div class="current-video-info">
                                    <p class="text-success">
                                        <i class="fa fa-check-circle"></i> يوجد فيديو تعريفي
                                    </p>
                                </div>
                            </div>
                            @endif

                            <div class="form-group">
                                <label for="demo_video">
                                    <i class="fa fa-video-camera text-danger"></i>
                                    {{ $course->demo_video ? 'تغيير الفيديو التعريفي' : 'إضافة فيديو تعريفي' }}
                                </label>
                                <input type="file" name="demo_video" id="demo_video" class="form-control" accept="video/*">
                                <small class="text-muted">الصيغ المدعومة: MP4, AVI, MOV</small>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="box box-solid">
                        <div class="box-body">
                            <button type="submit" class="btn btn-success btn-lg btn-block">
                                <i class="fa fa-save"></i> حفظ التعديلات
                            </button>
                            <a href="{{ route('dashboard.courses.index') }}" class="btn btn-default btn-lg btn-block">
                                <i class="fa fa-times"></i> إلغاء
                            </a>
                            <a href="{{ route('dashboard.courses.show', $course->id) }}" class="btn btn-info btn-lg btn-block">
                                <i class="fa fa-eye"></i> عرض الدورة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>

    </section><!-- end of content -->

</div><!-- end of content wrapper -->

@endsection

@push('styles')
<style>
.current-image-preview, .current-video-info {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 10px;
}

.select2-container .select2-selection--single {
    height: 34px;
    border-radius: 6px;
}

/* تحسين Select2 للمدربين */
.select2-container--default .select2-selection--multiple {
    border-radius: 6px;
    border: 1px solid #ddd;
    min-height: 34px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #3c8dbc;
    border: 1px solid #3c8dbc;
    color: white;
    border-radius: 4px;
    padding: 2px 8px;
    margin: 2px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-left: 5px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #ffcccc;
}

/* معاينة المدربين */
.selected-instructors-preview {
    margin-top: 15px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.instructor-item {
    transition: all 0.3s ease;
}

.instructor-item:hover {
    background: #e8f4fd !important;
    transform: translateX(5px);
}

/* المدربين الحاليين */
.current-instructors {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.instructor-item.current {
    transition: all 0.3s ease;
}

.instructor-item.current:hover {
    background: #c3e6cb !important;
    transform: translateX(3px);
}

/* تحسين box المدربين */
.box-warning .box-header {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.box-warning .box-title {
    color: white !important;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // تفعيل Select2 للقوائم العادية
    $('.select2:not(#instructors)').select2({
        placeholder: "اختر من القائمة",
        allowClear: true
    });

    // تفعيل Select2 للمدربين مع إعدادات خاصة
    $('#instructors').select2({
        placeholder: "اختر المدربين للدورة",
        allowClear: true,
        closeOnSelect: false,
        dir: "rtl",
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });

    // معاينة المدربين المختارين
    $('#instructors').on('change', function() {
        var selectedInstructors = $(this).val();
        var previewDiv = $('#selected-instructors-preview');
        var listDiv = $('#instructors-list');

        if (selectedInstructors && selectedInstructors.length > 0) {
            var instructorsList = '';

            selectedInstructors.forEach(function(instructorId) {
                var option = $('#instructors option[value="' + instructorId + '"]');
                var instructorText = option.text();

                instructorsList += '<div class="instructor-item" style="background: #f0f8ff; padding: 8px; margin: 5px 0; border-radius: 5px; border-left: 3px solid #3c8dbc;">';
                instructorsList += '<i class="fa fa-user text-primary"></i> ' + instructorText;
                instructorsList += '</div>';
            });

            listDiv.html(instructorsList);
            previewDiv.show();
        } else {
            previewDiv.hide();
        }
    });

    // عداد الأحرف للوصف المختصر
    $('#Short_description').on('input', function() {
        var length = $(this).val().length;
        var maxLength = 150;
        var remaining = maxLength - length;

        if (remaining < 0) {
            $(this).addClass('text-danger');
        } else {
            $(this).removeClass('text-danger');
        }
    });

    // معاينة الصورة الجديدة
    $('#image').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('.current-image-preview img').attr('src', e.target.result);
            };
            reader.readAsDataURL(file);
        }
    });

    // تحقق من النموذج قبل الإرسال
    $('#courseEditForm').on('submit', function(e) {
        var isValid = true;
        var errors = [];

        // التحقق من الحقول المطلوبة
        if (!$('#name').val().trim()) {
            errors.push('اسم الدورة مطلوب');
            isValid = false;
        }

        if (!$('#price').val() || $('#price').val() <= 0) {
            errors.push('السعر مطلوب ويجب أن يكون أكبر من صفر');
            isValid = false;
        }

        if (!$('#time').val() || $('#time').val() <= 0) {
            errors.push('عدد الساعات مطلوب ويجب أن يكون أكبر من صفر');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert('يرجى تصحيح الأخطاء التالية:\n' + errors.join('\n'));
        }
    });

    // إظهار معاينة المدربين عند تحميل الصفحة إذا كان هناك مدربين محددين
    if ($('#instructors').val() && $('#instructors').val().length > 0) {
        $('#instructors').trigger('change');
    }
});
</script>
@endpush
