<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class StudentPaymentTracking extends Model
{
    protected $table = 'student_payments_tracking';
    
    protected $fillable = [
        'purchase_id',
        'student_name',
        'student_email', 
        'student_phone',
        'course_id',
        'course_name',
        'course_price',
        'total_paid',
        'remaining_amount',
        'payment_status',
        'enrollment_date',
        'payment_deadline',
        'completion_date',
        'last_payment_date',
        'notes',
        'is_active'
    ];

    protected $dates = [
        'enrollment_date',
        'payment_deadline',
        'completion_date',
        'last_payment_date'
    ];

    protected $casts = [
        'course_price' => 'decimal:2',
        'total_paid' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'enrollment_date' => 'date',
        'payment_deadline' => 'date',
        'completion_date' => 'date',
        'last_payment_date' => 'date',
        'is_active' => 'boolean'
    ];

    // العلاقات
    public function purchase()
    {
        return $this->belongsTo(Purchase::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function individualPayments()
    {
        return $this->hasMany(IndividualPayment::class, 'payment_tracking_id');
    }

    public function verifiedPayments()
    {
        return $this->hasMany(IndividualPayment::class, 'payment_tracking_id')
                    ->where('status', 'verified');
    }

    // Scopes
    public function scopeNotStarted($query)
    {
        return $query->where('payment_status', 'not_started');
    }

    public function scopePartial($query)
    {
        return $query->where('payment_status', 'partial');
    }

    public function scopeCompleted($query)
    {
        return $query->where('payment_status', 'completed');
    }

    public function scopeOverdue($query)
    {
        return $query->where('payment_status', 'overdue')
                    ->orWhere(function($q) {
                        $q->where('payment_deadline', '<', Carbon::now())
                          ->whereIn('payment_status', ['not_started', 'partial']);
                    });
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Accessors
    public function getPaymentStatusArabicAttribute()
    {
        $statuses = [
            'not_started' => 'لم يبدأ الدفع',
            'partial' => 'دفع جزئي',
            'completed' => 'مكتمل',
            'overdue' => 'متأخر'
        ];

        return $statuses[$this->payment_status] ?? 'غير محدد';
    }

    public function getPaymentProgressAttribute()
    {
        if ($this->course_price == 0) return 0;
        return round(($this->total_paid / $this->course_price) * 100, 2);
    }

    public function getIsOverdueAttribute()
    {
        return $this->payment_deadline && 
               $this->payment_deadline->isPast() && 
               in_array($this->payment_status, ['not_started', 'partial']);
    }

    public function getDaysOverdueAttribute()
    {
        if (!$this->is_overdue) return 0;
        return $this->payment_deadline->diffInDays(Carbon::now());
    }

    public function getDaysUntilDeadlineAttribute()
    {
        if (!$this->payment_deadline) return null;
        if ($this->payment_deadline->isPast()) return 0;
        return Carbon::now()->diffInDays($this->payment_deadline);
    }

    // Methods
    public function addPayment($amount, $paymentMethod, $receiptNumber = null, $receiptImage = null, $notes = null)
    {
        // إنشاء دفعة جديدة
        $payment = IndividualPayment::create([
            'payment_tracking_id' => $this->id,
            'amount' => $amount,
            'payment_date' => Carbon::now(),
            'payment_method' => $paymentMethod,
            'receipt_number' => $receiptNumber,
            'receipt_image' => $receiptImage,
            'notes' => $notes,
            'status' => 'pending'
        ]);

        return $payment;
    }

    public function verifyPayment($paymentId)
    {
        $payment = $this->individualPayments()->find($paymentId);
        
        if ($payment && $payment->status == 'pending') {
            $payment->update(['status' => 'verified']);
            
            // تحديث إجمالي المدفوع
            $this->total_paid += $payment->amount;
            $this->remaining_amount = $this->course_price - $this->total_paid;
            $this->last_payment_date = $payment->payment_date;
            
            // تحديث حالة الدفع
            if ($this->remaining_amount <= 0) {
                $this->payment_status = 'completed';
                $this->completion_date = Carbon::now();
                $this->remaining_amount = 0;
                
                // تحديث حالة المشتري في جدول purchases
                $this->purchase->update(['status' => '1']); // مقبول
                
            } elseif ($this->total_paid > 0) {
                $this->payment_status = 'partial';
            }
            
            $this->save();
        }
        
        return $this;
    }

    public function rejectPayment($paymentId, $reason = null)
    {
        $payment = $this->individualPayments()->find($paymentId);
        
        if ($payment && $payment->status == 'pending') {
            $payment->update([
                'status' => 'rejected',
                'notes' => $reason
            ]);
        }
        
        return $this;
    }

    public function updateOverdueStatus()
    {
        if ($this->is_overdue && $this->payment_status != 'completed') {
            $this->payment_status = 'overdue';
            $this->save();
        }
        
        return $this;
    }

    public static function createFromPurchase(Purchase $purchase, $coursePrice, $paymentDeadline = null)
    {
        return self::create([
            'purchase_id' => $purchase->id,
            'student_name' => $purchase->first_name . ' ' . $purchase->last_name,
            'student_email' => $purchase->email,
            'student_phone' => $purchase->phone,
            'course_id' => $purchase->course_id,
            'course_name' => $purchase->name_course,
            'course_price' => $coursePrice,
            'total_paid' => 0,
            'remaining_amount' => $coursePrice,
            'payment_status' => 'not_started',
            'enrollment_date' => $purchase->created_at,
            'payment_deadline' => $paymentDeadline,
            'is_active' => true
        ]);
    }
}
