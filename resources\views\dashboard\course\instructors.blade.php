@extends('layouts.dashboard.app')

@section('content')
<div class="content-wrapper">
    <section class="content-header">
        <h1 style="color: #3c8dbc; font-weight: 600;">
            <i class="fa fa-users"></i> إدارة مدربي الدورة
        </h1>
        <p style="color: #666; margin-top: 5px;">الدورة: {{ $course->name }}</p>

        <ol class="breadcrumb">
            <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> لوحة التحكم</a></li>
            <li><a href="{{ route('dashboard.courses.index') }}"><i class="fa fa-graduation-cap"></i> الدورات</a></li>
            <li class="active"><i class="fa fa-users"></i> إدارة المدربين</li>
        </ol>
    </section>

    <section class="content">
        <div class="row">
            <!-- المدربين الحاليين -->
            <div class="col-md-6">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">
                            <i class="fa fa-check-circle"></i> المدربين المعينين
                        </h3>
                    </div>
                    <div class="box-body">
                        @if($course->instructors->count() > 0)
                            <div class="list-group">
                                @foreach($course->instructors as $instructor)
                                    <div class="list-group-item">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <img src="{{ asset('uploads/user_images/' . $instructor->image) }}" 
                                                     class="img-circle" style="width: 50px; height: 50px;">
                                            </div>
                                            <div class="col-md-7">
                                                <h5>{{ $instructor->name }}</h5>
                                                <p class="text-muted">{{ $instructor->email }}</p>
                                                <small class="text-info">
                                                    <i class="fa fa-star"></i> التقييم: {{ $instructor->rating ?? 'غير محدد' }}
                                                </small>
                                            </div>
                                            <div class="col-md-3 text-center">
                                                <form action="{{ route('dashboard.courses.remove-instructor', [$course->id, $instructor->id]) }}" 
                                                      method="POST" style="display: inline;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('هل تريد إزالة هذا المدرب؟')">
                                                        <i class="fa fa-times"></i> إزالة
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle"></i>
                                لم يتم تعيين أي مدربين لهذه الدورة بعد
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- إضافة مدربين جدد -->
            <div class="col-md-6">
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title">
                            <i class="fa fa-plus-circle"></i> إضافة مدربين
                        </h3>
                    </div>
                    <div class="box-body">
                        <form action="{{ route('dashboard.courses.add-instructor', $course->id) }}" method="POST">
                            @csrf
                            <div class="form-group">
                                <label for="instructor_id">اختر المدرب:</label>
                                <select name="instructor_id" id="instructor_id" class="form-control select2" required>
                                    <option value="">-- اختر المدرب --</option>
                                    @foreach($availableInstructors as $instructor)
                                        <option value="{{ $instructor->id }}">
                                            {{ $instructor->name }} - {{ $instructor->email }}
                                            @if($instructor->rating)
                                                (⭐ {{ $instructor->rating }})
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-success btn-block">
                                    <i class="fa fa-plus"></i> إضافة المدرب
                                </button>
                            </div>
                        </form>

                        @if($availableInstructors->count() == 0)
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle"></i>
                                جميع المدربين المتاحين تم تعيينهم لهذه الدورة
                            </div>
                        @endif
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title">
                            <i class="fa fa-bar-chart"></i> إحصائيات
                        </h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-box bg-aqua">
                                    <span class="info-box-icon"><i class="fa fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">المدربين المعينين</span>
                                        <span class="info-box-number">{{ $course->instructors->count() }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-box bg-green">
                                    <span class="info-box-icon"><i class="fa fa-user-plus"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">المدربين المتاحين</span>
                                        <span class="info-box-number">{{ $availableInstructors->count() }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التنقل -->
        <div class="row">
            <div class="col-md-12">
                <div class="box">
                    <div class="box-body text-center">
                        <a href="{{ route('dashboard.courses.edit', $course->id) }}" class="btn btn-primary">
                            <i class="fa fa-edit"></i> تعديل الدورة
                        </a>
                        <a href="{{ route('dashboard.courses.show', $course->id) }}" class="btn btn-info">
                            <i class="fa fa-eye"></i> عرض الدورة
                        </a>
                        <a href="{{ route('dashboard.courses.index') }}" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('styles')
<style>
.list-group-item {
    border-radius: 8px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.info-box {
    border-radius: 8px;
    margin-bottom: 15px;
}

.box {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.select2-container .select2-selection--single {
    height: 34px;
    border-radius: 6px;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    $('.select2').select2({
        placeholder: "اختر المدرب",
        allowClear: true
    });
});
</script>
@endpush
