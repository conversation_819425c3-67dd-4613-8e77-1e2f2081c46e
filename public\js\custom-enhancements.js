/**
 * تحسينات مخصصة لأكاديمية Leaders Vision
 * Custom Enhancements for Leaders Vision Academy
 */

$(document).ready(function() {

    // تحسين الأداء - تأخير تحميل الصور
    lazyLoadImages();

    // تأثيرات العد التصاعدي للإحصائيات
    animateCounters();

    // تحسين التنقل السلس
    smoothScrolling();

    // تأثيرات الظهور عند التمرير
    scrollAnimations();

    // تحسين النماذج
    enhanceForms();

    // تحسين الأزرار
    enhanceButtons();

    // تحسين البطاقات
    enhanceCards();

    // إضافة مؤشر التحميل
    addLoadingIndicators();

});

/**
 * تحميل الصور بشكل تدريجي لتحسين الأداء
 */
function lazyLoadImages() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * تأثير العد التصاعدي للأرقام
 */
function animateCounters() {
    const counters = document.querySelectorAll('.counter-number');

    const countUp = (element) => {
        const target = parseInt(element.getAttribute('data-count'));
        const increment = target / 100;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            element.textContent = Math.floor(current);

            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            }
        }, 20);
    };

    // تشغيل العد عند ظهور العنصر
    if ('IntersectionObserver' in window) {
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    countUp(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => {
            counterObserver.observe(counter);
        });
    } else {
        // للمتصفحات القديمة
        counters.forEach(counter => {
            countUp(counter);
        });
    }
}

/**
 * التنقل السلس بين الأقسام
 */
function smoothScrolling() {
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();

        const target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 80
            }, 800, 'easeInOutQuart');
        }
    });
}

/**
 * تأثيرات الظهور عند التمرير
 */
function scrollAnimations() {
    // إضافة فئات CSS للعناصر عند ظهورها
    const animateOnScroll = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1
    });

    // مراقبة العناصر التي تحتاج تأثيرات
    document.querySelectorAll('.hover-effect, .smooth-transition').forEach(el => {
        animateOnScroll.observe(el);
    });
}

/**
 * تحسين النماذج
 */
function enhanceForms() {
    // إضافة تأثيرات للحقول
    $('input, textarea, select').on('focus', function() {
        $(this).parent().addClass('field-focused');
    }).on('blur', function() {
        $(this).parent().removeClass('field-focused');

        // التحقق من صحة البيانات
        validateField($(this));
    });

    // تحسين أزرار الإرسال
    $('form').on('submit', function(e) {
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');

        // إضافة مؤشر التحميل
        submitBtn.html('<i class="fa fa-spinner fa-spin"></i> جاري الإرسال...');
        submitBtn.prop('disabled', true);

        // إعادة تفعيل الزر بعد 3 ثوان (في حالة عدم إعادة التوجيه)
        setTimeout(() => {
            submitBtn.prop('disabled', false);
            submitBtn.html('إرسال');
        }, 3000);
    });
}

/**
 * التحقق من صحة الحقول
 */
function validateField(field) {
    const value = field.val().trim();
    const fieldType = field.attr('type') || field.prop('tagName').toLowerCase();

    // إزالة رسائل الخطأ السابقة
    field.removeClass('is-invalid is-valid');
    field.next('.invalid-feedback').remove();

    let isValid = true;
    let errorMessage = '';

    // التحقق حسب نوع الحقل
    switch(fieldType) {
        case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (value && !emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'يرجى إدخال بريد إلكتروني صحيح';
            }
            break;

        case 'tel':
            const phoneRegex = /^[0-9+\-\s()]+$/;
            if (value && !phoneRegex.test(value)) {
                isValid = false;
                errorMessage = 'يرجى إدخال رقم هاتف صحيح';
            }
            break;
    }

    // إضافة الفئات والرسائل المناسبة
    if (value && isValid) {
        field.addClass('is-valid');
    } else if (value && !isValid) {
        field.addClass('is-invalid');
        field.after(`<div class="invalid-feedback">${errorMessage}</div>`);
    }
}

/**
 * تحسين الأزرار
 */
function enhanceButtons() {
    // إضافة تأثير الموجة عند النقر
    $('.btn').on('click', function(e) {
        const button = $(this);
        const ripple = $('<span class="ripple"></span>');

        button.append(ripple);

        const x = e.pageX - button.offset().left;
        const y = e.pageY - button.offset().top;

        ripple.css({
            left: x,
            top: y
        }).addClass('animate');

        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
}

/**
 * تحسين البطاقات
 */
function enhanceCards() {
    // إضافة تأثير الظل عند التمرير
    $('.card, .course-item').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
}

/**
 * إضافة مؤشرات التحميل
 */
function addLoadingIndicators() {
    // مؤشر التحميل للصفحة
    $(window).on('load', function() {
        $('.loader').fadeOut(500);
    });

    // مؤشر التحميل للروابط الخارجية
    $('a[href^="http"]').on('click', function() {
        const link = $(this);
        if (!link.hasClass('no-loading')) {
            link.append(' <i class="fa fa-spinner fa-spin"></i>');
        }
    });
}

/**
 * تحسين الأداء - تأخير تنفيذ الوظائف
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * تحسين البحث المباشر
 */
function enhanceSearch() {
    const searchInput = $('#search-input');
    if (searchInput.length) {
        const debouncedSearch = debounce(function(query) {
            if (query.length > 2) {
                // تنفيذ البحث
                performSearch(query);
            }
        }, 300);

        searchInput.on('input', function() {
            debouncedSearch($(this).val());
        });
    }
}

/**
 * تنفيذ البحث
 */
function performSearch(query) {
    // يمكن تخصيص هذه الوظيفة حسب الحاجة
    console.log('البحث عن:', query);
}

/**
 * إضافة تأثيرات CSS مخصصة
 */
const customStyles = `
<style>
.animate-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.field-focused {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(38, 199, 249, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.course-image:hover .course-overlay {
    opacity: 1;
}
</style>
`;

// إضافة الأنماط المخصصة
$('head').append(customStyles);
