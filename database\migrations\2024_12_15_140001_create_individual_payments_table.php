<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIndividualPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('individual_payments', function (Blueprint $table) {
            $table->id();
            
            // ربط مع جدول متابعة الدفعات
            $table->bigInteger('payment_tracking_id')->unsigned();
            $table->foreign('payment_tracking_id')->references('id')->on('student_payments_tracking')->onDelete('cascade');
            
            // معلومات الدفعة
            $table->decimal('amount', 10, 2)->comment('مبلغ الدفعة');
            $table->date('payment_date')->comment('تاريخ الدفع');
            $table->string('payment_method')->comment('طريقة الدفع');
            
            // معلومات الإيصال
            $table->string('receipt_number')->nullable()->comment('رقم الإيصال');
            $table->string('receipt_image')->nullable()->comment('صورة الإيصال');
            $table->string('transaction_id')->nullable()->comment('رقم المعاملة');
            
            // معلومات إضافية
            $table->text('notes')->nullable()->comment('ملاحظات الدفعة');
            $table->string('received_by')->nullable()->comment('استلمها');
            
            // حالة الدفعة
            $table->enum('status', [
                'pending',      // في الانتظار
                'verified',     // تم التحقق
                'rejected'      // مرفوضة
            ])->default('pending')->comment('حالة الدفعة');
            
            $table->timestamps();
            
            // فهارس
            $table->index(['payment_tracking_id']);
            $table->index(['payment_date']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('individual_payments');
    }
}
