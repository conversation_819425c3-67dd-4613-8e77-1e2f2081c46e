<?php $__env->startSection('content'); ?>

    <div class="content-wrapper">

        <section class="content-header">

            <h1>المدربين</h1>

            <ol class="breadcrumb">
                <li><a href="<?php echo e(route('dashboard.welcome')); ?>"><i class="fa fa-dashboard"></i> <?php echo app('translator')->get('dashboard.dashboard'); ?></a></li>
                <li><a href="<?php echo e(route('dashboard.coaches.index')); ?>"> المدربين</a></li>
                <li class="active"><?php echo app('translator')->get('dashboard.edit'); ?></li>
            </ol>
        </section>

        <section class="content">

            <div class="box box-primary">

                <div class="box-header">
                    <h3 class="box-title"><?php echo app('translator')->get('dashboard.edit'); ?></h3>
                </div><!-- end of box header -->

                <div class="box-body">

                    <?php echo $__env->make('partials._errors', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <form action="<?php echo e(route('dashboard.coaches.update', $coache->id)); ?>" method="post" enctype="multipart/form-data">

                        <?php echo e(csrf_field()); ?>

                        <?php echo e(method_field('put')); ?>


                        <div class="form-group">
                            <label><?php echo app('translator')->get('dashboard.name'); ?></label>
                            <input type="text" name="name" class="form-control" value="<?php echo e($coache->name); ?>">
                        </div>

                        <div class="form-group">
                            <label><?php echo app('translator')->get('dashboard.email'); ?></label>
                            <input type="email" name="email" class="form-control" value="<?php echo e($coache->email); ?>">
                        </div>


                        <div class="form-group">
                            <label><?php echo app('translator')->get('dashboard.image'); ?></label>
                            <input type="file" name="image" class="form-control image">
                        </div>

                        <div class="form-group">
                            <img src="<?php echo e($coache->image_path); ?>" style="width: 100px" class="img-thumbnail image-preview" alt="">
                        </div>

                        <div class="form-group">
                            <label><?php echo app('translator')->get('dashboard.phone'); ?></label>
                            <input type="number" name="phone" class="form-control" value="<?php echo e($coache->phone); ?>">
                        </div>

                        <div class="form-group">
                            <label><?php echo app('translator')->get('dashboard.jobs'); ?></label>
                            <input type="text" name="jobs" class="form-control" value="<?php echo e($coache->jobs); ?>">
                        </div>

                        <div class="form-group">
                            <label><?php echo app('translator')->get('dashboard.rating'); ?></label>
                            <input type="text" name="rating" class="form-control" value="<?php echo e($coache->rating); ?>">
                        </div>

                        <div class="form-group">
                            <label><strong><?php echo app('translator')->get('dashboard.text'); ?></strong></label>
                            <textarea class="form-control" name="description"><?php echo e($coache->description); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label><?php echo app('translator')->get('dashboard.password'); ?></label>
                            <input type="password" name="password" class="form-control">
                        </div>

                        <div class="form-group">
                            <label><?php echo app('translator')->get('dashboard.password_confirmation'); ?></label>
                            <input type="password" name="password_confirmation" class="form-control">
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary"><i class="fa fa-edit"></i> <?php echo app('translator')->get('dashboard.edit'); ?></button>
                        </div>

                    </form><!-- end of form -->

                </div><!-- end of box body -->

            </div><!-- end of box -->

        </section><!-- end of content -->

    </div><!-- end of content wrapper -->

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Academy\resources\views/dashboard/coaches/edit.blade.php ENDPATH**/ ?>