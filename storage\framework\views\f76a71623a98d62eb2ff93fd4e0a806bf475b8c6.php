<?php $__env->startSection('content'); ?>

    <div class="content-wrapper">

        <section class="content-header">
            <div class="row">
                <div class="col-md-8">
                    <h1 class="arabic-heading">
                        <i class="fa fa-dashboard text-primary"></i>
                        لوحة التحكم الرئيسية
                    </h1>
                    <p class="arabic-text text-muted">
                        مرحباً بك في لوحة تحكم أكاديمية Leaders Vision - إدارة شاملة لجميع أقسام الأكاديمية
                    </p>
                </div>
                <div class="col-md-4 text-left">
                    <div class="dashboard-date">
                        <i class="fa fa-calendar"></i>
                        <span class="arabic-text"><?php echo e(now()->format('Y/m/d')); ?></span>
                    </div>
                    <div class="dashboard-time">
                        <i class="fa fa-clock-o"></i>
                        <span class="arabic-text" id="current-time"></span>
                    </div>
                </div>
            </div>

            <ol class="breadcrumb">
                <li class="active">
                    <i class="fa fa-dashboard"></i>
                    لوحة التحكم الرئيسية
                </li>
            </ol>
        </section>

        <section class="content">

            <!-- بطاقات الإحصائيات الرئيسية -->
            <div class="row">

                
                <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <div class="info-box hover-effect smooth-transition">
                        <span class="info-box-icon bg-aqua">
                            <i class="fa fa-list-alt"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text arabic-text">فئات الدورات</span>
                            <span class="info-box-number counter-number" data-count="<?php echo e($categorys_count); ?>"><?php echo e($categorys_count); ?></span>
                            <div class="progress">
                                <div class="progress-bar bg-aqua" style="width: 70%"></div>
                            </div>
                            <span class="progress-description arabic-text">
                                <i class="fa fa-arrow-up text-success"></i>
                                نمو مستمر في التنوع
                            </span>
                        </div>
                        <a href="<?php echo e(route('dashboard.categories.index')); ?>" class="info-box-footer">
                            عرض التفاصيل <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>

                
                <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <div class="info-box hover-effect smooth-transition">
                        <span class="info-box-icon bg-green">
                            <i class="fa fa-graduation-cap"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text arabic-text">الدورات التدريبية</span>
                            <span class="info-box-number counter-number" data-count="<?php echo e($courses_count); ?>"><?php echo e($courses_count); ?></span>
                            <div class="progress">
                                <div class="progress-bar bg-green" style="width: 85%"></div>
                            </div>
                            <span class="progress-description arabic-text">
                                <i class="fa fa-arrow-up text-success"></i>
                                محتوى تعليمي متميز
                            </span>
                        </div>
                        <a href="<?php echo e(route('dashboard.courses.index')); ?>" class="info-box-footer">
                            إدارة الدورات <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>

                
                <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <div class="info-box hover-effect smooth-transition">
                        <span class="info-box-icon bg-red">
                            <i class="fa fa-user-tie"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text arabic-text">المدربين الخبراء</span>
                            <span class="info-box-number counter-number" data-count="<?php echo e($coachess_count); ?>"><?php echo e($coachess_count); ?></span>
                            <div class="progress">
                                <div class="progress-bar bg-red" style="width: 60%"></div>
                            </div>
                            <span class="progress-description arabic-text">
                                <i class="fa fa-arrow-up text-success"></i>
                                فريق متخصص ومؤهل
                            </span>
                        </div>
                        <a href="<?php echo e(route('dashboard.coaches.index')); ?>" class="info-box-footer">
                            إدارة المدربين <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>

                
                <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                    <div class="info-box hover-effect smooth-transition">
                        <span class="info-box-icon bg-yellow">
                            <i class="fa fa-users"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text arabic-text">إجمالي المستخدمين</span>
                            <span class="info-box-number counter-number" data-count="<?php echo e($users_count); ?>"><?php echo e($users_count); ?></span>
                            <div class="progress">
                                <div class="progress-bar bg-yellow" style="width: 90%"></div>
                            </div>
                            <span class="progress-description arabic-text">
                                <i class="fa fa-arrow-up text-success"></i>
                                مجتمع متنامي ونشط
                            </span>
                        </div>
                        <a href="<?php echo e(route('dashboard.users.index')); ?>" class="info-box-footer">
                            إدارة المستخدمين <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>

            </div><!-- end of statistics row -->

            <!-- إحصائيات إضافية -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title arabic-heading">
                                <i class="fa fa-chart-line"></i>
                                إحصائيات الأداء الشهرية
                            </h3>
                            <div class="box-tools pull-left">
                                <button type="button" class="btn btn-box-tool" data-widget="collapse">
                                    <i class="fa fa-minus"></i>
                                </button>
                                <button type="button" class="btn btn-box-tool" data-widget="remove">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="box-body">
                            <div class="chart" id="performance-chart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title arabic-heading">
                                <i class="fa fa-trophy"></i>
                                إنجازات اليوم
                            </h3>
                        </div>
                        <div class="box-body">
                            <div class="achievement-item">
                                <div class="achievement-icon">
                                    <i class="fa fa-user-plus text-success"></i>
                                </div>
                                <div class="achievement-content">
                                    <h4 class="arabic-text">مستخدمين جدد</h4>
                                    <p class="arabic-text text-muted">5 مستخدمين انضموا اليوم</p>
                                </div>
                            </div>

                            <div class="achievement-item">
                                <div class="achievement-icon">
                                    <i class="fa fa-shopping-cart text-primary"></i>
                                </div>
                                <div class="achievement-content">
                                    <h4 class="arabic-text">مبيعات جديدة</h4>
                                    <p class="arabic-text text-muted">3 دورات تم شراؤها</p>
                                </div>
                            </div>

                            <div class="achievement-item">
                                <div class="achievement-icon">
                                    <i class="fa fa-star text-warning"></i>
                                </div>
                                <div class="achievement-content">
                                    <h4 class="arabic-text">تقييمات إيجابية</h4>
                                    <p class="arabic-text text-muted">8 تقييمات 5 نجوم</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- end of additional statistics -->

            <!-- قسم الأنشطة الحديثة والإشعارات -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title arabic-heading">
                                <i class="fa fa-bell"></i>
                                الأنشطة الحديثة
                            </h3>
                        </div>
                        <div class="box-body">
                            <div class="activity-timeline">
                                <div class="activity-item">
                                    <div class="activity-time">
                                        <i class="fa fa-clock-o"></i>
                                        منذ 5 دقائق
                                    </div>
                                    <div class="activity-content">
                                        <h5 class="arabic-text">تسجيل مستخدم جديد</h5>
                                        <p class="arabic-text text-muted">انضم أحمد محمد إلى الأكاديمية</p>
                                    </div>
                                </div>

                                <div class="activity-item">
                                    <div class="activity-time">
                                        <i class="fa fa-clock-o"></i>
                                        منذ 15 دقيقة
                                    </div>
                                    <div class="activity-content">
                                        <h5 class="arabic-text">شراء دورة جديدة</h5>
                                        <p class="arabic-text text-muted">تم شراء دورة "تطوير المواقع"</p>
                                    </div>
                                </div>

                                <div class="activity-item">
                                    <div class="activity-time">
                                        <i class="fa fa-clock-o"></i>
                                        منذ 30 دقيقة
                                    </div>
                                    <div class="activity-content">
                                        <h5 class="arabic-text">إضافة دورة جديدة</h5>
                                        <p class="arabic-text text-muted">تم إضافة دورة "الذكاء الاصطناعي"</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="box-footer">
                            <a href="#" class="btn btn-primary btn-sm">
                                <i class="fa fa-eye"></i>
                                عرض جميع الأنشطة
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title arabic-heading">
                                <i class="fa fa-tasks"></i>
                                المهام المعلقة
                            </h3>
                        </div>
                        <div class="box-body">
                            <div class="task-list">
                                <div class="task-item">
                                    <div class="task-checkbox">
                                        <input type="checkbox" id="task1">
                                        <label for="task1"></label>
                                    </div>
                                    <div class="task-content">
                                        <h5 class="arabic-text">مراجعة الدورات الجديدة</h5>
                                        <p class="arabic-text text-muted">3 دورات في انتظار المراجعة</p>
                                    </div>
                                    <div class="task-priority high">
                                        <i class="fa fa-exclamation-circle"></i>
                                    </div>
                                </div>

                                <div class="task-item">
                                    <div class="task-checkbox">
                                        <input type="checkbox" id="task2">
                                        <label for="task2"></label>
                                    </div>
                                    <div class="task-content">
                                        <h5 class="arabic-text">الرد على الاستشارات</h5>
                                        <p class="arabic-text text-muted">5 استشارات جديدة</p>
                                    </div>
                                    <div class="task-priority medium">
                                        <i class="fa fa-minus-circle"></i>
                                    </div>
                                </div>

                                <div class="task-item">
                                    <div class="task-checkbox">
                                        <input type="checkbox" id="task3">
                                        <label for="task3"></label>
                                    </div>
                                    <div class="task-content">
                                        <h5 class="arabic-text">تحديث معلومات المدربين</h5>
                                        <p class="arabic-text text-muted">2 ملفات تحتاج تحديث</p>
                                    </div>
                                    <div class="task-priority low">
                                        <i class="fa fa-circle-o"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="box-footer">
                            <a href="#" class="btn btn-warning btn-sm">
                                <i class="fa fa-plus"></i>
                                إضافة مهمة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div><!-- end of activities and tasks -->

            <!-- قسم الروابط السريعة -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="box box-default">
                        <div class="box-header with-border">
                            <h3 class="box-title arabic-heading">
                                <i class="fa fa-rocket"></i>
                                الروابط السريعة
                            </h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-2 col-sm-4 col-xs-6">
                                    <a href="<?php echo e(route('dashboard.courses.create')); ?>" class="quick-link">
                                        <div class="quick-link-icon">
                                            <i class="fa fa-plus-circle"></i>
                                        </div>
                                        <div class="quick-link-text arabic-text">
                                            إضافة دورة
                                        </div>
                                    </a>
                                </div>

                                <div class="col-md-2 col-sm-4 col-xs-6">
                                    <a href="<?php echo e(route('dashboard.coaches.create')); ?>" class="quick-link">
                                        <div class="quick-link-icon">
                                            <i class="fa fa-user-plus"></i>
                                        </div>
                                        <div class="quick-link-text arabic-text">
                                            إضافة مدرب
                                        </div>
                                    </a>
                                </div>

                                <div class="col-md-2 col-sm-4 col-xs-6">
                                    <a href="<?php echo e(route('dashboard.posts.create')); ?>" class="quick-link">
                                        <div class="quick-link-icon">
                                            <i class="fa fa-edit"></i>
                                        </div>
                                        <div class="quick-link-text arabic-text">
                                            كتابة منشور
                                        </div>
                                    </a>
                                </div>

                                <div class="col-md-2 col-sm-4 col-xs-6">
                                    <a href="<?php echo e(route('dashboard.categories.create')); ?>" class="quick-link">
                                        <div class="quick-link-icon">
                                            <i class="fa fa-tags"></i>
                                        </div>
                                        <div class="quick-link-text arabic-text">
                                            إضافة فئة
                                        </div>
                                    </a>
                                </div>

                                <div class="col-md-2 col-sm-4 col-xs-6">
                                    <a href="<?php echo e(route('dashboard.purchases.index')); ?>" class="quick-link">
                                        <div class="quick-link-icon">
                                            <i class="fa fa-shopping-cart"></i>
                                        </div>
                                        <div class="quick-link-text arabic-text">
                                            المبيعات
                                        </div>
                                    </a>
                                </div>

                                <div class="col-md-2 col-sm-4 col-xs-6">
                                    <a href="<?php echo e(route('dashboard.simple-payment-tracking')); ?>" class="quick-link">
                                        <div class="quick-link-icon">
                                            <i class="fa fa-credit-card"></i>
                                        </div>
                                        <div class="quick-link-text arabic-text">
                                            متابعة الدفعات
                                        </div>
                                    </a>
                                </div>

                                <div class="col-md-2 col-sm-4 col-xs-6">
                                    <a href="<?php echo e(route('dashboard.about_index')); ?>" class="quick-link">
                                        <div class="quick-link-icon">
                                            <i class="fa fa-cogs"></i>
                                        </div>
                                        <div class="quick-link-text arabic-text">
                                            الإعدادات
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- end of quick links -->

        </section><!-- end of content -->

    </div><!-- end of content wrapper -->

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {

    // تحديث الوقت الحالي
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA');
        $('#current-time').text(timeString);
    }

    // تحديث الوقت كل ثانية
    updateTime();
    setInterval(updateTime, 1000);

    // تأثيرات العد التصاعدي للإحصائيات
    $('.counter-number').each(function() {
        const element = $(this);
        const target = parseInt(element.attr('data-count'));

        if (!isNaN(target)) {
            animateCounter(element, target);
        }
    });

    function animateCounter(element, target) {
        const increment = target / 50;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            element.text(Math.floor(current));

            if (current >= target) {
                element.text(target);
                clearInterval(timer);
            }
        }, 30);
    }

    // تأثيرات hover للبطاقات
    $('.info-box').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );

    // تأثيرات الروابط السريعة
    $('.quick-link').hover(
        function() {
            $(this).find('.quick-link-icon').addClass('bounce');
        },
        function() {
            $(this).find('.quick-link-icon').removeClass('bounce');
        }
    );

    // تفعيل/إلغاء تفعيل المهام
    $('.task-checkbox input[type="checkbox"]').change(function() {
        const taskItem = $(this).closest('.task-item');
        if ($(this).is(':checked')) {
            taskItem.addClass('completed');
        } else {
            taskItem.removeClass('completed');
        }
    });

    // رسم بياني بسيط للأداء
    if ($('#performance-chart').length) {
        drawPerformanceChart();
    }

    function drawPerformanceChart() {
        // يمكن استخدام Chart.js أو أي مكتبة رسوم بيانية أخرى
        const ctx = document.getElementById('performance-chart');
        if (ctx) {
            ctx.innerHTML = `
                <div class="chart-placeholder">
                    <i class="fa fa-chart-line fa-3x text-muted"></i>
                    <p class="arabic-text text-muted">الرسم البياني سيتم تحميله قريباً</p>
                </div>
            `;
        }
    }

    console.log('✅ تم تحميل لوحة التحكم المحسنة');
});
</script>

<style>
/* أنماط إضافية للوحة التحكم */
.dashboard-date, .dashboard-time {
    margin-bottom: 5px;
    color: #666;
}

.dashboard-date i, .dashboard-time i {
    margin-left: 5px;
    color: #26c7f9;
}

.info-box-footer {
    display: block;
    padding: 10px;
    background: rgba(0,0,0,0.1);
    color: white;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
}

.info-box-footer:hover {
    background: rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

.achievement-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 5px;
    background: rgba(0,0,0,0.02);
}

.achievement-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255,255,255,0.8);
    margin-left: 15px;
}

.achievement-content h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
}

.achievement-content p {
    margin: 0;
    font-size: 12px;
}

.activity-timeline {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.activity-time {
    width: 100px;
    font-size: 12px;
    color: #666;
    margin-left: 15px;
}

.activity-content h5 {
    margin: 0 0 5px 0;
    font-size: 14px;
}

.activity-content p {
    margin: 0;
    font-size: 12px;
}

.task-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 5px;
    background: rgba(0,0,0,0.02);
    transition: all 0.3s ease;
}

.task-item.completed {
    opacity: 0.6;
    text-decoration: line-through;
}

.task-checkbox {
    margin-left: 15px;
}

.task-content {
    flex: 1;
}

.task-content h5 {
    margin: 0 0 5px 0;
    font-size: 14px;
}

.task-content p {
    margin: 0;
    font-size: 12px;
}

.task-priority {
    width: 30px;
    text-align: center;
}

.task-priority.high {
    color: #dc3545;
}

.task-priority.medium {
    color: #ffc107;
}

.task-priority.low {
    color: #28a745;
}

.quick-link {
    display: block;
    text-align: center;
    padding: 20px 10px;
    border-radius: 8px;
    background: rgba(0,0,0,0.02);
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.quick-link:hover {
    background: #26c7f9;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.quick-link-icon {
    font-size: 24px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.quick-link-icon.bounce {
    animation: bounce 0.6s ease;
}

@keyframes  bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

.quick-link-text {
    font-size: 12px;
    font-weight: 500;
}

.chart-placeholder {
    text-align: center;
    padding: 50px;
}

.chart-placeholder i {
    display: block;
    margin-bottom: 15px;
}
</style>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.dashboard.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Academy\resources\views/dashboard/welcome.blade.php ENDPATH**/ ?>