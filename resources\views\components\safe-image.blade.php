{{-- مكون آمن لعرض الصور - متوافق مع Laravel 7 --}}
@php
    // قيم افتراضية
    $imageSrc = $src ?? '';
    $imageAlt = $alt ?? 'صورة أكاديمية Leaders Vision';
    $imageClass = $class ?? '';
    $imageStyle = $style ?? '';
    $imageFallback = $fallback ?? 'images/logo.png';
    
    // معالجة مسار الصورة
    if ($imageSrc) {
        // إذا كان المسار يبدأ بـ http
        if (substr($imageSrc, 0, 4) === 'http') {
            $finalImageUrl = $imageSrc;
        } else {
            // إزالة / من البداية إذا وجدت
            $imageSrc = ltrim($imageSrc, '/');
            $finalImageUrl = asset($imageSrc);
        }
    } else {
        $finalImageUrl = asset($imageFallback);
    }
    
    $fallbackImageUrl = asset($imageFallback);
@endphp

<img 
    src="{{ $finalImageUrl }}" 
    alt="{{ $imageAlt }}"
    @if($imageClass) class="{{ $imageClass }}" @endif
    @if($imageStyle) style="{{ $imageStyle }}" @endif
    onerror="this.src='{{ $fallbackImageUrl }}'; this.onerror=null;"
    loading="lazy"
>

<style>
.img-responsive {
    max-width: 100%;
    height: auto;
}

.logo-image {
    transition: all 0.3s ease;
}

.logo-image:hover {
    transform: scale(1.02);
}
</style>
