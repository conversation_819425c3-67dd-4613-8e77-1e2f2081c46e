# دليل حل المشاكل - أكاديمية Leaders Vision
## Troubleshooting Guide - Leaders Vision Academy

---

## 🚨 **المشاكل الشائعة وحلولها**

### **1. خطأ Illuminate\Foundation\Bootstrap\HandleExceptions::handleError**

#### **السبب:**
- متغير `$categorys` غير متوفر في ملفات العرض
- مشكلة في View Composer

#### **الحل:**
```bash
# 1. مسح الكاش
php artisan config:clear
php artisan cache:clear
php artisan view:clear

# 2. إعادة تشغيل قاعدة البيانات
php artisan migrate:fresh --seed
```

#### **الحل البديل:**
تم إضافة حماية في ملفات العرض:
```php
@if(isset($categorys) && $categorys->count() > 0)
    @foreach ($categorys as $category)
        // المحتوى
    @endforeach
@else
    <li><a href="#">لا توجد فئات متاحة</a></li>
@endif
```

---

### **2. خطأ Illuminate\Routing\UrlGenerator::route**

#### **السبب:**
- route غير موجود أو غير مسجل
- مشكلة في كاش الروابط

#### **الحل:**
```bash
# مسح كاش الروابط
php artisan route:clear

# عرض جميع الروابط المتاحة
php artisan route:list

# إعادة تحميل الروابط
php artisan route:cache
```

---

### **3. خطأ قاعدة البيانات**

#### **الأعراض:**
- "SQLSTATE[42S02]: Base table or view not found"
- "Database connection failed"

#### **الحل:**
```bash
# 1. تأكد من تشغيل MySQL في XAMPP
# 2. تحقق من إعدادات .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=academy
DB_USERNAME=root
DB_PASSWORD=

# 3. إنشاء قاعدة البيانات
# اذهب إلى phpMyAdmin وأنشئ قاعدة بيانات "academy"

# 4. تشغيل migrations
php artisan migrate:fresh --seed
```

---

### **4. مشكلة الصور والشعار**

#### **الأعراض:**
- الشعار لا يظهر
- الصور مكسورة

#### **الحل:**
```bash
# ربط مجلد التخزين
php artisan storage:link

# التأكد من وجود الملفات
# تحقق من وجود: public/images/logo.png
```

---

### **5. مشكلة الصلاحيات**

#### **الأعراض:**
- "Permission denied"
- لا يمكن الوصول لملفات معينة

#### **الحل في Windows:**
```bash
# تشغيل Command Prompt كمدير
# أو تغيير صلاحيات المجلدات يدوياً
```

#### **الحل في Linux/Mac:**
```bash
sudo chmod -R 775 storage bootstrap/cache
sudo chown -R www-data:www-data storage bootstrap/cache
```

---

### **6. مشكلة Composer**

#### **الأعراض:**
- "Class not found"
- "Autoload error"

#### **الحل:**
```bash
# تحديث autoload
composer dump-autoload

# إعادة تثبيت الحزم
composer install --no-dev --optimize-autoloader

# تحديث Composer
composer self-update
```

---

### **7. مشكلة مفتاح التطبيق**

#### **الأعراض:**
- "No application encryption key has been specified"

#### **الحل:**
```bash
php artisan key:generate
```

---

### **8. مشكلة تسجيل الدخول**

#### **الأعراض:**
- بيانات الدخول لا تعمل
- "These credentials do not match our records"

#### **الحل:**
```bash
# إنشاء مستخدم جديد
php artisan tinker

# في tinker:
$user = App\Models\User::create([
    'name' => 'Admin',
    'email' => '<EMAIL>',
    'password' => bcrypt('123456789')
]);

$user->attachRole('super_admin');
exit
```

---

### **9. مشكلة CSS/JS**

#### **الأعراض:**
- التصميم لا يظهر بشكل صحيح
- ملفات CSS/JS لا تحمل

#### **الحل:**
```bash
# تثبيت npm packages
npm install

# تجميع الملفات
npm run dev

# أو للإنتاج
npm run production
```

---

### **10. مشكلة البريد الإلكتروني**

#### **الأعراض:**
- رسائل البريد لا ترسل
- خطأ في إعدادات SMTP

#### **الحل:**
تحديث ملف `.env`:
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
```

---

## 🛠️ **أوامر مفيدة للصيانة**

### **مسح جميع أنواع الكاش:**
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear
php artisan queue:clear
```

### **إعادة تحميل التطبيق:**
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### **إعادة تشغيل قاعدة البيانات:**
```bash
php artisan migrate:fresh --seed
```

### **تحديث autoload:**
```bash
composer dump-autoload
```

### **عرض معلومات مفيدة:**
```bash
# عرض جميع الروابط
php artisan route:list

# عرض إعدادات التطبيق
php artisan config:show

# عرض حالة قاعدة البيانات
php artisan migrate:status
```

---

## 🔍 **تشخيص المشاكل**

### **1. فحص ملفات السجل:**
```
storage/logs/laravel.log
```

### **2. تفعيل وضع التطوير:**
في ملف `.env`:
```env
APP_DEBUG=true
APP_ENV=local
```

### **3. فحص متطلبات PHP:**
```bash
php -v
php -m | grep -i mysql
php -m | grep -i openssl
```

### **4. فحص Composer:**
```bash
composer --version
composer diagnose
```

---

## 📞 **الحصول على المساعدة**

### **خطوات التشخيص:**
1. **تحقق من ملف السجل**: `storage/logs/laravel.log`
2. **تأكد من تشغيل الخدمات**: Apache, MySQL
3. **تحقق من ملف .env**: إعدادات قاعدة البيانات
4. **جرب مسح الكاش**: الأوامر المذكورة أعلاه
5. **أعد تشغيل الخادم**: `php artisan serve`

### **معلومات مفيدة للدعم:**
- إصدار PHP: `php -v`
- إصدار Laravel: `php artisan --version`
- نظام التشغيل
- رسالة الخطأ الكاملة
- الخطوات التي أدت للخطأ

---

## ✅ **التحقق من سلامة النظام**

### **قائمة فحص سريعة:**
- [ ] Apache/Nginx يعمل
- [ ] MySQL يعمل  
- [ ] قاعدة البيانات "academy" موجودة
- [ ] ملف .env محدث بشكل صحيح
- [ ] composer install تم تشغيله
- [ ] php artisan migrate --seed تم تشغيله
- [ ] php artisan key:generate تم تشغيله
- [ ] php artisan storage:link تم تشغيله
- [ ] الصلاحيات صحيحة لمجلدات storage و bootstrap/cache

---

**إذا استمرت المشاكل، تأكد من اتباع جميع الخطوات في `SETUP_GUIDE.md` بالترتيب الصحيح.**
