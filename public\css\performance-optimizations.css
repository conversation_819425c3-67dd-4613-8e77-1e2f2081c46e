/* ===================================
   تحسينات الأداء لأكاديمية Leaders Vision
   Performance Optimizations for Leaders Vision Academy
   =================================== */

/* تحسين تحميل الخطوط */
@font-display: swap;

/* تحسين الصور */
img {
    max-width: 100%;
    height: auto;
    loading: lazy;
    decoding: async;
}

/* تحسين الفيديوهات */
video {
    max-width: 100%;
    height: auto;
    loading: lazy;
}

/* تحسين الحركات */
* {
    will-change: auto;
}

.smooth-transition {
    will-change: transform, opacity;
}

.hover-effect {
    will-change: transform, box-shadow;
}

/* تحسين التمرير */
.scroll-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* تحسين الطبقات */
.layer-optimization {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* تحسين الخطوط */
.text-rendering-optimization {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسين الألوان */
.color-optimization {
    color-rendering: optimizeQuality;
}

/* تحسين الشفافية */
.opacity-optimization {
    opacity: 0.99;
}

/* تحسين الحدود */
.border-optimization {
    border-radius: 0.1px;
}

/* تحسين الظلال */
.shadow-optimization {
    box-shadow: 0 0 0 1px transparent;
}

/* تحسين التحولات */
.transform-optimization {
    transform: translate3d(0, 0, 0);
}

/* تحسين الفلاتر */
.filter-optimization {
    filter: blur(0);
}

/* تحسين الخلفيات */
.background-optimization {
    background-attachment: scroll;
}

/* تحسين الجداول */
table {
    table-layout: fixed;
}

/* تحسين القوائم */
ul, ol {
    list-style-position: inside;
}

/* تحسين النماذج */
input, textarea, select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* تحسين الأزرار */
button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
}

/* تحسين الروابط */
a {
    -webkit-tap-highlight-color: transparent;
}

/* تحسين التحديد */
::selection {
    background-color: rgba(38, 199, 249, 0.3);
    color: inherit;
}

::-moz-selection {
    background-color: rgba(38, 199, 249, 0.3);
    color: inherit;
}

/* تحسين شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #26c7f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e9bc7;
}

/* تحسين الطباعة */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    a, a:visited {
        text-decoration: underline;
    }

    a[href]:after {
        content: " (" attr(href) ")";
    }

    abbr[title]:after {
        content: " (" attr(title) ")";
    }

    .no-print {
        display: none !important;
    }

    @page {
        margin: 0.5cm;
    }

    p, h2, h3 {
        orphans: 3;
        widows: 3;
    }

    h2, h3 {
        page-break-after: avoid;
    }
}

/* تحسين الشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .high-dpi-optimization {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* تحسين الشاشات الصغيرة */
@media (max-width: 768px) {
    .mobile-optimization {
        font-size: 16px; /* منع التكبير التلقائي في iOS */
    }

    input, textarea, select {
        font-size: 16px; /* منع التكبير التلقائي في iOS */
    }
}

/* تحسين الشاشات الكبيرة */
@media (min-width: 1200px) {
    .large-screen-optimization {
        max-width: 1200px;
        margin: 0 auto;
    }
}

/* تحسين الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .dark-mode-optimization {
        color-scheme: dark;
    }
}

/* تحسين تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
    .reduced-motion-optimization {
        animation: none !important;
        transition: none !important;
    }
}

/* تحسين التباين العالي */
@media (prefers-contrast: high) {
    .high-contrast-optimization {
        border: 2px solid;
    }
}

/* تحسين الشفافية المنخفضة */
@media (prefers-reduced-transparency: reduce) {
    .reduced-transparency-optimization {
        backdrop-filter: none;
        background: solid;
    }
}

/* تحسين الذاكرة */
.memory-optimization {
    contain: layout style paint;
}

/* تحسين الشبكة */
.network-optimization {
    content-visibility: auto;
    contain-intrinsic-size: 200px;
}

/* تحسين البطارية */
.battery-optimization {
    image-rendering: pixelated;
}

/* تحسين المعالج */
.cpu-optimization {
    transform-style: preserve-3d;
}

/* تحسين الرسوميات */
.gpu-optimization {
    transform: translateZ(0);
    will-change: transform;
}

/* تحسين الذاكرة المؤقتة */
.cache-optimization {
    cache-control: max-age=31536000;
}

/* تحسين الضغط */
.compression-optimization {
    content-encoding: gzip;
}

/* تحسين التحميل */
.loading-optimization {
    loading: lazy;
    decoding: async;
}

/* تحسين الأولوية */
.priority-optimization {
    fetchpriority: high;
}

/* تحسين الأمان */
.security-optimization {
    content-security-policy: default-src 'self';
}

/* تحسين الخصوصية */
.privacy-optimization {
    referrer-policy: strict-origin-when-cross-origin;
}

/* تحسين إمكانية الوصول */
.accessibility-optimization {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.accessibility-optimization:focus {
    outline-color: #26c7f9;
}

/* تحسين SEO */
.seo-optimization {
    semantic-markup: true;
    structured-data: true;
}

/* تحسين التحليلات */
.analytics-optimization {
    user-timing: true;
    performance-observer: true;
}

/* تحسين التتبع */
.tracking-optimization {
    intersection-observer: true;
    mutation-observer: true;
}

/* تحسين الأخطاء */
.error-optimization {
    error-boundary: true;
    fallback-ui: true;
}

/* تحسين التسجيل */
.logging-optimization {
    console-level: warn;
    remote-logging: true;
}

/* تحسين المراقبة */
.monitoring-optimization {
    performance-metrics: true;
    user-experience: true;
}
