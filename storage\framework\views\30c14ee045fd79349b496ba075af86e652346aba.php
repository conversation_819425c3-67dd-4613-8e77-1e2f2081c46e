<?php $__env->startSection('content'); ?>
<div dir="rtl" style="direction: rtl; text-align: right;">

	<!-- Hero Section with Course Banner -->
	<div class="course-hero-section" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); padding: 40px 0; position: relative; overflow: hidden;">
		<div class="hero-pattern" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0.1; background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.4"><circle cx="30" cy="30" r="4"/></g></svg>');"></div>
		<div class="container position-relative">
			<div class="row align-items-center">
				<div class="col-lg-8">
					<div class="hero-content text-white">
						<div class="course-badge mb-3">
							<span class="badge badge-light px-3 py-2" style="font-size: 14px; border-radius: 20px;">
								<i class="fa fa-graduation-cap ml-2"></i>
								دورة تدريبية متخصصة
							</span>
						</div>
						<h1 class="hero-title mb-3" style="font-size: 2rem; font-weight: 700; line-height: 1.2;">
							<?php echo e($details_course->name); ?>

						</h1>
						<p class="hero-subtitle mb-3" style="font-size: 1rem; opacity: 0.9; max-width: 600px;">
							<?php echo e($details_course->Short_description); ?>

						</p>
						<div class="course-meta d-flex flex-wrap gap-4">
							<div class="meta-item d-flex align-items-center">
								<i class="fa fa-star text-warning ml-2"></i>
								<span><?php echo e($details_course->rating); ?>/5</span>
							</div>
							<div class="meta-item d-flex align-items-center">
								<i class="fa fa-users ml-2"></i>
								<span><?php echo e($details_course->studant_count); ?> طالب</span>
							</div>
							<div class="meta-item d-flex align-items-center">
								<i class="fa fa-clock ml-2"></i>
								<span><?php echo e($details_course->time); ?> ساعة</span>
							</div>
						</div>
					</div>
				</div>
				<div class="col-lg-4 text-center">
					<div class="hero-price-card bg-white rounded-lg shadow-lg p-4" style="border-radius: 15px;">
						<?php if($details_course->show_price): ?>
						<div class="price-display mb-3">
							<span class="price-amount" style="font-size: 2.5rem; font-weight: 700; color: #e74c3c;">
								<?php echo e(number_format($details_course->price, 0)); ?>

							</span>
							<span class="price-currency" style="font-size: 1.2rem; color: #2c3e50; font-weight: 600;">دج</span>
						</div>
						<?php else: ?>
						<div class="price-contact mb-3">
							<div class="contact-for-price" style="font-size: 1.5rem; font-weight: 600; color: #e74c3c;">
								<i class="fa fa-phone ml-2"></i>
								اتصل للاستفسار
							</div>
							<div class="contact-numbers mt-2" style="font-size: 14px; color: #2c3e50;">
								<div><i class="fa fa-phone ml-1 text-success"></i> 0774479525</div>
								<div><i class="fa fa-phone ml-1 text-success"></i> 0665657400</div>
							</div>
						</div>
						<?php endif; ?>
						<a href="<?php echo e(route('purchase.create', $details_course->id)); ?>" class="btn btn-lg btn-block enrollment-btn-hero" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); border: none; border-radius: 10px; padding: 15px; font-weight: 600; color: white; text-decoration: none;">
							<i class="fa fa-shopping-cart ml-2"></i>
							<?php if($details_course->show_price): ?>
								اشترك الآن
							<?php else: ?>
								استفسر عن الدورة
							<?php endif; ?>
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
	  <!-- Course Content Section -->
	  <section class="course-content-section py-3" style="background: #ffffff;">
        <div class="container-fluid">
	        <div class="row">
	            <!-- Main Content -->
	            <div class="col-lg-9 mb-3">
	                <!-- Row 1: Description and Objectives -->
	                <div class="row mb-3">
	                    <!-- Course Description Card -->
	                    <div class="col-md-6">
	                        <div class="card shadow-sm h-100" style="border: 1px solid #e9ecef; border-radius: 15px;">
	                            <div class="card-header" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); border-bottom: none; border-radius: 15px 15px 0 0; color: white;">
	                                <h5 class="card-title mb-0" style="color: white; font-weight: 600; font-size: 16px;">
	                                    <i class="fa fa-info-circle ml-2"></i>
	                                    نظرة عامة على الدورة
	                                </h5>
	                            </div>
	                            <div class="card-body p-3" style="background: #ffffff;">
	                                <div class="course-description" style="line-height: 1.6; font-size: 14px; color: #2c3e50;">
	                                    <?php echo e(Str::limit($details_course->description, 200)); ?>

	                                </div>
	                            </div>
	                        </div>
	                    </div>

	                    <!-- Course Objectives Card -->
	                    <?php if($details_course->course_objectives): ?>
	                    <div class="col-md-6">
	                        <div class="card shadow-sm h-100" style="border: 1px solid #e9ecef; border-radius: 15px;">
	                            <div class="card-header" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%); border-bottom: none; border-radius: 15px 15px 0 0; color: white;">
	                                <h5 class="card-title mb-0" style="color: white; font-weight: 600; font-size: 16px;">
	                                    <i class="fa fa-target ml-2"></i>
	                                    أهداف الدورة
	                                </h5>
	                            </div>
	                            <div class="card-body p-3" style="background: #ffffff;">
	                                <div class="objectives-content" style="line-height: 1.6; font-size: 14px; color: #2c3e50;">
	                                    <?php echo nl2br(e(Str::limit($details_course->course_objectives, 200))); ?>

	                                </div>
	                            </div>
	                        </div>
	                    </div>
	                    <?php endif; ?>
	                </div>

	                <!-- Course Objectives Card -->
	                <?php if($details_course->course_objectives): ?>
	                <div class="card shadow-sm mb-4" style="border: 1px solid #e9ecef; border-radius: 15px;">
	                    <div class="card-header" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%); border-bottom: none; border-radius: 15px 15px 0 0; color: white;">
	                        <h3 class="card-title mb-0" style="color: white; font-weight: 600;">
	                            <i class="fa fa-target ml-2"></i>
	                            أهداف الدورة
	                        </h3>
	                    </div>
	                    <div class="card-body p-4" style="background: #ffffff;">
	                        <div class="objectives-content">
	                            <?php
	                                $objectives = explode("\n", $details_course->course_objectives);
	                                $objectives = array_filter($objectives, function($obj) {
	                                    return !empty(trim($obj));
	                                });
	                            ?>

	                            <div class="row">
	                                <?php $__currentLoopData = $objectives; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $objective): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
	                                    <?php if(!empty(trim($objective))): ?>
	                                    <div class="col-md-6 mb-3">
	                                        <div class="objective-item d-flex align-items-start p-3" style="background: #f8f9fa; border-radius: 10px; border-right: 4px solid #27ae60;">
	                                            <div class="objective-icon ml-3 mt-1">
	                                                <i class="fa fa-check-circle text-success" style="font-size: 18px;"></i>
	                                            </div>
	                                            <div class="objective-text flex-grow-1">
	                                                <p class="mb-0" style="color: #2c3e50; font-weight: 500; line-height: 1.6;">
	                                                    <?php echo e(trim(str_replace(['✅', '✓', '-'], '', $objective))); ?>

	                                                </p>
	                                            </div>
	                                        </div>
	                                    </div>
	                                    <?php endif; ?>
	                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
	                            </div>
	                        </div>
	                    </div>
	                </div>
	                <?php endif; ?>

	                <!-- Target Audience Card -->
	                <?php if($details_course->target_audience): ?>
	                <div class="card shadow-sm mb-4" style="border: 1px solid #e9ecef; border-radius: 15px;">
	                    <div class="card-header" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); border-bottom: none; border-radius: 15px 15px 0 0; color: white;">
	                        <h3 class="card-title mb-0" style="color: white; font-weight: 600;">
	                            <i class="fa fa-users ml-2"></i>
	                            الفئة المستهدفة
	                        </h3>
	                    </div>
	                    <div class="card-body p-4" style="background: #ffffff;">
	                        <div class="target-audience-content">
	                            <?php
	                                $audiences = explode("\n", $details_course->target_audience);
	                                $audiences = array_filter($audiences, function($aud) {
	                                    return !empty(trim($aud));
	                                });
	                            ?>

	                            <div class="row">
	                                <?php $__currentLoopData = $audiences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $audience): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
	                                    <?php if(!empty(trim($audience))): ?>
	                                    <div class="col-md-6 mb-3">
	                                        <div class="audience-item d-flex align-items-start p-3" style="background: #fff3e0; border-radius: 10px; border-right: 4px solid #f39c12;">
	                                            <div class="audience-icon ml-3 mt-1">
	                                                <i class="fa fa-user text-warning" style="font-size: 18px;"></i>
	                                            </div>
	                                            <div class="audience-text flex-grow-1">
	                                                <p class="mb-0" style="color: #2c3e50; font-weight: 500; line-height: 1.6;">
	                                                    <?php echo e(trim(str_replace(['✔️', '✓', '-'], '', $audience))); ?>

	                                                </p>
	                                            </div>
	                                        </div>
	                                    </div>
	                                    <?php endif; ?>
	                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
	                            </div>
	                        </div>
	                    </div>
	                </div>
	                <?php endif; ?>

	                <!-- Course Instructors Card -->
	                <?php if($instructors && $instructors->count() > 0): ?>
	                <div class="card shadow-sm mb-4" style="border: 1px solid #e9ecef; border-radius: 15px;">
	                    <div class="card-header" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); border-bottom: none; border-radius: 15px 15px 0 0; color: white;">
	                        <h3 class="card-title mb-0" style="color: white; font-weight: 600;">
	                            <i class="fa fa-graduation-cap ml-2"></i>
	                            مدربي الدورة
	                        </h3>
	                    </div>
	                    <div class="card-body p-4" style="background: #ffffff;">
	                        <div class="row">
	                            <?php $__currentLoopData = $instructors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $instructor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
	                            <div class="col-md-6 mb-4">
	                                <div class="instructor-card p-3" style="border: 1px solid #e9ecef; border-radius: 10px; transition: all 0.3s ease;">
	                                    <div class="instructor-info d-flex align-items-center">
	                                        <div class="instructor-avatar ml-3">
	                                            <?php if($instructor->image): ?>
	                                                <img src="<?php echo e(asset('uploads/user_images/' . $instructor->image)); ?>"
	                                                     alt="<?php echo e($instructor->name); ?>"
	                                                     class="rounded-circle"
	                                                     style="width: 60px; height: 60px; object-fit: cover;">
	                                            <?php else: ?>
	                                                <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center"
	                                                     style="width: 60px; height: 60px; background: linear-gradient(135deg, #6f42c1 0%, #8e44ad 100%); color: white; font-weight: 600; font-size: 20px;">
	                                                    <?php echo e(substr($instructor->name ?? 'مدرب', 0, 1)); ?>

	                                                </div>
	                                            <?php endif; ?>
	                                        </div>
	                                        <div class="instructor-details flex-grow-1">
	                                            <h5 class="instructor-name mb-1" style="color: #2c3e50; font-weight: 600;">
	                                                <?php echo e($instructor->name ?? 'مدرب'); ?>

	                                            </h5>
	                                            <p class="instructor-job mb-1" style="color: #7f8c8d; font-size: 14px;">
	                                                <?php echo e($instructor->jobs ?? 'مدرب'); ?>

	                                            </p>
	                                            <?php if($instructor->rating): ?>
	                                            <div class="instructor-rating">
	                                                <?php for($i = 1; $i <= 5; $i++): ?>
	                                                    <?php if($i <= $instructor->rating): ?>
	                                                        <i class="fa fa-star text-warning" style="font-size: 12px;"></i>
	                                                    <?php else: ?>
	                                                        <i class="fa fa-star-o text-muted" style="font-size: 12px;"></i>
	                                                    <?php endif; ?>
	                                                <?php endfor; ?>
	                                                <span class="rating-text" style="font-size: 12px; color: #666;">
	                                                    (<?php echo e($instructor->rating); ?>/5)
	                                                </span>
	                                            </div>
	                                            <?php endif; ?>
	                                        </div>
	                                    </div>
	                                    <?php if($instructor->description): ?>
	                                    <div class="instructor-description mt-3 pt-3" style="border-top: 1px solid #f8f9fa;">
	                                        <p class="mb-0" style="font-size: 14px; color: #666; line-height: 1.6;">
	                                            <?php echo e(Str::limit($instructor->description, 100)); ?>

	                                        </p>
	                                    </div>
	                                    <?php endif; ?>
	                                </div>
	                            </div>
	                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
	                        </div>
	                    </div>
	                </div>
	                <?php endif; ?>
	            </div>

	            <!-- Sidebar -->
	            <div class="col-lg-3">
	                <!-- Video Preview Card -->
	                <?php if($details_course->demo_video): ?>
	                <div class="card shadow-sm mb-3" style="border: 1px solid #e9ecef; border-radius: 15px;">
	                    <div class="card-header" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); border-bottom: none; border-radius: 15px 15px 0 0; color: white;">
	                        <h6 class="card-title mb-0" style="color: white; font-weight: 600; font-size: 14px;">
	                            <i class="fa fa-play-circle ml-2"></i>
	                            معاينة الدورة
	                        </h6>
	                    </div>
	                    <div class="card-body p-0">
	                        <div class="video-container" style="position: relative; border-radius: 0 0 15px 15px; overflow: hidden;">
	                            <video
	                                controls
	                                poster="<?php echo e($details_course->image_path); ?>"
	                                style="width: 100%; height: 180px; object-fit: cover;"
	                                class="course-video">
	                                <source src="<?php echo e(asset('uploads/'.$details_course->demo_video)); ?>" type="video/mp4">
	                                <source src="<?php echo e(asset('uploads/'.$details_course->demo_video)); ?>" type="video/webm">
	                                متصفحك لا يدعم تشغيل الفيديو
	                            </video>
	                        </div>
	                    </div>
	                </div>
	                <?php endif; ?>

	                <!-- Course Info Card -->
	                <div class="card shadow-sm mb-3" style="border: 1px solid #e9ecef; border-radius: 15px;">
	                    <div class="card-header" style="background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); border-bottom: none; border-radius: 15px 15px 0 0; color: white;">
	                        <h6 class="card-title mb-0" style="color: white; font-weight: 600; font-size: 14px;">
	                            <i class="fa fa-info-circle ml-2"></i>
	                            معلومات الدورة
	                        </h6>
	                    </div>
	                    <div class="card-body p-3" style="background: #ffffff;">
	                        <div class="course-info-list">
	                            <?php if($details_course->show_price): ?>
	                            <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
	                                <span class="info-label" style="font-weight: 600; color: #2c3e50;">
	                                    <i class="fa fa-tag ml-2 text-danger"></i>السعر:
	                                </span>
	                                <span class="info-value" style="font-weight: 700; color: #e74c3c; font-size: 18px;">
	                                    <?php echo e(number_format($details_course->price, 0)); ?> دج
	                                </span>
	                            </div>
	                            <?php else: ?>
	                            <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
	                                <span class="info-label" style="font-weight: 600; color: #2c3e50;">
	                                    <i class="fa fa-phone ml-2 text-success"></i>السعر:
	                                </span>
	                                <span class="info-value" style="font-weight: 600; color: #e74c3c; font-size: 14px;">
	                                    اتصل للاستفسار
	                                </span>
	                            </div>
	                            <?php endif; ?>
	                            <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
	                                <span class="info-label" style="font-weight: 600; color: #2c3e50;">
	                                    <i class="fa fa-clock ml-2 text-success"></i>المدة:
	                                </span>
	                                <span class="info-value" style="font-weight: 600; color: #2c3e50;">
	                                    <?php echo e($details_course->time); ?> ساعة
	                                </span>
	                            </div>
	                            <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
	                                <span class="info-label" style="font-weight: 600; color: #2c3e50;">
	                                    <i class="fa fa-users ml-2 text-info"></i>عدد الطلاب:
	                                </span>
	                                <span class="info-value" style="font-weight: 600; color: #2c3e50;">
	                                    <?php echo e($details_course->studant_count); ?> طالب
	                                </span>
	                            </div>
	                            <div class="info-item d-flex justify-content-between align-items-center mb-3">
	                                <span class="info-label" style="font-weight: 600; color: #2c3e50;">
	                                    <i class="fa fa-star ml-2 text-warning"></i>التقييم:
	                                </span>
	                                <div class="rating-display">
	                                    <?php for($i = 1; $i <= 5; $i++): ?>
	                                        <?php if($i <= $details_course->rating): ?>
	                                            <i class="fa fa-star text-warning"></i>
	                                        <?php else: ?>
	                                            <i class="fa fa-star-o text-muted"></i>
	                                        <?php endif; ?>
	                                    <?php endfor; ?>
	                                    <span class="rating-text mr-2" style="font-weight: 600; color: #2c3e50;">
	                                        (<?php echo e($details_course->rating); ?>/5)
	                                    </span>
	                                </div>
	                            </div>
	                        </div>

	                        <div class="enrollment-section mt-4 pt-4" style="border-top: 2px solid #f8f9fa;">
	                            <a href="<?php echo e(route('purchase.create', $details_course->id)); ?>"
	                               class="btn btn-lg btn-block enrollment-btn-sidebar"
	                               style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
	                                      border: none;
	                                      border-radius: 10px;
	                                      padding: 15px;
	                                      font-weight: 600;
	                                      color: white;
	                                      text-decoration: none;
	                                      transition: all 0.3s ease;">
	                                <i class="fa fa-shopping-cart ml-2"></i>
	                                <?php if($details_course->show_price): ?>
	                                    اشترك الآن
	                                <?php else: ?>
	                                    استفسر عن الدورة
	                                <?php endif; ?>
	                            </a>
	                        </div>
	                    </div>
	                </div>

	                <!-- Contact Card -->
	                <div class="card shadow-sm" style="border: 1px solid #e9ecef; border-radius: 15px;">
	                    <div class="card-header" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); border-bottom: none; border-radius: 15px 15px 0 0; color: white;">
	                        <h6 class="card-title mb-0" style="color: white; font-weight: 600; font-size: 14px;">
	                            <i class="fa fa-phone ml-2"></i>
	                            تحتاج مساعدة؟
	                        </h6>
	                    </div>
	                    <div class="card-body p-3 text-center" style="background: #ffffff;">
	                        <p class="mb-3" style="color: #2c3e50; font-weight: 500;">تواصل معنا للحصول على استشارة مجانية</p>
	                        <div class="contact-info">
	                            <div class="contact-item mb-3 p-2" style="background: #f8f9fa; border-radius: 8px; border-left: 4px solid #27ae60;">
	                                <i class="fa fa-phone text-success ml-2"></i>
	                                <span style="color: #2c3e50; font-weight: 600;">0774479525</span>
	                            </div>
	                            <div class="contact-item mb-3 p-2" style="background: #f8f9fa; border-radius: 8px; border-left: 4px solid #27ae60;">
	                                <i class="fa fa-phone text-success ml-2"></i>
	                                <span style="color: #2c3e50; font-weight: 600;">0665657400</span>
	                            </div>
	                            <div class="contact-item p-2" style="background: #f8f9fa; border-radius: 8px; border-left: 4px solid #e74c3c;">
	                                <i class="fa fa-map-marker text-danger ml-2"></i>
	                                <span style="color: #2c3e50; font-weight: 600;">برج بوعريريج، الجزائر</span>
	                            </div>
	                        </div>
	                    </div>
	                </div>
	            </div>
	        </div>
	    </div>
	</section>

	<!-- End intro Courses -->
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Hero Section Enhancements */
.course-hero-section {
    position: relative;
    background-attachment: fixed;
}

.hero-title {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    animation: fadeInUp 1s ease-out 0.2s both;
}

.course-meta {
    animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-price-card {
    animation: fadeInUp 1s ease-out 0.6s both;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.hero-price-card:hover {
    transform: translateY(-5px);
}

/* Card Enhancements */
.card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05) !important;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.card:hover .card-header::before {
    left: 100%;
}

/* Video Enhancements */
.course-video {
    transition: all 0.3s ease;
    border-radius: 0 0 15px 15px;
}

.course-video:hover {
    transform: scale(1.02);
}

/* Button Enhancements */
.enrollment-btn {
    position: relative;
    overflow: hidden;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.enrollment-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.enrollment-btn:hover::before {
    left: 100%;
}

.enrollment-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    color: white !important;
}

/* Info Items Animation */
.info-item {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 8px;
    margin: -8px;
}

.info-item:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

/* Rating Stars Animation */
.rating-display i {
    transition: all 0.2s ease;
}

.rating-display:hover i {
    transform: scale(1.2);
}

/* Contact Items */
.contact-item {
    transition: all 0.3s ease;
    border-radius: 8px;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Contact Card Enhancement */
.contact-info .contact-item {
    background: #f8f9fa !important;
    border-radius: 8px !important;
    padding: 8px !important;
    margin-bottom: 12px !important;
}

.contact-info .contact-item span {
    color: #2c3e50 !important;
    font-weight: 600 !important;
}

/* Feature Items */
.feature-item {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 8px;
    margin: -8px;
}

.feature-item:hover {
    background: rgba(40, 167, 69, 0.1);
    transform: translateX(5px);
}

/* Animations */
@keyframes  fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes  pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem !important;
    }

    .hero-subtitle {
        font-size: 1rem !important;
    }

    .course-meta {
        flex-direction: column;
        gap: 10px !important;
    }

    .meta-item {
        justify-content: center;
    }

    .hero-price-card {
        margin-top: 30px;
    }
}

/* Price Display Enhancement */
.price-amount {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s infinite;
}

/* Course Badge */
.course-badge .badge {
    animation: fadeInDown 1s ease-out;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation for Video */
.video-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-container.loading::before {
    opacity: 1;
}

@keyframes  spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Instructor Cards */
.instructor-card {
    transition: all 0.3s ease;
    background: #fff;
}

.instructor-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(111, 66, 193, 0.15);
    border-color: #6f42c1 !important;
}

.instructor-avatar img,
.avatar-placeholder {
    transition: all 0.3s ease;
}

.instructor-card:hover .instructor-avatar img,
.instructor-card:hover .avatar-placeholder {
    transform: scale(1.1);
}

.instructor-name {
    transition: color 0.3s ease;
}

.instructor-card:hover .instructor-name {
    color: #8e44ad !important;
}

/* Objectives and Target Audience */
.objectives-content,
.target-audience-content {
    color: #555;
}

.objectives-content i,
.target-audience-content i {
    color: #28a745;
    margin-left: 8px;
}

/* Contact for Price Animation */
.contact-for-price {
    animation: pulse 2s infinite;
}

.contact-numbers div {
    transition: all 0.3s ease;
    padding: 2px 0;
}

.contact-numbers div:hover {
    color: #e74c3c !important;
    transform: translateX(5px);
}

/* Enhanced Button Styles */
.enrollment-btn-hero {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.enrollment-btn-hero:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%) !important;
}

.enrollment-btn-sidebar {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.enrollment-btn-sidebar:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%) !important;
    color: white !important;
    text-decoration: none !important;
}

/* Card Headers */
.card-header {
    border-bottom: none !important;
}

/* Text Colors */
.course-description,
.objectives-content,
.target-audience-content {
    color: #2c3e50 !important;
}

/* Info Items */
.info-item .info-label {
    color: #2c3e50 !important;
}

.info-item .info-value {
    color: #2c3e50 !important;
}

/* Hero Section Text */
.course-hero-section h1 {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.course-hero-section p {
    color: rgba(255,255,255,0.9) !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Price Display */
.price-amount {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* Instructor Cards Enhancement */
.instructor-card {
    background: #ffffff !important;
    border: 1px solid #e9ecef !important;
}

.instructor-card:hover {
    border-color: #9b59b6 !important;
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.15) !important;
}

/* Objectives and Target Audience Items */
.objective-item {
    transition: all 0.3s ease;
    cursor: default;
}

.objective-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.15);
    background: #e8f5e8 !important;
}

.audience-item {
    transition: all 0.3s ease;
    cursor: default;
}

.audience-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.15);
    background: #fef9e7 !important;
}

.objective-icon i,
.audience-icon i {
    transition: all 0.3s ease;
}

.objective-item:hover .objective-icon i {
    transform: scale(1.2);
    color: #27ae60 !important;
}

.audience-item:hover .audience-icon i {
    transform: scale(1.2);
    color: #f39c12 !important;
}

/* Responsive adjustments for objectives and audience */
@media (max-width: 768px) {
    .objective-item,
    .audience-item {
        margin-bottom: 15px;
    }

    .col-md-6 {
        padding-left: 10px;
        padding-right: 10px;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Smooth scroll for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Video loading animation
    $('.course-video').on('loadstart', function() {
        $(this).closest('.video-container').addClass('loading');
    });

    $('.course-video').on('loadeddata', function() {
        $(this).closest('.video-container').removeClass('loading');
    });

    // Parallax effect for hero section
    $(window).scroll(function() {
        var scrolled = $(this).scrollTop();
        var parallax = $('.course-hero-section');
        var speed = 0.5;

        parallax.css('transform', 'translateY(' + (scrolled * speed) + 'px)');
    });

    // Counter animation for stats
    $('.price-amount, .info-value').each(function() {
        var $this = $(this);
        var countTo = $this.text().replace(/[^0-9]/g, '');

        if (countTo && !isNaN(countTo)) {
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    var formattedNumber = Math.floor(this.countNum).toLocaleString();
                    if ($this.hasClass('price-amount')) {
                        $this.text(formattedNumber);
                    }
                },
                complete: function() {
                    var formattedNumber = parseInt(countTo).toLocaleString();
                    if ($this.hasClass('price-amount')) {
                        $this.text(formattedNumber);
                    }
                }
            });
        }
    });

    // Intersection Observer for animations
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        });

        document.querySelectorAll('.card').forEach(card => {
            observer.observe(card);
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.home.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Academy\resources\views/home/<USER>/ ?>