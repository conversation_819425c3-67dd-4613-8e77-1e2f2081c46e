<?php $__env->startSection('content'); ?>
<div dir="rtl" style="direction: rtl; text-align: right; margin-top: 80px;">

    <!-- Course Header Section -->
    <section class="course-header-section" style="background: #f8f9fa; padding: 30px 0;">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="mb-3" dir="rtl">
                        <ol class="breadcrumb" style="background: transparent; padding: 0; direction: rtl;">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" style="color: #6c757d;">الرئيسية</a></li>
                            <li class="breadcrumb-item"><a href="#" style="color: #6c757d;">الدورات</a></li>
                            <li class="breadcrumb-item active" aria-current="page" style="color: #495057;"><?php echo e($details_course->name); ?></li>
                        </ol>
                    </nav>

                    <!-- Course Title -->
                    <h1 class="course-title mb-3" style="font-size: 2.2rem; font-weight: 700; color: #2c3e50; line-height: 1.2; text-align: right;">
                        <?php echo e($details_course->name); ?>

                    </h1>

                    <!-- Course Subtitle -->
                    <p class="course-subtitle mb-3" style="font-size: 1.1rem; color: #6c757d; line-height: 1.6; text-align: right;">
                        <?php echo e($details_course->Short_description); ?>

                    </p>

                    <!-- Course Meta Info -->
                    <div class="course-meta-info d-flex flex-wrap align-items-center mb-3" style="direction: rtl;">
                        <!-- Rating -->
                        <div class="meta-item d-flex align-items-center mr-4 mb-2">
                            <span class="rating-text mr-2" style="color: #495057; font-weight: 600;">
                                <?php echo e($details_course->rating); ?>/5 (<?php echo e($details_course->studant_count ?? '0'); ?> تقييم)
                            </span>
                            <div class="rating-stars">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <?php if($i <= $details_course->rating): ?>
                                        <i class="fa fa-star text-warning"></i>
                                    <?php else: ?>
                                        <i class="fa fa-star-o text-muted"></i>
                                    <?php endif; ?>
                                <?php endfor; ?>
                            </div>
                        </div>

                        <!-- Students Count -->
                        <div class="meta-item d-flex align-items-center mr-4 mb-2">
                            <span style="color: #495057; font-weight: 600;"><?php echo e($details_course->studant_count ?? '0'); ?> طالب</span>
                            <i class="fa fa-users text-primary mr-2"></i>
                        </div>

                        <!-- Duration -->
                        <div class="meta-item d-flex align-items-center mr-4 mb-2">
                            <span style="color: #495057; font-weight: 600;"><?php echo e($details_course->time); ?> ساعة</span>
                            <i class="fa fa-clock text-success mr-2"></i>
                        </div>

                        <!-- Category -->
                        <div class="meta-item d-flex align-items-center mb-2">
                            <span style="color: #495057; font-weight: 600;"><?php echo e($details_course->category->name ?? 'غير محدد'); ?></span>
                            <i class="fa fa-tag text-info mr-2"></i>
                        </div>
                    </div>

                    <!-- Instructors -->
                    <?php if($instructors && $instructors->count() > 0): ?>
                    <div class="course-instructors mb-3" style="direction: rtl;">
                        <h6 class="mb-2" style="color: #495057; font-weight: 600; text-align: right;">المدربون:</h6>
                        <div class="instructors-list d-flex flex-wrap" style="direction: rtl;">
                            <?php $__currentLoopData = $instructors->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $instructor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="instructor-item d-flex align-items-center mr-4 mb-2">
                                <div class="instructor-info mr-2">
                                    <div class="instructor-name" style="font-weight: 600; color: #495057; font-size: 14px; text-align: right;">
                                        <?php echo e($instructor->name ?? 'مدرب'); ?>

                                    </div>
                                    <div class="instructor-title" style="font-size: 12px; color: #6c757d; text-align: right;">
                                        <?php echo e($instructor->jobs ?? 'مدرب'); ?>

                                    </div>
                                </div>
                                <div class="instructor-avatar">
                                    <?php if($instructor->image): ?>
                                        <img src="<?php echo e(asset('uploads/user_images/' . $instructor->image)); ?>"
                                             alt="<?php echo e($instructor->name); ?>"
                                             class="rounded-circle"
                                             style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px; background: #007bff; color: white; font-weight: 600; font-size: 14px;">
                                            <?php echo e(substr($instructor->name ?? 'مدرب', 0, 1)); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($instructors->count() > 3): ?>
                            <span class="more-instructors" style="color: #6c757d; font-size: 14px;">
                                و <?php echo e($instructors->count() - 3); ?> مدربين آخرين
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="col-lg-4">
                    <!-- Course Preview Card -->
                    <div class="course-preview-card" style="position: sticky; top: 100px;">
                        <div class="card shadow-lg" style="border: none; border-radius: 15px; overflow: hidden;">
                            <!-- Course Image/Video -->
                            <div class="course-media" style="position: relative;">
                                <img src="<?php echo e($details_course->image_path); ?>"
                                     alt="<?php echo e($details_course->name); ?>"
                                     style="width: 100%; height: 250px; object-fit: cover;">

                                <?php if($details_course->demo_video): ?>
                                <div class="video-overlay d-flex align-items-center justify-content-center"
                                     style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.3); cursor: pointer;"
                                     onclick="showVideoModal()">
                                    <div class="play-button"
                                         style="width: 70px; height: 70px; background: rgba(255,255,255,0.9); border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
                                        <i class="fa fa-play" style="font-size: 24px; color: #007bff; margin-right: -3px;"></i>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>

                            <!-- Course Price and Enrollment -->
                            <div class="card-body p-4">
                                <!-- Price -->
                                <div class="course-price mb-4 text-center">
                                    <?php if($details_course->show_price): ?>
                                    <div class="price-display">
                                        <span class="current-price" style="font-size: 2.5rem; font-weight: 700; color: #007bff;">
                                            <?php echo e(number_format($details_course->price, 0)); ?>

                                        </span>
                                        <span class="currency" style="font-size: 1.2rem; color: #6c757d; font-weight: 600;">دج</span>
                                    </div>
                                    <?php else: ?>
                                    <div class="contact-price">
                                        <div class="price-text" style="font-size: 1.5rem; font-weight: 600; color: #007bff;">
                                            <i class="fa fa-phone ml-2"></i>
                                            اتصل للاستفسار عن السعر
                                        </div>
                                        <div class="contact-numbers mt-2" style="font-size: 14px; color: #6c757d;">
                                            <div><i class="fa fa-phone ml-1 text-success"></i> 0774479525</div>
                                            <div><i class="fa fa-phone ml-1 text-success"></i> 0665657400</div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Enrollment Button -->
                                <div class="enrollment-button mb-4">
                                    <a href="<?php echo e(route('purchase.create', $details_course->id)); ?>"
                                       class="btn btn-primary btn-lg btn-block"
                                       style="border-radius: 8px; font-weight: 600; padding: 15px; font-size: 16px;">
                                        <i class="fa fa-shopping-cart ml-2"></i>
                                        <?php if($details_course->show_price): ?>
                                            اشترك في الدورة
                                        <?php else: ?>
                                            استفسر عن الدورة
                                        <?php endif; ?>
                                    </a>
                                </div>

                                <!-- Course Includes -->
                                <div class="course-includes" style="direction: rtl;">
                                    <h6 class="mb-3" style="color: #495057; font-weight: 600; text-align: right;">تتضمن هذه الدورة:</h6>
                                    <ul class="list-unstyled" style="direction: rtl;">
                                        <li class="d-flex align-items-center mb-2" style="direction: rtl;">
                                            <span style="color: #6c757d;"><?php echo e($details_course->time); ?> ساعة من المحتوى</span>
                                            <i class="fa fa-clock text-primary mr-2" style="width: 16px;"></i>
                                        </li>
                                        <li class="d-flex align-items-center mb-2" style="direction: rtl;">
                                            <span style="color: #6c757d;"><?php echo e($details_course->studant_count ?? '0'); ?> طالب مسجل</span>
                                            <i class="fa fa-users text-primary mr-2" style="width: 16px;"></i>
                                        </li>
                                        <li class="d-flex align-items-center mb-2" style="direction: rtl;">
                                            <span style="color: #6c757d;">شهادة إتمام</span>
                                            <i class="fa fa-certificate text-primary mr-2" style="width: 16px;"></i>
                                        </li>
                                        <li class="d-flex align-items-center mb-2" style="direction: rtl;">
                                            <span style="color: #6c757d;">الوصول عبر الهاتف والحاسوب</span>
                                            <i class="fa fa-mobile text-primary mr-2" style="width: 16px;"></i>
                                        </li>
                                        <li class="d-flex align-items-center mb-2" style="direction: rtl;">
                                            <span style="color: #6c757d;">وصول مدى الحياة</span>
                                            <i class="fa fa-infinity text-primary mr-2" style="width: 16px;"></i>
                                        </li>
                                        <?php if($details_course->demo_video): ?>
                                        <li class="d-flex align-items-center mb-2" style="direction: rtl;">
                                            <span style="color: #6c757d;">فيديو تعريفي</span>
                                            <i class="fa fa-play-circle text-primary mr-2" style="width: 16px;"></i>
                                        </li>
                                        <?php endif; ?>
                                    </ul>
                                </div>

                                <!-- Course Details -->
                                <div class="course-details mt-4 pt-4" style="border-top: 1px solid #e9ecef; direction: rtl;">
                                    <h6 class="mb-3" style="color: #495057; font-weight: 600; text-align: right;">تفاصيل الدورة:</h6>
                                    <div class="details-list">
                                        <div class="detail-item d-flex justify-content-between mb-2" style="direction: rtl;">
                                            <span style="color: #495057; font-weight: 600;">#<?php echo e($details_course->id); ?></span>
                                            <span style="color: #6c757d;">رقم الدورة:</span>
                                        </div>
                                        <div class="detail-item d-flex justify-content-between mb-2" style="direction: rtl;">
                                            <span style="color: #495057; font-weight: 600;"><?php echo e($details_course->rating); ?>/5</span>
                                            <span style="color: #6c757d;">التقييم:</span>
                                        </div>
                                        <div class="detail-item d-flex justify-content-between mb-2" style="direction: rtl;">
                                            <span style="color: #495057; font-weight: 600;"><?php echo e($details_course->category->name ?? 'غير محدد'); ?></span>
                                            <span style="color: #6c757d;">القسم:</span>
                                        </div>
                                        <div class="detail-item d-flex justify-content-between mb-2" style="direction: rtl;">
                                            <span style="color: #495057; font-weight: 600;"><?php echo e($details_course->created_at->format('Y/m/d')); ?></span>
                                            <span style="color: #6c757d;">تاريخ الإنشاء:</span>
                                        </div>
                                        <div class="detail-item d-flex justify-content-between mb-2" style="direction: rtl;">
                                            <span style="color: #495057; font-weight: 600;"><?php echo e($details_course->updated_at->format('Y/m/d')); ?></span>
                                            <span style="color: #6c757d;">آخر تحديث:</span>
                                        </div>
                                        <?php if($details_course->url): ?>
                                        <div class="detail-item d-flex justify-content-between mb-2" style="direction: rtl;">
                                            <a href="<?php echo e($details_course->url); ?>" target="_blank" style="color: #007bff; font-weight: 600; text-decoration: none;">عرض</a>
                                            <span style="color: #6c757d;">الرابط:</span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Course Content Section -->
    <section class="course-content-section" style="background: white; padding: 30px 0;">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <!-- Course Content Tabs -->
                    <div class="course-content-tabs" dir="rtl">
                        <!-- Tab Navigation -->
                        <ul class="nav nav-tabs" id="courseContentTabs" role="tablist" style="border-bottom: 2px solid #e9ecef; direction: rtl;">
                            <li class="nav-item" role="presentation">
                                <a class="nav-link active" id="overview-tab" data-toggle="tab" href="#overview" role="tab" aria-controls="overview" aria-selected="true"
                                   style="color: #495057; font-weight: 600; padding: 15px 20px; border: none; border-bottom: 3px solid transparent; text-align: right;">
                                    نظرة عامة<i class="fa fa-info-circle mr-2"></i>
                                </a>
                            </li>
                            <?php if($details_course->course_objectives): ?>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="objectives-tab" data-toggle="tab" href="#objectives" role="tab" aria-controls="objectives" aria-selected="false"
                                   style="color: #495057; font-weight: 600; padding: 15px 20px; border: none; border-bottom: 3px solid transparent; text-align: right;">
                                    الأهداف<i class="fa fa-target mr-2"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                            <?php if($details_course->target_audience): ?>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="audience-tab" data-toggle="tab" href="#audience" role="tab" aria-controls="audience" aria-selected="false"
                                   style="color: #495057; font-weight: 600; padding: 15px 20px; border: none; border-bottom: 3px solid transparent; text-align: right;">
                                    الفئة المستهدفة<i class="fa fa-users mr-2"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                            <?php if($instructors && $instructors->count() > 0): ?>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="instructors-tab" data-toggle="tab" href="#instructors" role="tab" aria-controls="instructors" aria-selected="false"
                                   style="color: #495057; font-weight: 600; padding: 15px 20px; border: none; border-bottom: 3px solid transparent; text-align: right;">
                                    المدربون<i class="fa fa-graduation-cap mr-2"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="courseContentTabsContent" style="padding: 30px 0;" dir="rtl">
                            <!-- Overview Tab -->
                            <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                                <div class="overview-content" style="text-align: right;">
                                    <h3 class="mb-4" style="color: #2c3e50; font-weight: 700; text-align: right;">وصف الدورة</h3>
                                    <div class="description-content" style="line-height: 1.8; color: #495057; font-size: 16px; text-align: right;">
                                        <?php echo nl2br(e($details_course->description)); ?>

                                    </div>

                                    <?php if($details_course->Short_description && $details_course->Short_description != $details_course->description): ?>
                                    <div class="short-description mt-4 p-4" style="background: #f8f9fa; border-radius: 10px; border-right: 4px solid #007bff; text-align: right;">
                                        <h5 class="mb-3" style="color: #2c3e50; font-weight: 600; text-align: right;">ملخص الدورة:</h5>
                                        <p class="mb-0" style="line-height: 1.6; color: #495057; text-align: right;">
                                            <?php echo e($details_course->Short_description); ?>

                                        </p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Objectives Tab -->
                            <?php if($details_course->course_objectives): ?>
                            <div class="tab-pane fade" id="objectives" role="tabpanel" aria-labelledby="objectives-tab">
                                <div class="objectives-content" style="text-align: right;">
                                    <h3 class="mb-4" style="color: #2c3e50; font-weight: 700; text-align: right;">أهداف الدورة</h3>
                                    <div class="objectives-list" style="line-height: 1.8; color: #495057; font-size: 16px; text-align: right;">
                                        <?php
                                            $objectives = explode("\n", $details_course->course_objectives);
                                            $objectives = array_filter($objectives, function($obj) {
                                                return !empty(trim($obj));
                                            });
                                        ?>

                                        <?php if(count($objectives) > 1): ?>
                                        <ul class="objectives-list-styled" style="direction: rtl;">
                                            <?php $__currentLoopData = $objectives; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objective): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if(!empty(trim($objective))): ?>
                                                <li class="mb-3 d-flex align-items-start" style="direction: rtl;">
                                                    <span style="text-align: right;"><?php echo e(trim(str_replace(['✅', '✓', '-'], '', $objective))); ?></span>
                                                    <i class="fa fa-check-circle text-success mr-3 mt-1" style="font-size: 18px;"></i>
                                                </li>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                        <?php else: ?>
                                        <div class="single-objective" style="text-align: right;">
                                            <?php echo nl2br(e($details_course->course_objectives)); ?>

                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Target Audience Tab -->
                            <?php if($details_course->target_audience): ?>
                            <div class="tab-pane fade" id="audience" role="tabpanel" aria-labelledby="audience-tab">
                                <div class="audience-content" style="text-align: right;">
                                    <h3 class="mb-4" style="color: #2c3e50; font-weight: 700; text-align: right;">الفئة المستهدفة</h3>
                                    <div class="audience-list" style="line-height: 1.8; color: #495057; font-size: 16px; text-align: right;">
                                        <?php
                                            $audiences = explode("\n", $details_course->target_audience);
                                            $audiences = array_filter($audiences, function($aud) {
                                                return !empty(trim($aud));
                                            });
                                        ?>

                                        <?php if(count($audiences) > 1): ?>
                                        <ul class="audience-list-styled" style="direction: rtl;">
                                            <?php $__currentLoopData = $audiences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $audience): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if(!empty(trim($audience))): ?>
                                                <li class="mb-3 d-flex align-items-start" style="direction: rtl;">
                                                    <span style="text-align: right;"><?php echo e(trim(str_replace(['✔️', '✓', '-'], '', $audience))); ?></span>
                                                    <i class="fa fa-user text-primary mr-3 mt-1" style="font-size: 18px;"></i>
                                                </li>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                        <?php else: ?>
                                        <div class="single-audience" style="text-align: right;">
                                            <?php echo nl2br(e($details_course->target_audience)); ?>

                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Instructors Tab -->
                            <?php if($instructors && $instructors->count() > 0): ?>
                            <div class="tab-pane fade" id="instructors" role="tabpanel" aria-labelledby="instructors-tab">
                                <div class="instructors-content" style="text-align: right;">
                                    <h3 class="mb-4" style="color: #2c3e50; font-weight: 700; text-align: right;">مدربو الدورة</h3>
                                    <div class="instructors-grid">
                                        <?php $__currentLoopData = $instructors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $instructor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="instructor-card mb-4 p-4" style="background: #f8f9fa; border-radius: 15px; border: 1px solid #e9ecef; direction: rtl;">
                                            <div class="d-flex align-items-start" style="direction: rtl;">
                                                <div class="instructor-info flex-grow-1 mr-4" style="text-align: right;">
                                                    <h5 class="instructor-name mb-2" style="color: #2c3e50; font-weight: 700; text-align: right;">
                                                        <?php echo e($instructor->name ?? 'مدرب'); ?>

                                                    </h5>
                                                    <p class="instructor-title mb-2" style="color: #6c757d; font-size: 16px; text-align: right;">
                                                        <?php echo e($instructor->jobs ?? 'مدرب'); ?>

                                                    </p>
                                                    <?php if($instructor->rating): ?>
                                                    <div class="instructor-rating mb-3" style="text-align: right;">
                                                        <span class="rating-text ml-2" style="color: #6c757d; font-weight: 600;">
                                                            (<?php echo e($instructor->rating); ?>/5)
                                                        </span>
                                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                                            <?php if($i <= $instructor->rating): ?>
                                                                <i class="fa fa-star text-warning"></i>
                                                            <?php else: ?>
                                                                <i class="fa fa-star-o text-muted"></i>
                                                            <?php endif; ?>
                                                        <?php endfor; ?>
                                                    </div>
                                                    <?php endif; ?>
                                                    <?php if($instructor->description): ?>
                                                    <p class="instructor-bio mb-0" style="color: #495057; line-height: 1.6; text-align: right;">
                                                        <?php echo e($instructor->description); ?>

                                                    </p>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="instructor-avatar">
                                                    <?php if($instructor->image): ?>
                                                        <img src="<?php echo e(asset('uploads/user_images/' . $instructor->image)); ?>"
                                                             alt="<?php echo e($instructor->name); ?>"
                                                             class="rounded-circle"
                                                             style="width: 80px; height: 80px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center"
                                                             style="width: 80px; height: 80px; background: #007bff; color: white; font-weight: 600; font-size: 24px;">
                                                            <?php echo e(substr($instructor->name ?? 'مدرب', 0, 1)); ?>

                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Contact Card -->
                    <div class="contact-card">
                        <div class="card shadow-sm" style="border: none; border-radius: 15px;">
                            <div class="card-header text-center" style="background: #007bff; color: white; border-radius: 15px 15px 0 0; padding: 20px;">
                                <h5 class="mb-0" style="font-weight: 600;">
                                    تحتاج مساعدة؟<i class="fa fa-phone mr-2"></i>
                                </h5>
                            </div>
                            <div class="card-body p-4 text-center">
                                <p class="mb-4" style="color: #6c757d; line-height: 1.6;">
                                    تواصل معنا للحصول على استشارة مجانية حول الدورة
                                </p>
                                <div class="contact-info">
                                    <div class="contact-item mb-3 p-3" style="background: #f8f9fa; border-radius: 10px; border-right: 4px solid #28a745; text-align: center;">
                                        <span style="color: #495057; font-weight: 600;">0774479525</span>
                                        <i class="fa fa-phone text-success mr-2"></i>
                                    </div>
                                    <div class="contact-item mb-3 p-3" style="background: #f8f9fa; border-radius: 10px; border-right: 4px solid #28a745; text-align: center;">
                                        <span style="color: #495057; font-weight: 600;">0665657400</span>
                                        <i class="fa fa-phone text-success mr-2"></i>
                                    </div>
                                    <div class="contact-item p-3" style="background: #f8f9fa; border-radius: 10px; border-right: 4px solid #dc3545; text-align: center;">
                                        <span style="color: #495057; font-weight: 600;">برج بوعريريج، الجزائر</span>
                                        <i class="fa fa-map-marker text-danger mr-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Modal -->
    <?php if($details_course->demo_video): ?>
    <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content" style="border-radius: 15px; border: none;">
                <div class="modal-header" style="border-bottom: none; background: #007bff; color: white; border-radius: 15px 15px 0 0;">
                    <h5 class="modal-title" id="videoModalLabel">
                        <i class="fa fa-play-circle ml-2"></i>
                        معاينة الدورة - <?php echo e($details_course->name); ?>

                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-0">
                    <div class="video-container" style="position: relative; border-radius: 0 0 15px 15px; overflow: hidden;">
                        <video
                            id="courseVideo"
                            controls
                            style="width: 100%; height: 400px; object-fit: cover;"
                            class="course-video">
                            <source src="<?php echo e(asset('uploads/'.$details_course->demo_video)); ?>" type="video/mp4">
                            <source src="<?php echo e(asset('uploads/'.$details_course->demo_video)); ?>" type="video/webm">
                            متصفحك لا يدعم تشغيل الفيديو
                        </video>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>



	<!-- Video Modal -->
	<?php if($details_course->demo_video): ?>
	<div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg modal-dialog-centered" role="document">
			<div class="modal-content" style="border-radius: 15px; border: none;">
				<div class="modal-header" style="border-bottom: none; background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; border-radius: 15px 15px 0 0;">
					<h5 class="modal-title" id="videoModalLabel">
						<i class="fa fa-play-circle ml-2"></i>
						معاينة الدورة - <?php echo e($details_course->name); ?>

					</h5>
					<button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body p-0">
					<div class="video-container" style="position: relative; border-radius: 0 0 15px 15px; overflow: hidden;">
						<video
							id="courseVideo"
							controls
							style="width: 100%; height: 400px; object-fit: cover;"
							class="course-video">
							<source src="<?php echo e(asset('uploads/'.$details_course->demo_video)); ?>" type="video/mp4">
							<source src="<?php echo e(asset('uploads/'.$details_course->demo_video)); ?>" type="video/webm">
							متصفحك لا يدعم تشغيل الفيديو
						</video>
					</div>
				</div>
			</div>
		</div>
	</div>
	<?php endif; ?>

	<!-- End intro Courses -->
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Course Header Section */
.course-header-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.breadcrumb-item a {
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #007bff !important;
}

.course-title {
    animation: fadeInUp 1s ease-out;
}

.course-subtitle {
    animation: fadeInUp 1s ease-out 0.2s both;
}

.course-meta-info {
    animation: fadeInUp 1s ease-out 0.4s both;
}

.course-preview-card {
    animation: fadeInRight 1s ease-out 0.6s both;
}

/* Course Preview Card */
.course-preview-card .card {
    transition: all 0.3s ease;
    border: none !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
}

.course-preview-card .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.course-media {
    position: relative;
    overflow: hidden;
}

.video-overlay {
    transition: all 0.3s ease;
    opacity: 0;
}

.course-media:hover .video-overlay {
    opacity: 1;
}

.play-button {
    transition: all 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
    background: rgba(255,255,255,1) !important;
    box-shadow: 0 8px 25px rgba(0,123,255,0.3);
}

/* Course Tabs */
.nav-tabs {
    border-bottom: 2px solid #e9ecef !important;
}

.nav-tabs .nav-link {
    border: none !important;
    border-bottom: 3px solid transparent !important;
    transition: all 0.3s ease;
    color: #6c757d !important;
    font-weight: 600;
    padding: 15px 20px;
}

.nav-tabs .nav-link:hover {
    border-color: transparent transparent #007bff transparent !important;
    color: #007bff !important;
    background: rgba(0,123,255,0.05);
}

.nav-tabs .nav-link.active {
    border-color: transparent transparent #007bff transparent !important;
    color: #007bff !important;
    background: rgba(0,123,255,0.05);
}

.tab-content {
    animation: fadeInUp 0.5s ease-out;
}

.tab-pane {
    min-height: 300px;
}

/* Objectives and Audience Lists */
.objectives-list-styled,
.audience-list-styled {
    list-style: none;
    padding: 0;
}

.objectives-list-styled li,
.audience-list-styled li {
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.objectives-list-styled li:hover {
    background: rgba(40, 167, 69, 0.1) !important;
    transform: translateX(10px);
}

.audience-list-styled li:hover {
    background: rgba(0, 123, 255, 0.1) !important;
    transform: translateX(10px);
}

/* Instructor Cards */
.instructor-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef !important;
}

.instructor-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.15) !important;
    border-color: #007bff !important;
}

.instructor-avatar img,
.avatar-placeholder {
    transition: all 0.3s ease;
}

.instructor-card:hover .instructor-avatar img,
.instructor-card:hover .avatar-placeholder {
    transform: scale(1.05);
}

/* Contact Card */
.contact-card .card {
    transition: all 0.3s ease;
}

.contact-card .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0,123,255,0.15) !important;
}

.contact-item {
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Price Display */
.current-price {
    animation: pulse 2s infinite;
    text-shadow: 2px 2px 4px rgba(0,123,255,0.3);
}

.price-text {
    animation: pulse 2s infinite;
}

/* Course Includes */
.course-includes ul li {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 6px;
    margin: -8px;
}

.course-includes ul li:hover {
    background: rgba(0,123,255,0.05);
    transform: translateX(10px);
}

/* Course Details */
.detail-item {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 6px;
    margin: -8px;
}

.detail-item:hover {
    background: rgba(0,123,255,0.05);
    transform: translateX(5px);
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    border: none !important;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.4) !important;
    background: linear-gradient(135deg, #0056b3 0%, #007bff 100%) !important;
}

/* Instructors in Header */
.instructor-item {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 8px;
    margin: -8px;
}

.instructor-item:hover {
    background: rgba(0,123,255,0.05);
    transform: translateY(-2px);
}

/* Hero Section Enhancements */
.course-hero-section {
    position: relative;
    background-attachment: fixed;
}

/* Course Image and Video Play Button */
.course-image-container {
    transition: all 0.3s ease;
}

.course-image-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.4) !important;
}

.course-main-image {
    transition: all 0.3s ease;
}

.video-play-overlay {
    opacity: 0;
    transition: all 0.3s ease;
}

.course-image-container:hover .video-play-overlay {
    opacity: 1;
}

.play-button {
    transition: all 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
    background: rgba(231, 76, 60, 1) !important;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

.hero-media-section {
    animation: fadeInRight 1s ease-out;
}

.hero-title {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    animation: fadeInUp 1s ease-out 0.2s both;
}

.course-meta {
    animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-price-card {
    animation: fadeInUp 1s ease-out 0.6s both;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.hero-price-card:hover {
    transform: translateY(-5px);
}

/* Card Enhancements */
.card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05) !important;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.card:hover .card-header::before {
    left: 100%;
}

/* Video Enhancements */
.course-video {
    transition: all 0.3s ease;
    border-radius: 0 0 15px 15px;
}

.course-video:hover {
    transform: scale(1.02);
}

/* Button Enhancements */
.enrollment-btn {
    position: relative;
    overflow: hidden;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.enrollment-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.enrollment-btn:hover::before {
    left: 100%;
}

.enrollment-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    color: white !important;
}

/* Info Items Animation */
.info-item {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 8px;
    margin: -8px;
}

.info-item:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

/* Rating Stars Animation */
.rating-display i {
    transition: all 0.2s ease;
}

.rating-display:hover i {
    transform: scale(1.2);
}

/* Contact Items */
.contact-item {
    transition: all 0.3s ease;
    border-radius: 8px;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Contact Card Enhancement */
.contact-info .contact-item {
    background: #f8f9fa !important;
    border-radius: 8px !important;
    padding: 8px !important;
    margin-bottom: 12px !important;
}

.contact-info .contact-item span {
    color: #2c3e50 !important;
    font-weight: 600 !important;
}

/* Feature Items */
.feature-item {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 8px;
    margin: -8px;
}

.feature-item:hover {
    background: rgba(40, 167, 69, 0.1);
    transform: translateX(5px);
}

/* Animations */
@keyframes  fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes  fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes  pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem !important;
    }

    .hero-subtitle {
        font-size: 1rem !important;
    }

    .course-meta {
        flex-direction: column;
        gap: 10px !important;
    }

    .meta-item {
        justify-content: center;
    }

    .hero-price-card {
        margin-top: 30px;
    }
}

/* Price Display Enhancement */
.price-amount {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s infinite;
}

/* Course Badge */
.course-badge .badge {
    animation: fadeInDown 1s ease-out;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation for Video */
.video-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-container.loading::before {
    opacity: 1;
}

@keyframes  spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Instructor Cards */
.instructor-card {
    transition: all 0.3s ease;
    background: #fff;
}

.instructor-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(111, 66, 193, 0.15);
    border-color: #6f42c1 !important;
}

.instructor-avatar img,
.avatar-placeholder {
    transition: all 0.3s ease;
}

.instructor-card:hover .instructor-avatar img,
.instructor-card:hover .avatar-placeholder {
    transform: scale(1.1);
}

.instructor-name {
    transition: color 0.3s ease;
}

.instructor-card:hover .instructor-name {
    color: #8e44ad !important;
}

/* Objectives and Target Audience */
.objectives-content,
.target-audience-content {
    color: #555;
}

.objectives-content i,
.target-audience-content i {
    color: #28a745;
    margin-left: 8px;
}

/* Contact for Price Animation */
.contact-for-price {
    animation: pulse 2s infinite;
}

.contact-numbers div {
    transition: all 0.3s ease;
    padding: 2px 0;
}

.contact-numbers div:hover {
    color: #e74c3c !important;
    transform: translateX(5px);
}

/* Enhanced Button Styles */
.enrollment-btn-hero {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.enrollment-btn-hero:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%) !important;
}

.enrollment-btn-sidebar {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.enrollment-btn-sidebar:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%) !important;
    color: white !important;
    text-decoration: none !important;
}

/* Card Headers */
.card-header {
    border-bottom: none !important;
}

/* Text Colors */
.course-description,
.objectives-content,
.target-audience-content {
    color: #2c3e50 !important;
}

/* Info Items */
.info-item .info-label {
    color: #2c3e50 !important;
}

.info-item .info-value {
    color: #2c3e50 !important;
}

/* Hero Section Text */
.course-hero-section h1 {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.course-hero-section p {
    color: rgba(255,255,255,0.9) !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Price Display */
.price-amount {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* Instructor Cards Enhancement */
.instructor-card {
    background: #ffffff !important;
    border: 1px solid #e9ecef !important;
}

.instructor-card:hover {
    border-color: #9b59b6 !important;
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.15) !important;
}

/* Objectives and Target Audience Items */
.objective-item {
    transition: all 0.3s ease;
    cursor: default;
}

.objective-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.15);
    background: #e8f5e8 !important;
}

.audience-item {
    transition: all 0.3s ease;
    cursor: default;
}

.audience-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.15);
    background: #fef9e7 !important;
}

.objective-icon i,
.audience-icon i {
    transition: all 0.3s ease;
}

.objective-item:hover .objective-icon i {
    transform: scale(1.2);
    color: #27ae60 !important;
}

.audience-item:hover .audience-icon i {
    transform: scale(1.2);
    color: #f39c12 !important;
}

/* Responsive adjustments for objectives and audience */
@media (max-width: 768px) {
    .objective-item,
    .audience-item {
        margin-bottom: 15px;
    }

    .col-md-6 {
        padding-left: 10px;
        padding-right: 10px;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Function to show video modal
function showVideoModal() {
    $('#videoModal').modal('show');
}

$(document).ready(function() {
    // Initialize Bootstrap tabs
    $('#courseContentTabs a').on('click', function (e) {
        e.preventDefault();
        $(this).tab('show');
    });

    // Tab switching animation
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        var target = $(e.target).attr("href");
        $(target).addClass('animated fadeInUp');
        setTimeout(function() {
            $(target).removeClass('animated fadeInUp');
        }, 500);
    });

    // Pause video when modal is closed
    $('#videoModal').on('hidden.bs.modal', function () {
        var video = document.getElementById('courseVideo');
        if (video) {
            video.pause();
            video.currentTime = 0;
        }
    });

    // Smooth scroll for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Video loading animation
    $('.course-video').on('loadstart', function() {
        $(this).closest('.video-container').addClass('loading');
    });

    $('.course-video').on('loadeddata', function() {
        $(this).closest('.video-container').removeClass('loading');
    });

    // Parallax effect for hero section
    $(window).scroll(function() {
        var scrolled = $(this).scrollTop();
        var parallax = $('.course-hero-section');
        var speed = 0.5;

        parallax.css('transform', 'translateY(' + (scrolled * speed) + 'px)');
    });

    // Counter animation for stats
    $('.price-amount, .info-value').each(function() {
        var $this = $(this);
        var countTo = $this.text().replace(/[^0-9]/g, '');

        if (countTo && !isNaN(countTo)) {
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    var formattedNumber = Math.floor(this.countNum).toLocaleString();
                    if ($this.hasClass('price-amount')) {
                        $this.text(formattedNumber);
                    }
                },
                complete: function() {
                    var formattedNumber = parseInt(countTo).toLocaleString();
                    if ($this.hasClass('price-amount')) {
                        $this.text(formattedNumber);
                    }
                }
            });
        }
    });

    // Intersection Observer for animations
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        });

        document.querySelectorAll('.card').forEach(card => {
            observer.observe(card);
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.home.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Academy\resources\views/home/<USER>/ ?>