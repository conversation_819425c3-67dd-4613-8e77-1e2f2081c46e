<?php $__env->startSection('content'); ?>
<div dir="rtl" style="direction: rtl; text-align: right; margin-top: 80px;">

    <!-- Course Main Section -->
    <section class="course-main-section" style="background: transparent; padding: 30px 0; position: relative; z-index: 1;">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Course Info Card -->
                    <div class="card shadow-sm mb-4" style="border: none; border-radius: 15px;" dir="rtl">
                        <div class="card-body p-4">
                            <!-- Breadcrumb -->
                            <nav aria-label="breadcrumb" class="mb-3">
                                <ol class="breadcrumb" style="background: transparent; padding: 0; direction: rtl;">
                                    <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" style="color: #6c757d;">الرئيسية</a></li>
                                    <li class="breadcrumb-item"><a href="#" style="color: #6c757d;">الدورات</a></li>
                                    <li class="breadcrumb-item active" aria-current="page" style="color: #495057;"><?php echo e($details_course->name); ?></li>
                                </ol>
                            </nav>

                            <!-- Course Title -->
                            <h1 class="course-title mb-4" style="font-size: 2.2rem; font-weight: 700; color: #1a1a1a; line-height: 1.2; text-align: right;">
                                <?php echo e($details_course->name); ?>

                            </h1>

                            <!-- Course Content Tabs -->
                            <div class="course-content-tabs">
                                <!-- Tab Navigation -->
                                <ul class="nav nav-tabs d-flex" id="courseContentTabs" role="tablist" style="direction: rtl; justify-content: flex-end; flex-direction: row-reverse;">
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link active" id="overview-tab" data-toggle="tab" href="#overview" role="tab" aria-controls="overview" aria-selected="true">
                                            <i class="fas fa-book-open"></i>
                                            نظرة عامة
                                        </a>
                                    </li>
                                    <?php if($details_course->course_objectives): ?>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link" id="objectives-tab" data-toggle="tab" href="#objectives" role="tab" aria-controls="objectives" aria-selected="false">
                                            <i class="fas fa-bullseye"></i>
                                            الأهداف
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    <?php if($details_course->target_audience): ?>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link" id="audience-tab" data-toggle="tab" href="#audience" role="tab" aria-controls="audience" aria-selected="false">
                                            <i class="fas fa-user-friends"></i>
                                            الفئة المستهدفة
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    <?php if($instructors && $instructors->count() > 0): ?>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link" id="instructors-tab" data-toggle="tab" href="#instructors" role="tab" aria-controls="instructors" aria-selected="false">
                                            <i class="fas fa-chalkboard-teacher"></i>
                                            المدربون
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>

                            <!-- Tab Content -->
                            <div class="tab-content" id="courseContentTabsContent" style="padding: 30px 0;">
                                <!-- Overview Tab -->
                                <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                                    <div class="overview-content" style="text-align: right;">
                                        <h3 class="section-title mb-4">وصف الدورة</h3>
                                        <div class="description-content" style="line-height: 1.8; color: #495057; font-size: 16px; text-align: right;">
                                            <?php echo nl2br(e($details_course->description)); ?>

                                        </div>
                                    </div>
                                </div>

                                <!-- Objectives Tab -->
                                <?php if($details_course->course_objectives): ?>
                                <div class="tab-pane fade" id="objectives" role="tabpanel" aria-labelledby="objectives-tab">
                                    <div class="objectives-content" style="text-align: right;">
                                        <h3 class="section-title mb-4">أهداف الدورة</h3>
                                        <div class="objectives-list" style="line-height: 1.8; color: #495057; font-size: 16px; text-align: right;">
                                            <?php
                                                $objectives = explode("\n", $details_course->course_objectives);
                                                $objectives = array_filter($objectives, function($obj) {
                                                    return !empty(trim($obj));
                                                });
                                            ?>

                                            <?php if(count($objectives) > 1): ?>
                                            <ul class="objectives-list-styled" style="direction: rtl; list-style: none; padding: 0;">
                                                <?php $__currentLoopData = $objectives; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objective): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if(!empty(trim($objective))): ?>
                                                    <li class="mb-3 d-flex align-items-start" style="direction: rtl;">
                                                        <span style="text-align: right; flex-grow: 1;"><?php echo e(trim(str_replace(['✅', '✓', '-'], '', $objective))); ?></span>
                                                        <i class="fa fa-check-circle text-success ml-3 mt-1" style="font-size: 18px;"></i>
                                                    </li>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                            <?php else: ?>
                                            <div class="single-objective" style="text-align: right;">
                                                <?php echo nl2br(e($details_course->course_objectives)); ?>

                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- Target Audience Tab -->
                                <?php if($details_course->target_audience): ?>
                                <div class="tab-pane fade" id="audience" role="tabpanel" aria-labelledby="audience-tab">
                                    <div class="audience-content" style="text-align: right;">
                                        <h3 class="section-title mb-4">الفئة المستهدفة</h3>
                                        <div class="audience-list" style="line-height: 1.8; color: #495057; font-size: 16px; text-align: right;">
                                            <?php
                                                $audiences = explode("\n", $details_course->target_audience);
                                                $audiences = array_filter($audiences, function($aud) {
                                                    return !empty(trim($aud));
                                                });
                                            ?>

                                            <?php if(count($audiences) > 1): ?>
                                            <ul class="audience-list-styled" style="direction: rtl; list-style: none; padding: 0;">
                                                <?php $__currentLoopData = $audiences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $audience): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if(!empty(trim($audience))): ?>
                                                    <li class="mb-3 d-flex align-items-start" style="direction: rtl;">
                                                        <span style="text-align: right; flex-grow: 1;"><?php echo e(trim(str_replace(['✔️', '✓', '-'], '', $audience))); ?></span>
                                                        <i class="fa fa-user text-primary ml-3 mt-1" style="font-size: 18px;"></i>
                                                    </li>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                            <?php else: ?>
                                            <div class="single-audience" style="text-align: right;">
                                                <?php echo nl2br(e($details_course->target_audience)); ?>

                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- Instructors Tab -->
                                <?php if($instructors && $instructors->count() > 0): ?>
                                <div class="tab-pane fade" id="instructors" role="tabpanel" aria-labelledby="instructors-tab">
                                    <div class="instructors-content" style="text-align: right;">
                                        <h3 class="section-title mb-4">مدربو الدورة</h3>
                                        <div class="instructors-grid">
                                            <?php $__currentLoopData = $instructors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $instructor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="instructor-card mb-4 p-4" style="background: #f8f9fa; border-radius: 15px; border: 1px solid #e9ecef; direction: rtl;">
                                                <div class="d-flex align-items-start" style="direction: rtl;">
                                                    <div class="instructor-info flex-grow-1 ml-4" style="text-align: right;">
                                                        <h5 class="instructor-name mb-2" style="color: #2c3e50; font-weight: 700; text-align: right;">
                                                            <?php echo e($instructor->name ?? 'مدرب'); ?>

                                                        </h5>
                                                        <p class="instructor-title mb-2" style="color: #6c757d; font-size: 16px; text-align: right;">
                                                            <?php echo e($instructor->jobs ?? 'مدرب'); ?>

                                                        </p>
                                                        <?php if($instructor->rating): ?>
                                                        <div class="instructor-rating mb-3" style="text-align: right;">
                                                            <span class="rating-text mr-2" style="color: #6c757d; font-weight: 600;">
                                                                (<?php echo e($instructor->rating); ?>/5)
                                                            </span>
                                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                                <?php if($i <= $instructor->rating): ?>
                                                                    <i class="fa fa-star text-warning"></i>
                                                                <?php else: ?>
                                                                    <i class="fa fa-star-o text-muted"></i>
                                                                <?php endif; ?>
                                                            <?php endfor; ?>
                                                        </div>
                                                        <?php endif; ?>
                                                        <?php if($instructor->description): ?>
                                                        <p class="instructor-bio mb-0" style="color: #495057; line-height: 1.6; text-align: right;">
                                                            <?php echo e($instructor->description); ?>

                                                        </p>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="instructor-avatar">
                                                        <?php if($instructor->image): ?>
                                                            <img src="<?php echo e(asset('uploads/user_images/' . $instructor->image)); ?>"
                                                                 alt="<?php echo e($instructor->name); ?>"
                                                                 class="rounded-circle"
                                                                 style="width: 80px; height: 80px; object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center"
                                                                 style="width: 80px; height: 80px; background: #007bff; color: white; font-weight: 600; font-size: 24px;">
                                                                <?php echo e(substr($instructor->name ?? 'مدرب', 0, 1)); ?>

                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Course Preview Card -->
                    <div class="course-preview-card" style="position: sticky; top: 100px;" dir="rtl">
                        <div class="card shadow-sm mb-4" style="border: none; border-radius: 15px;">
                            <div class="course-media" style="position: relative;">
                                <?php if($details_course->demo_video): ?>
                                    <video
                                        poster="<?php echo e($details_course->image_path); ?>"
                                        style="width: 100%; height: 250px; object-fit: cover; border-radius: 15px 15px 0 0;"
                                        class="course-video">
                                        <source src="<?php echo e(asset('uploads/'.$details_course->demo_video)); ?>" type="video/mp4">
                                        <source src="<?php echo e(asset('uploads/'.$details_course->demo_video)); ?>" type="video/webm">
                                        متصفحك لا يدعم تشغيل الفيديو
                                    </video>
                                    <div class="video-overlay position-absolute w-100 h-100 d-flex align-items-center justify-content-center"
                                         style="top: 0; left: 0; background: rgba(0,0,0,0.3); border-radius: 15px 15px 0 0; cursor: pointer;"
                                         onclick="showVideoModal()">
                                        <div class="play-button d-flex align-items-center justify-content-center"
                                             style="width: 80px; height: 80px; background: rgba(255,255,255,0.9); border-radius: 50%; color: #007bff; font-size: 30px;">
                                            <i class="fa fa-play"></i>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <img src="<?php echo e($details_course->image_path); ?>"
                                         alt="<?php echo e($details_course->name); ?>"
                                         style="width: 100%; height: 250px; object-fit: cover; border-radius: 15px 15px 0 0;">
                                <?php endif; ?>
                            </div>

                            <div class="card-body p-4">
                                <!-- Price Section -->
                                <div class="price-section text-center mb-4">
                                    <?php if($details_course->show_price): ?>
                                        <div class="current-price mb-2">
                                            <span class="price-text" style="font-size: 2rem; font-weight: 700; color: #007bff;">
                                                <?php echo e(number_format($details_course->price, 0)); ?>

                                            </span>
                                            <span style="font-size: 1.2rem; color: #6c757d; font-weight: 600;">دج</span>
                                        </div>
                                    <?php else: ?>
                                        <div class="contact-price mb-2">
                                            <span style="font-size: 1.5rem; font-weight: 700; color: #dc3545;">
                                                اتصل للاستفسار عن السعر
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Enrollment Button -->
                                <div class="enrollment-section mb-4">
                                    <a href="<?php echo e(route('purchase.create', $details_course->id)); ?>"
                                       class="btn btn-danger btn-lg btn-block pulse-button"
                                       style="border-radius: 10px; padding: 20px; font-weight: 700; font-size: 20px; animation: pulse 2s infinite;">
                                        اشترك الآن
                                        <i class="fa fa-shopping-cart mr-2"></i>
                                    </a>
                                </div>

                                <!-- Course Info -->
                                <div class="course-info">
                                    <!-- Duration and Students in one row -->
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <div class="info-item p-3" style="background: #e9ecef; border-radius: 10px; text-align: right; direction: rtl; border: 1px solid #dee2e6;">
                                                <div style="color: #2c3e50; font-weight: 600; font-size: 14px;">مدة الدورة: <span style="color: #007bff; font-weight: 700;"><?php echo e($details_course->time); ?> ساعة</span></div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="info-item p-3" style="background: #e9ecef; border-radius: 10px; text-align: right; direction: rtl; border: 1px solid #dee2e6;">
                                                <div style="color: #2c3e50; font-weight: 600; font-size: 14px;">عدد الطلبة: <span style="color: #28a745; font-weight: 700;"><?php echo e($details_course->studant_count ?? '0'); ?> طالب</span></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="contact-info p-3" style="background: #d1ecf1; border-radius: 10px; text-align: right; direction: rtl; border: 1px solid #bee5eb;">
                                        <div style="color: #1a1a1a; font-weight: 700; font-size: 16px; margin-bottom: 10px; text-align: center;">للاستفسار</div>
                                        <div style="color: #1976d2; font-weight: 600; margin-bottom: 5px; text-align: center;">0774479525</div>
                                        <div style="color: #1976d2; font-weight: 600; margin-bottom: 5px; text-align: center;">0665657400</div>
                                        <div style="color: #495057; font-size: 14px; text-align: center;">برج بوعريريج، الجزائر</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Modal -->
    <?php if($details_course->demo_video): ?>
    <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content" style="border-radius: 15px; border: none;">
                <div class="modal-header" style="border-bottom: none; background: #007bff; color: white; border-radius: 15px 15px 0 0;">
                    <h5 class="modal-title" id="videoModalLabel">
                        <i class="fa fa-play-circle ml-2"></i>
                        معاينة الدورة - <?php echo e($details_course->name); ?>

                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-0">
                    <div class="video-container" style="position: relative; border-radius: 0 0 15px 15px; overflow: hidden;">
                        <video
                            id="courseVideo"
                            controls
                            style="width: 100%; height: 400px; object-fit: cover;"
                            class="course-video">
                            <source src="<?php echo e(asset('uploads/'.$details_course->demo_video)); ?>" type="video/mp4">
                            <source src="<?php echo e(asset('uploads/'.$details_course->demo_video)); ?>" type="video/webm">
                            متصفحك لا يدعم تشغيل الفيديو
                        </video>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Chat Box -->
    <div id="chatBox" class="chat-box" style="position: fixed; bottom: 20px; left: 20px; width: 350px; height: 400px; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); z-index: 1000; display: none; direction: rtl;">
        <div class="chat-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 15px 15px 0 0; text-align: right;">
            <div class="d-flex justify-content-between align-items-center">
                <button onclick="closeChatBox()" style="background: none; border: none; color: white; font-size: 18px;">×</button>
                <div>
                    <h6 class="mb-0" style="font-weight: 600;">💬 مساعد الدورات</h6>
                    <small style="opacity: 0.8;">متاح الآن للمساعدة</small>
                </div>
                <div class="chat-avatar" style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-robot" style="font-size: 20px;"></i>
                </div>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages" style="height: 280px; overflow-y: auto; padding: 15px; background: #f8f9fa;">
            <div class="message bot-message" style="margin-bottom: 15px;">
                <div style="background: #e3f2fd; padding: 10px 15px; border-radius: 15px 15px 15px 5px; max-width: 80%; text-align: right;">
                    <p class="mb-1" style="color: #1976d2; font-size: 14px;">مرحباً! 👋</p>
                    <p class="mb-0" style="color: #495057; font-size: 14px;">كيف يمكنني مساعدتك بخصوص هذه الدورة؟</p>
                </div>
            </div>
        </div>

        <div class="chat-input" style="padding: 15px; border-top: 1px solid #e9ecef;">
            <div class="input-group">
                <input type="text" id="chatInput" class="form-control" placeholder="اكتب رسالتك هنا..." style="border-radius: 20px; border: 1px solid #ddd; text-align: right;" onkeypress="handleChatKeyPress(event)">
                <div class="input-group-append">
                    <button onclick="sendMessage()" class="btn btn-primary" style="border-radius: 50%; width: 40px; height: 40px; margin-right: 10px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
                        <i class="fas fa-paper-plane" style="font-size: 14px;"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Button -->
    <button id="chatButton" onclick="openChatBox()" class="chat-button" style="position: fixed; bottom: 20px; left: 20px; width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 50%; color: white; font-size: 24px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); z-index: 999; transition: all 0.3s ease;">
        <i class="fas fa-comments"></i>
    </button>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Page Background */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.05) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(255,255,255,0.05) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.05) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.05) 75%);
    background-size: 30px 30px;
    background-position: 0 0, 0 15px, 15px -15px, -15px 0px;
    animation: move 20s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes  move {
    0% { background-position: 0 0, 0 15px, 15px -15px, -15px 0px; }
    100% { background-position: 30px 30px, 30px 45px, 45px 15px, 15px 30px; }
}

/* Course Title Styles */
.course-title {
    color: #2c3e50 !important;
    font-weight: 800 !important;
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 25px;
}

.course-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 4px;
    background: linear-gradient(to right, #007bff, #00bfff);
    border-radius: 2px;
}

/* Section Titles */
.section-title {
    color: #2c3e50 !important;
    font-weight: 700 !important;
    font-size: 1.8rem !important;
    margin-bottom: 20px !important;
    position: relative;
    padding-bottom: 10px;
}

.section-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, #28a745, #5cb85c);
    border-radius: 2px;
}

/* Tab Content Styling */
.tab-content {
    background: transparent !important;
    border: none !important;
    padding: 20px 0 !important;
}

/* Tab Navigation Styling */
.nav-tabs {
    border-bottom: 2px solid #e9ecef !important;
    background: transparent !important;
}

.nav-tabs .nav-link {
    border: none !important;
    color: #495057 !important;
    font-weight: 600 !important;
    padding: 12px 20px !important;
    margin: 0 5px !important;
    background: transparent !important;
    transition: all 0.3s ease !important;
    border-radius: 0 !important;
    position: relative;
}

.nav-tabs .nav-link:hover {
    color: #007bff !important;
    background: transparent !important;
}

.nav-tabs .nav-link.active {
    color: #007bff !important;
    background: transparent !important;
    border-bottom: 3px solid #007bff !important;
    font-weight: 700 !important;
}

.nav-tabs .nav-link i {
    margin-left: 8px;
}

/* Content Styling */
.description-content,
.objectives-list,
.audience-list {
    color: #495057 !important;
    line-height: 1.8 !important;
    font-size: 16px !important;
}

/* List Items Styling */
.objectives-list-styled li,
.audience-list-styled li {
    padding: 10px 15px !important;
    margin-bottom: 10px !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    border-right: 3px solid #28a745 !important;
    transition: all 0.3s ease !important;
}

.audience-list-styled li {
    border-right-color: #17a2b8 !important;
}

.objectives-list-styled li:hover,
.audience-list-styled li:hover {
    transform: translateX(-5px) !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1) !important;
}

/* Instructor Cards */
.instructor-card {
    transition: all 0.3s ease !important;
    border-left: 3px solid #007bff !important;
}

.instructor-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.instructor-name {
    color: #2c3e50 !important;
    position: relative;
    display: inline-block;
}

.instructor-name::after {
    content: "";
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 50%;
    height: 2px;
    background: #007bff;
}

/* Price Section */
.price-text {
    color: #007bff !important;
    text-shadow: 1px 1px 3px rgba(0,123,255,0.2) !important;
}

/* Button Styling */
.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(220,53,69,0.3) !important;
    transition: all 0.3s ease !important;
}

.btn-danger:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(220,53,69,0.4) !important;
}

/* Info Items */
.info-item {
    transition: all 0.3s ease !important;
}

.info-item:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1) !important;
}

/* Contact Info */
.contact-info {
    transition: all 0.3s ease !important;
    border-left: 3px solid #17a2b8 !important;
}

.contact-info:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1) !important;
}

/* Pulse Animation */
@keyframes  pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse-button {
    animation: pulse 2s infinite;
}

/* Chat Button Animations */
.chat-button:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6) !important;
}

.chat-button {
    animation: chatPulse 3s infinite;
}

@keyframes  chatPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Enhanced Tab Animations */
.nav-tabs .nav-link {
    position: relative;
    overflow: hidden;
}

.nav-tabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.nav-tabs .nav-link:hover::before {
    left: 100%;
}

/* Card Hover Effects */
.card {
    transition: all 0.3s ease !important;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s;
    opacity: 0;
}

.card:hover::before {
    animation: shimmer 1.5s ease-in-out;
}

@keyframes  shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .course-title {
        font-size: 1.8rem !important;
    }

    .section-title {
        font-size: 1.5rem !important;
    }

    .nav-tabs .nav-link {
        padding: 10px 15px !important;
        font-size: 14px !important;
    }

    .nav-tabs {
        flex-direction: column !important;
    }

    .nav-tabs .nav-link {
        margin: 5px 0 !important;
        text-align: center !important;
    }

    #chatBox {
        width: 90% !important;
        left: 5% !important;
        right: 5% !important;
    }

    .chat-button {
        bottom: 15px !important;
        left: 15px !important;
        width: 50px !important;
        height: 50px !important;
        font-size: 20px !important;
    }
}

@media (max-width: 576px) {
    .course-title {
        font-size: 1.5rem !important;
    }

    .row {
        margin: 0 !important;
    }

    .col-lg-8, .col-lg-4 {
        padding: 0 10px !important;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Function to show video modal
function showVideoModal() {
    $('#videoModal').modal('show');
}

$(document).ready(function() {
    // Initialize Bootstrap tabs
    $('#courseContentTabs a').on('click', function (e) {
        e.preventDefault();
        $(this).tab('show');
    });

    // Pause video when modal is closed
    $('#videoModal').on('hidden.bs.modal', function () {
        var video = document.getElementById('courseVideo');
        if (video) {
            video.pause();
            video.currentTime = 0;
        }
    });

    // Add hover effect to cards
    $('.card').hover(
        function() {
            $(this).css('transform', 'translateY(-5px)');
            $(this).css('box-shadow', '0 10px 30px rgba(0,0,0,0.15)');
        },
        function() {
            $(this).css('transform', 'translateY(0)');
            $(this).css('box-shadow', '0 5px 15px rgba(0,0,0,0.1)');
        }
    );
});

// Chat Box Functions
function openChatBox() {
    document.getElementById('chatBox').style.display = 'block';
    document.getElementById('chatButton').style.display = 'none';
    document.getElementById('chatInput').focus();
}

function closeChatBox() {
    document.getElementById('chatBox').style.display = 'none';
    document.getElementById('chatButton').style.display = 'block';
}

function sendMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();

    if (message === '') return;

    // Add user message
    addMessage(message, 'user');
    input.value = '';

    // Simulate bot response
    setTimeout(() => {
        const responses = [
            'شكراً لك! سأساعدك بخصوص هذه الدورة. 😊',
            'يمكنك التسجيل في الدورة من خلال الضغط على زر "اشترك الآن"',
            'هذه الدورة تتضمن محتوى شامل ومفيد جداً',
            'للمزيد من المعلومات، يمكنك الاتصال على الأرقام المذكورة',
            'سأكون سعيداً لمساعدتك في أي استفسار آخر! 💪'
        ];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        addMessage(randomResponse, 'bot');
    }, 1000);
}

function addMessage(message, sender) {
    const messagesContainer = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message ' + sender + '-message';
    messageDiv.style.marginBottom = '15px';

    if (sender === 'user') {
        messageDiv.innerHTML = `
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 10px 15px; border-radius: 15px 15px 5px 15px; max-width: 80%; margin-right: auto; text-align: right;">
                <p class="mb-0" style="font-size: 14px;">${message}</p>
            </div>
        `;
    } else {
        messageDiv.innerHTML = `
            <div style="background: #e3f2fd; padding: 10px 15px; border-radius: 15px 15px 15px 5px; max-width: 80%; text-align: right;">
                <p class="mb-0" style="color: #495057; font-size: 14px;">${message}</p>
            </div>
        `;
    }

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function handleChatKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.home.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Academy\resources\views/home/<USER>/ ?>