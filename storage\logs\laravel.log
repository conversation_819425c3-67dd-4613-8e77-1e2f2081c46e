[2025-05-28 07:55:11] local.INFO: بداية إنشاء دورة جديدة {"user_id":1,"course_name":"دورة احترافية في التسويق الرقمي – انطلق نحو النجاح الرقمي","request_data":{"_token":"3oeJ53VC7oY5GULkEHgpNHRVui5vM4qeNy4IJYSR","_method":"post","name":"دورة احترافية في التسويق الرقمي – انطلق نحو النجاح الرقمي","url":"http://127.0.0.1:8000/dashboard/courses/create","categories_id":"3","Short_description":"دورة حديثة في التسويق الرقمي تغطي استراتيجيات التسويق الإلكتروني ووسائل التواصل الاجتماعي","description":"تقدم هذه الدورة التدريبية الحديثة المعرفة المتقدمة في مجال التسويق الرقمي، وتهدف إلى تأهيل المشاركين لاستخدام أحدث استراتيجيات التسويق الإلكتروني بفعالية.

تشمل الدورة موضوعات شاملة مثل إدارة وسائل التواصل الاجتماعي، والتسويق عبر محركات البحث، وإنشاء المحتوى الرقمي، وتحليل البيانات التسويقية، والتجارة الإلكترونية. كما تركز على فهم سلوك المستهلك الرقمي وبناء العلامة التجارية عبر الإنترنت.

يتم التدريب من خلال ورش عمل تطبيقية باستخدام أدوات التسويق الرقمي الفعلية، مع التركيز على الحالات العملية من السوق الجزائري والعربي. تؤهل الدورة المشاركين لإطلاق حملات تسويقية ناجحة، وقياس عائد الاستثمار التسويقي، والتكيف مع التطورات السريعة في عالم التسويق الرقمي.","course_objectives":"✅ إتقان استراتيجيات التسويق الرقمي الحديثة
✅ تطوير مهارات إدارة حملات وسائل التواصل الاجتماعي
✅ فهم تحليلات الويب وقياس الأداء التسويقي
✅ إكساب مهارات إنشاء المحتوى التسويقي
✅ تطبيق تقنيات تحسين محركات البحث SEO","target_audience":"✔️ مدراء وموظفي التسويق والمبيعات
✔️ أصحاب المشاريع والمتاجر الإلكترونية
✔️ المهتمين بريادة الأعمال الرقمية
✔️ موظفي وسائل التواصل الاجتماعي
✔️ الراغبين في تطوير مهارات التسويق الحديثة","price":"6000","time":"20","rating":"5"}} 
[2025-05-28 07:55:11] local.INFO: بيانات الدورة بعد المعالجة {"request_data":{"_token":"3oeJ53VC7oY5GULkEHgpNHRVui5vM4qeNy4IJYSR","_method":"post","name":"دورة احترافية في التسويق الرقمي – انطلق نحو النجاح الرقمي","url":"http://127.0.0.1:8000/dashboard/courses/create","categories_id":"3","Short_description":"دورة حديثة في التسويق الرقمي تغطي استراتيجيات التسويق الإلكتروني ووسائل التواصل الاجتماعي","description":"تقدم هذه الدورة التدريبية الحديثة المعرفة المتقدمة في مجال التسويق الرقمي، وتهدف إلى تأهيل المشاركين لاستخدام أحدث استراتيجيات التسويق الإلكتروني بفعالية.

تشمل الدورة موضوعات شاملة مثل إدارة وسائل التواصل الاجتماعي، والتسويق عبر محركات البحث، وإنشاء المحتوى الرقمي، وتحليل البيانات التسويقية، والتجارة الإلكترونية. كما تركز على فهم سلوك المستهلك الرقمي وبناء العلامة التجارية عبر الإنترنت.

يتم التدريب من خلال ورش عمل تطبيقية باستخدام أدوات التسويق الرقمي الفعلية، مع التركيز على الحالات العملية من السوق الجزائري والعربي. تؤهل الدورة المشاركين لإطلاق حملات تسويقية ناجحة، وقياس عائد الاستثمار التسويقي، والتكيف مع التطورات السريعة في عالم التسويق الرقمي.","course_objectives":"✅ إتقان استراتيجيات التسويق الرقمي الحديثة
✅ تطوير مهارات إدارة حملات وسائل التواصل الاجتماعي
✅ فهم تحليلات الويب وقياس الأداء التسويقي
✅ إكساب مهارات إنشاء المحتوى التسويقي
✅ تطبيق تقنيات تحسين محركات البحث SEO","target_audience":"✔️ مدراء وموظفي التسويق والمبيعات
✔️ أصحاب المشاريع والمتاجر الإلكترونية
✔️ المهتمين بريادة الأعمال الرقمية
✔️ موظفي وسائل التواصل الاجتماعي
✔️ الراغبين في تطوير مهارات التسويق الحديثة","price":"6000","time":"20","rating":"5","show_price":false}} 
[2025-05-28 07:55:11] local.INFO: معالجة صورة الدورة  
[2025-05-28 07:55:12] local.INFO: تم حفظ الصورة بنجاح {"image_name":"cP3ZlzLBJUIknX3KVOmE8GKs3BQdJxARl1S7AbAQ.jpg"} 
[2025-05-28 07:55:12] local.INFO: لم يتم رفع فيديو للدورة  
[2025-05-28 07:55:12] local.INFO: محاولة حفظ الدورة في قاعدة البيانات {"final_data":{"_token":"3oeJ53VC7oY5GULkEHgpNHRVui5vM4qeNy4IJYSR","_method":"post","name":"دورة احترافية في التسويق الرقمي – انطلق نحو النجاح الرقمي","url":"http://127.0.0.1:8000/dashboard/courses/create","categories_id":"3","Short_description":"دورة حديثة في التسويق الرقمي تغطي استراتيجيات التسويق الإلكتروني ووسائل التواصل الاجتماعي","description":"تقدم هذه الدورة التدريبية الحديثة المعرفة المتقدمة في مجال التسويق الرقمي، وتهدف إلى تأهيل المشاركين لاستخدام أحدث استراتيجيات التسويق الإلكتروني بفعالية.

تشمل الدورة موضوعات شاملة مثل إدارة وسائل التواصل الاجتماعي، والتسويق عبر محركات البحث، وإنشاء المحتوى الرقمي، وتحليل البيانات التسويقية، والتجارة الإلكترونية. كما تركز على فهم سلوك المستهلك الرقمي وبناء العلامة التجارية عبر الإنترنت.

يتم التدريب من خلال ورش عمل تطبيقية باستخدام أدوات التسويق الرقمي الفعلية، مع التركيز على الحالات العملية من السوق الجزائري والعربي. تؤهل الدورة المشاركين لإطلاق حملات تسويقية ناجحة، وقياس عائد الاستثمار التسويقي، والتكيف مع التطورات السريعة في عالم التسويق الرقمي.","course_objectives":"✅ إتقان استراتيجيات التسويق الرقمي الحديثة
✅ تطوير مهارات إدارة حملات وسائل التواصل الاجتماعي
✅ فهم تحليلات الويب وقياس الأداء التسويقي
✅ إكساب مهارات إنشاء المحتوى التسويقي
✅ تطبيق تقنيات تحسين محركات البحث SEO","target_audience":"✔️ مدراء وموظفي التسويق والمبيعات
✔️ أصحاب المشاريع والمتاجر الإلكترونية
✔️ المهتمين بريادة الأعمال الرقمية
✔️ موظفي وسائل التواصل الاجتماعي
✔️ الراغبين في تطوير مهارات التسويق الحديثة","price":"6000","time":"20","rating":"5","show_price":false,"image":"cP3ZlzLBJUIknX3KVOmE8GKs3BQdJxARl1S7AbAQ.jpg"}} 
[2025-05-28 07:55:12] local.ERROR: خطأ في إنشاء الدورة {"error":"SQLSTATE[HY000]: General error: 1364 Field 'demo_video' doesn't have a default value (SQL: insert into `courses` (`name`, `url`, `categories_id`, `Short_description`, `description`, `course_objectives`, `target_audience`, `price`, `time`, `rating`, `show_price`, `image`, `updated_at`, `created_at`) values (دورة احترافية في التسويق الرقمي – انطلق نحو النجاح الرقمي, http://127.0.0.1:8000/dashboard/courses/create, 3, دورة حديثة في التسويق الرقمي تغطي استراتيجيات التسويق الإلكتروني ووسائل التواصل الاجتماعي, تقدم هذه الدورة التدريبية الحديثة المعرفة المتقدمة في مجال التسويق الرقمي، وتهدف إلى تأهيل المشاركين لاستخدام أحدث استراتيجيات التسويق الإلكتروني بفعالية.

تشمل الدورة موضوعات شاملة مثل إدارة وسائل التواصل الاجتماعي، والتسويق عبر محركات البحث، وإنشاء المحتوى الرقمي، وتحليل البيانات التسويقية، والتجارة الإلكترونية. كما تركز على فهم سلوك المستهلك الرقمي وبناء العلامة التجارية عبر الإنترنت.

يتم التدريب من خلال ورش عمل تطبيقية باستخدام أدوات التسويق الرقمي الفعلية، مع التركيز على الحالات العملية من السوق الجزائري والعربي. تؤهل الدورة المشاركين لإطلاق حملات تسويقية ناجحة، وقياس عائد الاستثمار التسويقي، والتكيف مع التطورات السريعة في عالم التسويق الرقمي., ✅ إتقان استراتيجيات التسويق الرقمي الحديثة
✅ تطوير مهارات إدارة حملات وسائل التواصل الاجتماعي
✅ فهم تحليلات الويب وقياس الأداء التسويقي
✅ إكساب مهارات إنشاء المحتوى التسويقي
✅ تطبيق تقنيات تحسين محركات البحث SEO, ✔️ مدراء وموظفي التسويق والمبيعات
✔️ أصحاب المشاريع والمتاجر الإلكترونية
✔️ المهتمين بريادة الأعمال الرقمية
✔️ موظفي وسائل التواصل الاجتماعي
✔️ الراغبين في تطوير مهارات التسويق الحديثة, 6000, 20, 5, 0, cP3ZlzLBJUIknX3KVOmE8GKs3BQdJxARl1S7AbAQ.jpg, 2025-05-28 07:55:12, 2025-05-28 07:55:12))","file":"C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":671,"trace":"#0 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('insert into `co...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(465): Illuminate\\Database\\Connection->run('insert into `co...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(417): Illuminate\\Database\\Connection->statement('insert into `co...', Array)
#3 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `co...', Array)
#4 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2839): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `co...', Array, 'id')
#5 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1422): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(902): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(867): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(730): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(776): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(433): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Course))
#11 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(777): tap(Object(App\\Models\\Course), Object(Closure))
#12 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1736): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1748): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 C:\\xampp\\htdocs\\Academy\\app\\Http\\Controllers\\Dashboard\\CourseController.php(121): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\CourseController->store(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('store', Array)
#18 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\CourseController), 'store')
#19 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#20 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#21 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(62): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\Academy\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\Academy\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\Academy\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\Academy\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#61 {main}"} 
[2025-05-28 08:00:00] local.ERROR: Cannot declare class FixDemoVideoNullableInCoursesTable, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class FixDemoVideoNullableInCoursesTable, because the name is already in use at C:\\xampp\\htdocs\\Academy\\database\\migrations\\2025_05_28_080000_fix_demo_video_nullable_in_courses_table.php:34)
[stacktrace]
#0 {main}
"} 
[2025-05-28 08:47:43] local.INFO: المدربين المتاحين في النظام {"instructors_count":4,"instructors":{"Illuminate\\Support\\Collection":{"2":"Hassan","3":"Ali","4":"Ahemd","5":"بن جدو امينة"}}} 
[2025-05-28 08:50:37] local.INFO: المدربين المتاحين في النظام {"instructors_count":5,"instructors":{"Illuminate\\Support\\Collection":{"2":"Hassan","3":"Ali","4":"Ahemd","5":"بن جدو امينة","7":"الدكتور:بويمة لنور"}}} 
[2025-05-28 08:52:01] local.INFO: بداية إنشاء دورة جديدة {"user_id":1,"course_name":"دورة احترافية في التسويق الرقمي – انطلق نحو النجاح الرقمي","request_data":{"_token":"T566XyFcjBShOvgoUfHfzqCmnzkPwQZ2kepLwEl5","_method":"post","name":"دورة احترافية في التسويق الرقمي – انطلق نحو النجاح الرقمي","url":null,"categories_id":"3","Short_description":"دورة حديثة في التسويق الرقمي تغطي استراتيجيات التسويق الإلكتروني ووسائل التواصل الاجتماعي","description":"تقدم هذه الدورة التدريبية الحديثة المعرفة المتقدمة في مجال التسويق الرقمي، وتهدف إلى تأهيل المشاركين لاستخدام أحدث استراتيجيات التسويق الإلكتروني بفعالية.

تشمل الدورة موضوعات شاملة مثل إدارة وسائل التواصل الاجتماعي، والتسويق عبر محركات البحث، وإنشاء المحتوى الرقمي، وتحليل البيانات التسويقية، والتجارة الإلكترونية. كما تركز على فهم سلوك المستهلك الرقمي وبناء العلامة التجارية عبر الإنترنت.

يتم التدريب من خلال ورش عمل تطبيقية باستخدام أدوات التسويق الرقمي الفعلية، مع التركيز على الحالات العملية من السوق الجزائري والعربي. تؤهل الدورة المشاركين لإطلاق حملات تسويقية ناجحة، وقياس عائد الاستثمار التسويقي، والتكيف مع التطورات السريعة في عالم التسويق الرقمي.","course_objectives":"✅ إتقان استراتيجيات التسويق الرقمي الحديثة
✅ تطوير مهارات إدارة حملات وسائل التواصل الاجتماعي
✅ فهم تحليلات الويب وقياس الأداء التسويقي
✅ إكساب مهارات إنشاء المحتوى التسويقي
✅ تطبيق تقنيات تحسين محركات البحث SEO","target_audience":"✔️ مدراء وموظفي التسويق والمبيعات
✔️ أصحاب المشاريع والمتاجر الإلكترونية
✔️ المهتمين بريادة الأعمال الرقمية
✔️ موظفي وسائل التواصل الاجتماعي
✔️ الراغبين في تطوير مهارات التسويق الحديثة","instructors":["7"],"price":"6000","time":"20","rating":"5"}} 
[2025-05-28 08:52:01] local.INFO: بيانات الدورة بعد المعالجة {"request_data":{"_token":"T566XyFcjBShOvgoUfHfzqCmnzkPwQZ2kepLwEl5","_method":"post","name":"دورة احترافية في التسويق الرقمي – انطلق نحو النجاح الرقمي","url":null,"categories_id":"3","Short_description":"دورة حديثة في التسويق الرقمي تغطي استراتيجيات التسويق الإلكتروني ووسائل التواصل الاجتماعي","description":"تقدم هذه الدورة التدريبية الحديثة المعرفة المتقدمة في مجال التسويق الرقمي، وتهدف إلى تأهيل المشاركين لاستخدام أحدث استراتيجيات التسويق الإلكتروني بفعالية.

تشمل الدورة موضوعات شاملة مثل إدارة وسائل التواصل الاجتماعي، والتسويق عبر محركات البحث، وإنشاء المحتوى الرقمي، وتحليل البيانات التسويقية، والتجارة الإلكترونية. كما تركز على فهم سلوك المستهلك الرقمي وبناء العلامة التجارية عبر الإنترنت.

يتم التدريب من خلال ورش عمل تطبيقية باستخدام أدوات التسويق الرقمي الفعلية، مع التركيز على الحالات العملية من السوق الجزائري والعربي. تؤهل الدورة المشاركين لإطلاق حملات تسويقية ناجحة، وقياس عائد الاستثمار التسويقي، والتكيف مع التطورات السريعة في عالم التسويق الرقمي.","course_objectives":"✅ إتقان استراتيجيات التسويق الرقمي الحديثة
✅ تطوير مهارات إدارة حملات وسائل التواصل الاجتماعي
✅ فهم تحليلات الويب وقياس الأداء التسويقي
✅ إكساب مهارات إنشاء المحتوى التسويقي
✅ تطبيق تقنيات تحسين محركات البحث SEO","target_audience":"✔️ مدراء وموظفي التسويق والمبيعات
✔️ أصحاب المشاريع والمتاجر الإلكترونية
✔️ المهتمين بريادة الأعمال الرقمية
✔️ موظفي وسائل التواصل الاجتماعي
✔️ الراغبين في تطوير مهارات التسويق الحديثة","price":"6000","time":"20","rating":"5","show_price":false,"demo_video":null,"studant_count":"0"}} 
[2025-05-28 08:52:01] local.INFO: معالجة صورة الدورة  
[2025-05-28 08:52:01] local.INFO: تم حفظ الصورة بنجاح {"image_name":"oC22l6mAgXa3HE8JZMvUuczVR2Ga7dUydVEnNFCz.jpg"} 
[2025-05-28 08:52:01] local.INFO: لم يتم رفع فيديو للدورة - سيتم استخدام القيمة الافتراضية null  
[2025-05-28 08:52:01] local.INFO: محاولة حفظ الدورة في قاعدة البيانات {"final_data":{"_token":"T566XyFcjBShOvgoUfHfzqCmnzkPwQZ2kepLwEl5","_method":"post","name":"دورة احترافية في التسويق الرقمي – انطلق نحو النجاح الرقمي","url":null,"categories_id":"3","Short_description":"دورة حديثة في التسويق الرقمي تغطي استراتيجيات التسويق الإلكتروني ووسائل التواصل الاجتماعي","description":"تقدم هذه الدورة التدريبية الحديثة المعرفة المتقدمة في مجال التسويق الرقمي، وتهدف إلى تأهيل المشاركين لاستخدام أحدث استراتيجيات التسويق الإلكتروني بفعالية.

تشمل الدورة موضوعات شاملة مثل إدارة وسائل التواصل الاجتماعي، والتسويق عبر محركات البحث، وإنشاء المحتوى الرقمي، وتحليل البيانات التسويقية، والتجارة الإلكترونية. كما تركز على فهم سلوك المستهلك الرقمي وبناء العلامة التجارية عبر الإنترنت.

يتم التدريب من خلال ورش عمل تطبيقية باستخدام أدوات التسويق الرقمي الفعلية، مع التركيز على الحالات العملية من السوق الجزائري والعربي. تؤهل الدورة المشاركين لإطلاق حملات تسويقية ناجحة، وقياس عائد الاستثمار التسويقي، والتكيف مع التطورات السريعة في عالم التسويق الرقمي.","course_objectives":"✅ إتقان استراتيجيات التسويق الرقمي الحديثة
✅ تطوير مهارات إدارة حملات وسائل التواصل الاجتماعي
✅ فهم تحليلات الويب وقياس الأداء التسويقي
✅ إكساب مهارات إنشاء المحتوى التسويقي
✅ تطبيق تقنيات تحسين محركات البحث SEO","target_audience":"✔️ مدراء وموظفي التسويق والمبيعات
✔️ أصحاب المشاريع والمتاجر الإلكترونية
✔️ المهتمين بريادة الأعمال الرقمية
✔️ موظفي وسائل التواصل الاجتماعي
✔️ الراغبين في تطوير مهارات التسويق الحديثة","price":"6000","time":"20","rating":"5","show_price":false,"demo_video":null,"studant_count":"0","image":"oC22l6mAgXa3HE8JZMvUuczVR2Ga7dUydVEnNFCz.jpg"}} 
[2025-05-28 08:52:01] local.ERROR: خطأ في إنشاء الدورة {"error":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'url' cannot be null (SQL: insert into `courses` (`demo_video`, `image`, `studant_count`, `show_price`, `name`, `url`, `categories_id`, `Short_description`, `description`, `course_objectives`, `target_audience`, `price`, `time`, `rating`, `updated_at`, `created_at`) values (?, oC22l6mAgXa3HE8JZMvUuczVR2Ga7dUydVEnNFCz.jpg, 0, 0, دورة احترافية في التسويق الرقمي – انطلق نحو النجاح الرقمي, ?, 3, دورة حديثة في التسويق الرقمي تغطي استراتيجيات التسويق الإلكتروني ووسائل التواصل الاجتماعي, تقدم هذه الدورة التدريبية الحديثة المعرفة المتقدمة في مجال التسويق الرقمي، وتهدف إلى تأهيل المشاركين لاستخدام أحدث استراتيجيات التسويق الإلكتروني بفعالية.

تشمل الدورة موضوعات شاملة مثل إدارة وسائل التواصل الاجتماعي، والتسويق عبر محركات البحث، وإنشاء المحتوى الرقمي، وتحليل البيانات التسويقية، والتجارة الإلكترونية. كما تركز على فهم سلوك المستهلك الرقمي وبناء العلامة التجارية عبر الإنترنت.

يتم التدريب من خلال ورش عمل تطبيقية باستخدام أدوات التسويق الرقمي الفعلية، مع التركيز على الحالات العملية من السوق الجزائري والعربي. تؤهل الدورة المشاركين لإطلاق حملات تسويقية ناجحة، وقياس عائد الاستثمار التسويقي، والتكيف مع التطورات السريعة في عالم التسويق الرقمي., ✅ إتقان استراتيجيات التسويق الرقمي الحديثة
✅ تطوير مهارات إدارة حملات وسائل التواصل الاجتماعي
✅ فهم تحليلات الويب وقياس الأداء التسويقي
✅ إكساب مهارات إنشاء المحتوى التسويقي
✅ تطبيق تقنيات تحسين محركات البحث SEO, ✔️ مدراء وموظفي التسويق والمبيعات
✔️ أصحاب المشاريع والمتاجر الإلكترونية
✔️ المهتمين بريادة الأعمال الرقمية
✔️ موظفي وسائل التواصل الاجتماعي
✔️ الراغبين في تطوير مهارات التسويق الحديثة, 6000, 20, 5, 2025-05-28 08:52:01, 2025-05-28 08:52:01))","file":"C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":671,"trace":"#0 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('insert into `co...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(465): Illuminate\\Database\\Connection->run('insert into `co...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(417): Illuminate\\Database\\Connection->statement('insert into `co...', Array)
#3 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `co...', Array)
#4 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2839): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `co...', Array, 'id')
#5 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1422): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(902): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(867): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(730): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(776): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(433): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Course))
#11 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(777): tap(Object(App\\Models\\Course), Object(Closure))
#12 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1736): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1748): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 C:\\xampp\\htdocs\\Academy\\app\\Http\\Controllers\\Dashboard\\CourseController.php(132): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\CourseController->store(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('store', Array)
#18 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\CourseController), 'store')
#19 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#20 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#21 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(62): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\Academy\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\Academy\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\xampp\\htdocs\\Academy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\Academy\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\Academy\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#61 {main}"} 
[2025-05-28 08:52:02] local.INFO: المدربين المتاحين في النظام {"instructors_count":5,"instructors":{"Illuminate\\Support\\Collection":{"2":"Hassan","3":"Ali","4":"Ahemd","5":"بن جدو امينة","7":"الدكتور:بويمة لنور"}}} 
