@extends('layouts.dashboard.app')

@section('content')

<div class="content-wrapper">
    <section class="content-header">
        <h1 class="arabic-heading">
            <i class="fa fa-university"></i>
            معلومات المؤسسة
            <small>أكاديمية Leaders Vision</small>
        </h1>
        <ol class="breadcrumb">
            <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
            <li class="active">معلومات المؤسسة</li>
        </ol>
    </section>

    <section class="content">
        
        @if (session('success'))
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <i class="icon fa fa-check"></i>
                {{ session('success') }}
            </div>
        @endif

        <!-- معلومات أساسية -->
        <div class="row">
            <div class="col-md-8">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title arabic-text">
                            <i class="fa fa-info-circle"></i>
                            المعلومات الأساسية
                        </h3>
                        <div class="box-tools pull-right">
                            <a href="{{ route('dashboard.institution.edit') }}" class="btn btn-primary btn-sm">
                                <i class="fa fa-edit"></i> تعديل
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong class="arabic-text">الاسم بالعربية:</strong>
                                <p class="text-muted arabic-text">{{ $institution->name_ar }}</p>
                                
                                <strong class="arabic-text">الاسم بالإنجليزية:</strong>
                                <p class="text-muted">{{ $institution->name_en }}</p>
                                
                                <strong class="arabic-text">نوع المؤسسة:</strong>
                                <p class="text-muted arabic-text">{{ $institution->institution_type }}</p>
                                
                                <strong class="arabic-text">التخصص:</strong>
                                <p class="text-muted arabic-text">{{ $institution->specialization }}</p>
                            </div>
                            <div class="col-md-6">
                                <strong class="arabic-text">سنة التأسيس:</strong>
                                <p class="text-muted">{{ $institution->established_year }}</p>
                                
                                <strong class="arabic-text">العملة:</strong>
                                <p class="text-muted">{{ $institution->currency_name_ar }} ({{ $institution->currency_symbol }})</p>
                                
                                <strong class="arabic-text">الحالة:</strong>
                                <p class="text-muted">
                                    @if($institution->is_active)
                                        <span class="label label-success">نشط</span>
                                    @else
                                        <span class="label label-danger">غير نشط</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <strong class="arabic-text">الوصف:</strong>
                        <p class="text-muted arabic-text">{{ $institution->description_ar }}</p>
                        
                        <strong class="arabic-text">الشعار:</strong>
                        <p class="text-muted arabic-text">{{ $institution->slogan_ar }}</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <!-- الشعار -->
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title arabic-text">
                            <i class="fa fa-image"></i>
                            الشعار
                        </h3>
                    </div>
                    <div class="box-body text-center">
                        @if($institution->logo_path)
                            <img src="{{ asset($institution->logo_path) }}" alt="شعار المؤسسة" class="img-responsive" style="max-height: 150px; margin: 0 auto;">
                        @else
                            <p class="text-muted arabic-text">لا يوجد شعار</p>
                        @endif
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title arabic-text">
                            <i class="fa fa-bar-chart"></i>
                            الإحصائيات
                        </h3>
                        <div class="box-tools pull-right">
                            <button type="button" class="btn btn-success btn-sm" onclick="updateStatistics()">
                                <i class="fa fa-refresh"></i> تحديث
                            </button>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-xs-6">
                                <div class="description-block border-right">
                                    <span class="description-percentage text-green">
                                        <i class="fa fa-users"></i>
                                    </span>
                                    <h5 class="description-header">{{ number_format($institution->students_count) }}</h5>
                                    <span class="description-text arabic-text">طالب</span>
                                </div>
                            </div>
                            <div class="col-xs-6">
                                <div class="description-block">
                                    <span class="description-percentage text-blue">
                                        <i class="fa fa-book"></i>
                                    </span>
                                    <h5 class="description-header">{{ number_format($institution->courses_count) }}</h5>
                                    <span class="description-text arabic-text">دورة</span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-6">
                                <div class="description-block border-right">
                                    <span class="description-percentage text-yellow">
                                        <i class="fa fa-user-tie"></i>
                                    </span>
                                    <h5 class="description-header">{{ number_format($institution->trainers_count) }}</h5>
                                    <span class="description-text arabic-text">مدرب</span>
                                </div>
                            </div>
                            <div class="col-xs-6">
                                <div class="description-block">
                                    <span class="description-percentage text-red">
                                        <i class="fa fa-graduation-cap"></i>
                                    </span>
                                    <h5 class="description-header">{{ number_format($institution->graduates_count) }}</h5>
                                    <span class="description-text arabic-text">خريج</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="row">
            <div class="col-md-6">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title arabic-text">
                            <i class="fa fa-phone"></i>
                            معلومات الاتصال
                        </h3>
                    </div>
                    <div class="box-body">
                        <strong class="arabic-text">الهاتف الأول:</strong>
                        <p class="text-muted">
                            <a href="tel:{{ $institution->phone_1 }}">{{ $institution->phone_1 }}</a>
                        </p>
                        
                        @if($institution->phone_2)
                        <strong class="arabic-text">الهاتف الثاني:</strong>
                        <p class="text-muted">
                            <a href="tel:{{ $institution->phone_2 }}">{{ $institution->phone_2 }}</a>
                        </p>
                        @endif
                        
                        <strong class="arabic-text">البريد الإلكتروني:</strong>
                        <p class="text-muted">
                            <a href="mailto:{{ $institution->email }}">{{ $institution->email }}</a>
                        </p>
                        
                        @if($institution->website)
                        <strong class="arabic-text">الموقع الإلكتروني:</strong>
                        <p class="text-muted">
                            <a href="http://{{ $institution->website }}" target="_blank">{{ $institution->website }}</a>
                        </p>
                        @endif
                        
                        @if($institution->whatsapp)
                        <strong class="arabic-text">واتساب:</strong>
                        <p class="text-muted">
                            <a href="{{ $institution->whatsapp_url }}" target="_blank">{{ $institution->whatsapp }}</a>
                        </p>
                        @endif
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="box box-warning">
                    <div class="box-header with-border">
                        <h3 class="box-title arabic-text">
                            <i class="fa fa-map-marker"></i>
                            العنوان والموقع
                        </h3>
                    </div>
                    <div class="box-body">
                        <strong class="arabic-text">العنوان:</strong>
                        <p class="text-muted arabic-text">{{ $institution->address_ar }}</p>
                        
                        <strong class="arabic-text">المدينة:</strong>
                        <p class="text-muted arabic-text">{{ $institution->city }}</p>
                        
                        <strong class="arabic-text">الولاية:</strong>
                        <p class="text-muted arabic-text">{{ $institution->state }}</p>
                        
                        <strong class="arabic-text">البلد:</strong>
                        <p class="text-muted arabic-text">{{ $institution->country }}</p>
                        
                        @if($institution->latitude && $institution->longitude)
                        <strong class="arabic-text">الموقع على الخريطة:</strong>
                        <p class="text-muted">
                            <a href="{{ $institution->google_maps_url }}" target="_blank" class="btn btn-sm btn-primary">
                                <i class="fa fa-map"></i> عرض على خرائط جوجل
                            </a>
                        </p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- الخدمات وأوقات العمل -->
        <div class="row">
            <div class="col-md-6">
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title arabic-text">
                            <i class="fa fa-cogs"></i>
                            الخدمات المقدمة
                        </h3>
                    </div>
                    <div class="box-body">
                        @if($institution->services)
                            <ul class="list-unstyled">
                                @foreach($institution->services as $service)
                                    <li class="arabic-text">
                                        <i class="fa fa-check text-green"></i> {{ $service }}
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <p class="text-muted arabic-text">لا توجد خدمات محددة</p>
                        @endif
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title arabic-text">
                            <i class="fa fa-clock-o"></i>
                            أوقات العمل
                        </h3>
                    </div>
                    <div class="box-body">
                        @if($institution->working_hours)
                            <table class="table table-condensed">
                                @foreach($institution->working_hours as $day => $hours)
                                    <tr>
                                        <td class="arabic-text"><strong>{{ $day }}</strong></td>
                                        <td class="text-muted">{{ $hours }}</td>
                                    </tr>
                                @endforeach
                            </table>
                        @else
                            <p class="text-muted arabic-text">لا توجد أوقات عمل محددة</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

    </section>
</div>

@endsection

@push('scripts')
<script>
function updateStatistics() {
    $.ajax({
        url: '{{ route("dashboard.institution.statistics") }}',
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            }
        },
        error: function() {
            alert('حدث خطأ أثناء تحديث الإحصائيات');
        }
    });
}
</script>
@endpush
