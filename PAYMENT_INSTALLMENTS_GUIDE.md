# دليل نظام الدفعات بالتقسيط - Payment Installments
## Payment Installments System Guide

---

## ✅ **تم إنشاء نظام الدفعات بالتقسيط بنجاح!**

تم إنشاء نظام متكامل لإدارة دفعات المشتركين بالتقسيط مع تتبع دقيق لكل قسط.

---

## 🎯 **المبدأ الأساسي:**

### **قاعدة الدفع:**
- ✅ **المشترك لا يُعتبر "مدفوع" إلا عند إكمال المبلغ الكامل للدورة**
- ✅ **يمكن الدفع على أقساط متعددة**
- ✅ **تتبع دقيق لكل قسط برقم تسلسلي**
- ✅ **تحديث تلقائي لحالة المشترك عند اكتمال الدفع**
- ✅ **تحديد القسط الأخير تلقائياً**

---

## 🛠️ **المكونات المنشأة:**

### **1. جدول قاعدة البيانات:**
```sql
payment_installments
- id, purchase_id, amount, payment_date, payment_method
- receipt_number, receipt_image, transaction_id, notes
- received_by, status, installment_number, is_final_payment
- created_at, updated_at
```

### **2. Migration:**
```
database/migrations/2024_12_15_150000_create_payment_installments_table.php
```

### **3. الكونترولر المحدث:**
```
app/Http/Controllers/Dashboard/SimplePaymentController.php
```

### **4. الصفحات المحدثة:**
```
resources/views/dashboard/add-payment-simple.blade.php
resources/views/dashboard/purchases/show.blade.php
```

---

## 🌐 **كيفية الوصول:**

### **الطريقة الأولى - من قائمة الطلبات:**
1. **اذهب إلى**: http://localhost:8000/dashboard/purchases
2. **اضغط على الزر الأخضر** 🟢 (إضافة دفعة) بجانب أي طلب

### **الطريقة الثانية - من تفاصيل الطلب:**
1. **اضغط على الزر الأزرق** 🔵 (عرض التفاصيل)
2. **اضغط على "إضافة دفعة"** في صفحة التفاصيل

### **الطريقة الثالثة - الرابط المباشر:**
```
http://localhost:8000/dashboard/purchases/{ID}/add-payment
```

---

## 💰 **معلومات الأقساط:**

### **رقم القسط:**
- ✅ **يبدأ من 1** للقسط الأول
- ✅ **يزيد تلقائياً** مع كل دفعة جديدة
- ✅ **يظهر في قائمة الدفعات السابقة**

### **القسط الأخير:**
- ✅ **يُحدد تلقائياً** عند إكمال المبلغ
- ✅ **يُعلم بـ `is_final_payment = true`**
- ✅ **يُحدث حالة المشترك** إلى "مقبول"

### **معلومات إضافية:**
- ✅ **تاريخ كل قسط**
- ✅ **طريقة الدفع**
- ✅ **رقم الإيصال**
- ✅ **صورة الإيصال**
- ✅ **ملاحظات خاصة**
- ✅ **من استلم الدفعة**

---

## 📊 **المعلومات المعروضة:**

### **في صفحة إضافة الدفعة:**
- 👤 **معلومات المشترك** - الاسم، البريد، الهاتف، الدورة
- 💰 **معلومات الدفع** - السعر، المدفوع، المتبقي، التقدم
- 📊 **شريط التقدم** ملون حسب النسبة
- 📝 **نموذج إضافة قسط** شامل
- 📋 **الأقساط السابقة** مع أرقامها

### **في صفحة تفاصيل الطلب:**
- 📄 **معلومات الطالب** كاملة
- 💳 **معلومات الدفع** مع الإحصائيات
- 🖼️ **صورة الفاتورة** المرفقة
- ⚙️ **الإجراءات المتاحة**

---

## 🔄 **آلية العمل:**

### **عند إضافة قسط:**
1. ✅ **التحقق من وجود الجدول**
2. ✅ **حساب رقم القسط التالي**
3. ✅ **التحقق من صحة البيانات**
4. ✅ **التأكد من عدم تجاوز المبلغ المتبقي**
5. ✅ **تحديد إذا كان القسط الأخير**
6. ✅ **حفظ القسط في الجدول**
7. ✅ **تحديث حالة المشترك** إذا اكتمل الدفع

### **حساب رقم القسط:**
```php
$nextInstallmentNumber = DB::table('payment_installments')
                          ->where('purchase_id', $purchase->id)
                          ->max('installment_number') + 1;
```

### **تحديد القسط الأخير:**
```php
$newTotalPaid = $totalPaid + $request->amount;
$newRemaining = $coursePrice - $newTotalPaid;
$isFinalPayment = $newRemaining <= 0;
```

---

## 📈 **الإحصائيات والتقارير:**

### **في صفحة التفاصيل:**
- 🔵 **سعر الدورة** - 10,000 دج (افتراضي)
- 🟢 **المدفوع** - مجموع الأقساط المؤكدة
- 🟡 **المتبقي** - الفرق بين السعر والمدفوع
- 🔴 **التقدم** - نسبة مئوية مع شريط ملون

### **في قائمة الأقساط السابقة:**
- 💰 **مبلغ كل قسط**
- 📅 **تاريخ الدفع**
- 🔢 **رقم القسط**
- ✅ **حالة التأكيد**

---

## 🎨 **التصميم والألوان:**

### **الأزرار:**
- 🔵 **أزرق** - عرض التفاصيل
- 🟢 **أخضر** - إضافة قسط
- 🟡 **أصفر** - تعديل
- 🔴 **أحمر** - حذف

### **شريط التقدم:**
- 🔴 **أحمر** - أقل من 50%
- 🟡 **أصفر** - 50% إلى 99%
- 🟢 **أخضر** - 100% (مكتمل)

### **حالات الأقساط:**
- 🟢 **أخضر** - مؤكد
- 🟡 **أصفر** - في الانتظار
- 🔴 **أحمر** - مرفوض

---

## 🚀 **للاختبار:**

### **الخطوات:**
1. **تشغيل الـ migration**: `php artisan migrate`
2. **اذهب إلى**: http://localhost:8000/dashboard/purchases
3. **اضغط على الزر الأخضر** 🟢 بجانب أي طلب
4. **أدخل مبلغ القسط الأول** (مثال: 3000 دج)
5. **اختر طريقة الدفع** (مثال: نقداً)
6. **اضغط "إضافة الدفعة"**

### **النتيجة المتوقعة:**
- ✅ **رسالة نجاح** مع المبلغ المتبقي
- ✅ **تحديث شريط التقدم** إلى 30%
- ✅ **إضافة "القسط رقم 1"** للقائمة
- ✅ **تحديث الإحصائيات**

### **عند إضافة القسط الثاني (3000 دج):**
- ✅ **تحديث التقدم** إلى 60%
- ✅ **إضافة "القسط رقم 2"**

### **عند إضافة القسط الأخير (4000 دج):**
- 🎉 **رسالة "مدفوع بالكامل"**
- ✅ **تحديث حالة الطلب** إلى "مقبول"
- 🟢 **شريط تقدم أخضر** 100%
- ✅ **تعليم القسط كـ "القسط الأخير"**
- 🚫 **إخفاء زر "إضافة دفعة"**

---

## 🔧 **الإعدادات:**

### **سعر الدورة الافتراضي:**
```php
$coursePrice = 10000; // 10,000 دج
```

### **مجلد حفظ الصور:**
```
public/uploads/receipts/
```

### **حالات الأقساط:**
```php
'pending'   => 'في الانتظار'
'verified'  => 'تم التحقق'  (افتراضي)
'rejected'  => 'مرفوضة'
```

---

## 📊 **هيكل الجدول:**

### **الحقول الأساسية:**
- **id** - معرف القسط
- **purchase_id** - ربط مع الطلب
- **amount** - مبلغ القسط
- **payment_date** - تاريخ الدفع
- **payment_method** - طريقة الدفع

### **حقول الإيصال:**
- **receipt_number** - رقم الإيصال
- **receipt_image** - صورة الإيصال
- **transaction_id** - رقم المعاملة

### **حقول التتبع:**
- **installment_number** - رقم القسط
- **is_final_payment** - هل هو القسط الأخير
- **received_by** - من استلم الدفعة
- **status** - حالة القسط

### **حقول إضافية:**
- **notes** - ملاحظات
- **created_at** - تاريخ الإنشاء
- **updated_at** - تاريخ التحديث

---

## 🔍 **المميزات المتقدمة:**

### **التتبع الدقيق:**
- ✅ **رقم تسلسلي لكل قسط**
- ✅ **تحديد القسط الأخير تلقائياً**
- ✅ **تتبع من استلم كل دفعة**
- ✅ **ملاحظات مخصصة لكل قسط**

### **الأمان:**
- ✅ **التحقق من وجود الجدول**
- ✅ **حماية من تجاوز المبلغ**
- ✅ **تشفير البيانات الحساسة**
- ✅ **تسجيل جميع العمليات**

### **المرونة:**
- ✅ **عدد أقساط غير محدود**
- ✅ **مبالغ مختلفة لكل قسط**
- ✅ **طرق دفع متنوعة**
- ✅ **إرفاق إيصالات لكل قسط**

---

**تم إنشاء نظام دفعات بالتقسيط متكامل! 💰✨**

**المميزات الرئيسية:**
- ✅ **دفع بالتقسيط** مع ترقيم تلقائي
- ✅ **تتبع دقيق** لكل قسط
- ✅ **تحديد القسط الأخير** تلقائياً
- ✅ **تحديث حالة المشترك** عند الاكتمال
- ✅ **واجهة سهلة** ومتجاوبة
- ✅ **أمان عالي** وحماية البيانات

**يمكنك الآن:**
- 💰 **إضافة أقساط** للمشتركين بسهولة
- 📊 **متابعة التقدم** في الدفع
- 🔍 **عرض تفاصيل** كل قسط
- 📈 **مراقبة الإحصائيات** الحية
- ✅ **تأكيد اكتمال الدفع** تلقائياً
- 🔢 **تتبع أرقام الأقساط** بدقة
