<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Institution extends Model
{

    protected $fillable = [
        'name_ar',
        'name_en',
        'name_fr',
        'description_ar',
        'slogan_ar',
        'slogan_en',
        'phone_1',
        'phone_2',
        'email',
        'website',
        'address_ar',
        'address_en',
        'city',
        'state',
        'country',
        'postal_code',
        'latitude',
        'longitude',
        'facebook',
        'instagram',
        'twitter',
        'linkedin',
        'youtube',
        'whatsapp',
        'established_year',
        'license_number',
        'tax_number',
        'commercial_register',
        'currency',
        'currency_symbol',
        'currency_name_ar',
        'currency_name_en',
        'logo_path',
        'cover_image_path',
        'gallery',
        'institution_type',
        'specialization',
        'services',
        'working_hours',
        'students_count',
        'courses_count',
        'trainers_count',
        'graduates_count',
        'is_active',
        'is_featured'
    ];

    protected $casts = [
        'services' => 'array',
        'working_hours' => 'array',
        'gallery' => 'array',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'students_count' => 'integer',
        'courses_count' => 'integer',
        'trainers_count' => 'integer',
        'graduates_count' => 'integer'
    ];

    /**
     * Get the institution's full address
     */
    public function getFullAddressAttribute()
    {
        return $this->address_ar . ', ' . $this->city . ', ' . $this->state . ', ' . $this->country;
    }

    /**
     * Get the institution's logo URL
     */
    public function getLogoUrlAttribute()
    {
        return asset($this->logo_path);
    }

    /**
     * Get the institution's cover image URL
     */
    public function getCoverImageUrlAttribute()
    {
        return $this->cover_image_path ? asset($this->cover_image_path) : null;
    }

    /**
     * Get formatted phone numbers
     */
    public function getFormattedPhonesAttribute()
    {
        $phones = [$this->phone_1];
        if ($this->phone_2) {
            $phones[] = $this->phone_2;
        }
        return implode(' - ', $phones);
    }

    /**
     * Get Google Maps URL
     */
    public function getGoogleMapsUrlAttribute()
    {
        if ($this->latitude && $this->longitude) {
            return "https://www.google.com/maps?q={$this->latitude},{$this->longitude}";
        }
        return "https://www.google.com/maps/search/" . urlencode($this->full_address);
    }

    /**
     * Get WhatsApp URL
     */
    public function getWhatsappUrlAttribute()
    {
        $phone = str_replace(['+', ' ', '-'], '', $this->whatsapp);
        if (substr($phone, 0, 3) !== '213') {
            $phone = '213' . ltrim($phone, '0');
        }
        return "https://wa.me/{$phone}";
    }

    /**
     * Get the default institution instance
     */
    public static function getDefault()
    {
        return static::first() ?? static::create([
            'name_ar' => 'أكاديمية Leaders Vision',
            'name_en' => 'Leaders Vision Academy',
            'description_ar' => 'أكاديمية متخصصة في التكوينات المهنية والدراسات الاقتصادية',
            'slogan_ar' => 'نصنع قادة المستقبل بالعلم والتقنية',
            'slogan_en' => 'Building Future Leaders with Science and Technology',
            'phone_1' => '0774479525',
            'phone_2' => '0665657400',
            'email' => '<EMAIL>',
            'website' => 'www.leadersvision.academy',
            'address_ar' => 'عوين زريقة، شارع ب رقم 16',
            'address_en' => 'Aouin Zrika Street B No. 16',
            'city' => 'برج بوعريريج',
            'state' => 'برج بوعريريج',
            'country' => 'الجزائر',
            'latitude' => 36.0731,
            'longitude' => 4.7617,
            'facebook' => 'https://www.facebook.com/Leadersvisionacademy',
            'whatsapp' => '0774479525',
            'currency' => 'DZD',
            'currency_symbol' => 'د.ج',
            'currency_name_ar' => 'دينار جزائري',
            'currency_name_en' => 'Algerian Dinar',
            'logo_path' => 'images/logo.png',
            'institution_type' => 'مدرسة خاصة',
            'specialization' => 'التكوينات المهنية والدراسات الاقتصادية'
        ]);
    }

    /**
     * Update statistics
     */
    public function updateStatistics()
    {
        $this->update([
            'students_count' => \App\Models\User::whereRoleIs('admin')->count(),
            'courses_count' => \App\Models\Course::count(),
            'trainers_count' => \App\Models\User::whereRoleIs('coaches')->count(),
        ]);
    }
}
