<?php $__env->startSection('content'); ?>

<!-- Breadcrumbs Start -->
<div class="rs-breadcrumbs breadcrumbs-overlay">
    <div class="breadcrumbs-img">
        <img src="<?php echo e(asset('home_file/assets/images/breadcrumbs/2.jpg')); ?>" alt="Breadcrumbs Image">
    </div>
    <div class="breadcrumbs-text white-color">
        <h1 class="page-title text-center">التسجيل في الدورة</h1>
        <ul class="text-center">
            <li>التسجيل في الدورة</li> //
            <li>
                <a class="active" href="/">الرئسيه</a>
            </li>
        </ul>
    </div>
</div>
<!-- Breadcrumbs End -->
<!-- My Account Section Start -->
<section class="register-section pt-100 pb-100 loaded bg-hidden-bits-o">

    <div class="container">
        <div class="register-box">

            <div class="sec-title text-center mb-30">
                <h2 class="title mb-10 text-light text-center">فورم التسجيل</h2>
            </div>

            <!-- Login Form -->
            <div class="styled-form">
            	<?php if(session('error')): ?>
					<div class="alert alert-danger"><?php echo e(session('error')); ?></div>
				<?php endif; ?>
                <form method="post" action="<?php echo e(route('Purchase.store')); ?>" enctype="multipart/form-data">

                		<?php echo e(csrf_field()); ?>

                        <?php echo e(method_field('post')); ?>


                    <div class="row clearfix">
                        <!-- Form Group -->
                        <div class="form-group col-lg-12">
                            <input class="bg-transparent text-light" type="text" value="<?php echo e(auth()->user()->name); ?>" id="Name" name="first_name" readonly="" value="" placeholder="الاسم الاول" required="">
                        </div>

                        <!-- Form Group -->
                        <div class="form-group col-lg-12">
                            <input class="bg-transparent text-light" type="text" id="last" name="last_name" value="" placeholder="الاسم الثاني" required="">
                        </div>

                        <!-- Form Group -->
                        <div class="form-group col-lg-12">
                            <input class="bg-transparent text-light" type="email" value="<?php echo e(auth()->user()->email); ?>" id="email" name="email" value="" readonly="" placeholder="الاميل" required="">
                        </div>

                        <!-- Form Group -->
                        <div class="form-group col-lg-12">
                            <input class="bg-transparent text-light" type="tel" name="phone" value="" placeholder="رقم الهاتف" required=""
                            size="20" minlength="9" maxlength="14">
                        </div>
                        <!-- Form Group -->
                        <div class="form-group col-lg-12">
                            <input class="bg-transparent text-light new_Btn" type="file" name="bill_image" id="user" name="file" value="" placeholder="رقم الهاتف" required="">
                        </div>
                        <!-- Form Group -->
                        <div class="form-group col-lg-12">
                            <input class="bg-transparent text-light" type="text" id="puser" name="name_course" value="<?php echo e($purchase_course->name); ?>" placeholder="اسم الدورة" required="" readonly="">
                        </div>

                        <div class="form-group col-lg-12">
                            <input type="text" name="course_id" value="<?php echo e($purchase_course->id); ?>" required="" readonly="" hidden>
                        </div>
                        <div class="form-group col-lg-12">
                            <input type="text" name="users_id" value="<?php echo e(auth()->user()->id); ?>" required="" readonly="" hidden >
                        </div>
                        <!-- Form Group -->

                        <div class="form-group col-lg-12 col-md-12 col-sm-12 text-center">
                            <button type="submit" class="btn btn-dark col-12"><span class="txt">اضافه</span></button>
                        </div>

                    </div>

                </form>
            </div>

        </div>
    </div>
</section>

<!-- My Account Section End -->

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.home.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Academy\resources\views/home/<USER>/ ?>