<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AIContentService
{
    private $apiKey;
    private $baseUrl;

    public function __construct()
    {
        // يمكن استخدام OpenAI أو أي API آخر
        $this->apiKey = env('OPENAI_API_KEY', 'demo-key');
        $this->baseUrl = 'https://api.openai.com/v1/chat/completions';
    }

    /**
     * توليد الوصف المختصر للدورة
     */
    public function generateShortDescription($courseName, $category = null)
    {
        $prompt = $this->buildShortDescriptionPrompt($courseName, $category);

        try {
            $response = $this->callAI($prompt);
            return $this->cleanResponse($response);
        } catch (\Exception $e) {
            Log::error('AI Short Description Error: ' . $e->getMessage());
            return $this->getFallbackShortDescription($courseName);
        }
    }

    /**
     * توليد أهداف الدورة
     */
    public function generateCourseObjectives($courseName, $description = null, $category = null)
    {
        $prompt = $this->buildObjectivesPrompt($courseName, $description, $category);

        try {
            $response = $this->callAI($prompt);
            return $this->cleanResponse($response);
        } catch (\Exception $e) {
            Log::error('AI Objectives Error: ' . $e->getMessage());
            return $this->getFallbackObjectives($courseName);
        }
    }

    /**
     * توليد الفئة المستهدفة
     */
    public function generateTargetAudience($courseName, $description = null, $category = null)
    {
        $prompt = $this->buildTargetAudiencePrompt($courseName, $description, $category);

        try {
            $response = $this->callAI($prompt);
            return $this->cleanResponse($response);
        } catch (\Exception $e) {
            Log::error('AI Target Audience Error: ' . $e->getMessage());
            return $this->getFallbackTargetAudience($courseName);
        }
    }

    /**
     * بناء prompt للوصف المختصر
     */
    private function buildShortDescriptionPrompt($courseName, $category)
    {
        $categoryText = $category ? "في مجال {$category}" : "";

        return "اكتب وصفاً مختصراً وجذاباً لدورة تدريبية بعنوان '{$courseName}' {$categoryText}.
                الوصف يجب أن يكون:
                - لا يتجاوز 120 حرف
                - باللغة العربية
                - جذاب ومحفز للتسجيل
                - يركز على الفوائد الرئيسية
                - مناسب لأكاديمية Leaders Vision للتدريب المهني

                اكتب الوصف فقط بدون أي إضافات:";
    }

    /**
     * بناء prompt لأهداف الدورة
     */
    private function buildObjectivesPrompt($courseName, $description, $category)
    {
        return "بناءً على عنوان الدورة التدريبية '{$courseName}' فقط، اكتب أهداف تعليمية واضحة ومحددة.

                الأهداف يجب أن تكون:
                - 4-6 أهداف محددة وقابلة للقياس
                - تبدأ كل هدف بـ ✅
                - باللغة العربية
                - تستنتج من عنوان الدورة المهارات والمعرفة المطلوبة
                - مناسبة للتدريب المهني في الجزائر
                - تركز على النتائج العملية والتطبيقية

                اكتب الأهداف فقط:";
    }

    /**
     * بناء prompt للفئة المستهدفة
     */
    private function buildTargetAudiencePrompt($courseName, $description, $category)
    {
        return "بناءً على عنوان الدورة التدريبية '{$courseName}' فقط، حدد الفئة المستهدفة المناسبة.

                الفئة المستهدفة يجب أن تشمل:
                - 4-6 فئات مختلفة
                - تبدأ كل فئة بـ ✔️
                - باللغة العربية
                - محددة ودقيقة حسب طبيعة الدورة
                - تشمل المستويات المهنية المختلفة
                - مناسبة للسوق الجزائري
                - تستنتج من عنوان الدورة من يحتاج هذا التدريب

                اكتب الفئات المستهدفة فقط:";
    }

    /**
     * استدعاء API الذكاء الاصطناعي
     */
    private function callAI($prompt)
    {
        // إذا لم يكن هناك API key، استخدم الاستجابة التجريبية
        if ($this->apiKey === 'demo-key') {
            return $this->getDemoResponse($prompt);
        }

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ])->timeout(30)->post($this->baseUrl, [
            'model' => 'gpt-3.5-turbo',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'أنت خبير في التدريب المهني والتعليم في الجزائر. تكتب محتوى تعليمي باللغة العربية لأكاديمية Leaders Vision.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => 300,
            'temperature' => 0.7,
        ]);

        if ($response->successful()) {
            return $response->json()['choices'][0]['message']['content'];
        }

        throw new \Exception('AI API Error: ' . $response->body());
    }

    /**
     * استجابة تجريبية للاختبار
     */
    private function getDemoResponse($prompt)
    {
        // استخراج اسم الدورة من الـ prompt
        preg_match("/'([^']+)'/", $prompt, $matches);
        $courseName = isset($matches[1]) ? $matches[1] : 'الدورة التدريبية';

        if (strpos($prompt, 'وصفاً مختصراً') !== false) {
            return $this->generateSmartShortDescription($courseName);
        }

        if (strpos($prompt, 'أهداف تعليمية') !== false) {
            return $this->generateSmartObjectives($courseName);
        }

        if (strpos($prompt, 'الفئة المستهدفة') !== false) {
            return $this->generateSmartTargetAudience($courseName);
        }

        return "محتوى تم توليده بواسطة الذكاء الاصطناعي";
    }

    /**
     * توليد وصف ذكي بناءً على عنوان الدورة
     */
    private function generateSmartShortDescription($courseName)
    {
        $courseName = strtolower($courseName);

        if (strpos($courseName, 'محاسب') !== false || strpos($courseName, 'مالي') !== false) {
            return "دورة متخصصة في المحاسبة المالية تهدف إلى إكساب المشاركين مهارات إعداد القوائم المالية والتحليل المحاسبي";
        }

        if (strpos($courseName, 'مشتريات') !== false || strpos($courseName, 'مخازن') !== false) {
            return "دورة شاملة في إدارة المشتريات والمخازن لتطوير مهارات التخطيط والتنفيذ وإدارة سلسلة التوريد بكفاءة";
        }

        if (strpos($courseName, 'موارد بشرية') !== false || strpos($courseName, 'hr') !== false) {
            return "دورة متقدمة في إدارة الموارد البشرية تركز على استراتيجيات التوظيف والتطوير وإدارة الأداء";
        }

        if (strpos($courseName, 'تسويق') !== false || strpos($courseName, 'رقمي') !== false) {
            return "دورة حديثة في التسويق الرقمي تغطي استراتيجيات التسويق الإلكتروني ووسائل التواصل الاجتماعي";
        }

        if (strpos($courseName, 'إدارة') !== false || strpos($courseName, 'قيادة') !== false) {
            return "دورة تطويرية في مهارات الإدارة والقيادة لبناء قدرات المدراء في اتخاذ القرارات وإدارة الفرق";
        }

        // وصف عام للدورات الأخرى
        return "دورة تدريبية متخصصة تهدف إلى تطوير المهارات المهنية وتعزيز القدرات العملية في مجال التخصص";
    }

    /**
     * توليد أهداف ذكية بناءً على عنوان الدورة
     */
    private function generateSmartObjectives($courseName)
    {
        $courseName = strtolower($courseName);

        if (strpos($courseName, 'محاسب') !== false || strpos($courseName, 'مالي') !== false) {
            return "✅ إتقان مبادئ المحاسبة المالية والمعايير المحاسبية\n✅ تطوير مهارات إعداد القوائم المالية والميزانيات\n✅ فهم أساليب التحليل المالي وتقييم الأداء\n✅ إكساب مهارات استخدام البرامج المحاسبية\n✅ تطبيق مبادئ الرقابة الداخلية والمراجعة";
        }

        if (strpos($courseName, 'مشتريات') !== false || strpos($courseName, 'مخازن') !== false) {
            return "✅ إتقان استراتيجيات التخطيط للمشتريات وإدارة المخزون\n✅ تطوير مهارات التفاوض مع الموردين وإدارة العقود\n✅ فهم أنظمة إدارة سلسلة التوريد الحديثة\n✅ إكساب مهارات تحليل التكاليف وتحسين الكفاءة\n✅ تطبيق معايير الجودة والسلامة في المخازن";
        }

        if (strpos($courseName, 'موارد بشرية') !== false || strpos($courseName, 'hr') !== false) {
            return "✅ إتقان استراتيجيات التوظيف والاختيار الفعال\n✅ تطوير مهارات تقييم الأداء وإدارة المواهب\n✅ فهم قوانين العمل والعلاقات الصناعية\n✅ إكساب مهارات التدريب والتطوير المهني\n✅ تطبيق أنظمة الحوافز والمكافآت";
        }

        if (strpos($courseName, 'تسويق') !== false || strpos($courseName, 'رقمي') !== false) {
            return "✅ إتقان استراتيجيات التسويق الرقمي الحديثة\n✅ تطوير مهارات إدارة حملات وسائل التواصل الاجتماعي\n✅ فهم تحليلات الويب وقياس الأداء التسويقي\n✅ إكساب مهارات إنشاء المحتوى التسويقي\n✅ تطبيق تقنيات تحسين محركات البحث SEO";
        }

        if (strpos($courseName, 'إدارة') !== false || strpos($courseName, 'قيادة') !== false) {
            return "✅ إتقان مهارات القيادة الفعالة وإدارة الفرق\n✅ تطوير قدرات اتخاذ القرارات الاستراتيجية\n✅ فهم أساليب التخطيط والتنظيم الإداري\n✅ إكساب مهارات التواصل والتفاوض\n✅ تطبيق مبادئ إدارة التغيير والتطوير";
        }

        // أهداف عامة للدورات الأخرى
        return "✅ إكساب المشاركين المهارات الأساسية في المجال\n✅ تطوير القدرات العملية والتطبيقية\n✅ تعزيز مهارات حل المشكلات واتخاذ القرارات\n✅ بناء الثقة المهنية والشخصية\n✅ تحسين مهارات التواصل والعمل الجماعي";
    }

    /**
     * توليد فئة مستهدفة ذكية بناءً على عنوان الدورة
     */
    private function generateSmartTargetAudience($courseName)
    {
        $courseName = strtolower($courseName);

        if (strpos($courseName, 'محاسب') !== false || strpos($courseName, 'مالي') !== false) {
            return "✔️ المحاسبين والمراجعين في القطاعين العام والخاص\n✔️ خريجي كليات التجارة والاقتصاد\n✔️ موظفي الإدارات المالية والمحاسبية\n✔️ أصحاب المشاريع الراغبين في فهم الجوانب المالية\n✔️ المهتمين بالحصول على شهادات مهنية في المحاسبة";
        }

        if (strpos($courseName, 'مشتريات') !== false || strpos($courseName, 'مخازن') !== false) {
            return "✔️ مدراء ومشرفي المشتريات والمخازن\n✔️ موظفي إدارة سلسلة التوريد واللوجستيات\n✔️ المسؤولين عن إدارة المخزون في الشركات\n✔️ أصحاب المشاريع التجارية والصناعية\n✔️ الراغبين في العمل في مجال إدارة المشتريات";
        }

        if (strpos($courseName, 'موارد بشرية') !== false || strpos($courseName, 'hr') !== false) {
            return "✔️ مدراء وموظفي الموارد البشرية\n✔️ المدراء والمشرفين في جميع الأقسام\n✔️ خريجي علم النفس وإدارة الأعمال\n✔️ أصحاب المشاريع والشركات الناشئة\n✔️ المهتمين بتطوير مهارات إدارة الأفراد";
        }

        if (strpos($courseName, 'تسويق') !== false || strpos($courseName, 'رقمي') !== false) {
            return "✔️ مدراء وموظفي التسويق والمبيعات\n✔️ أصحاب المشاريع والمتاجر الإلكترونية\n✔️ المهتمين بريادة الأعمال الرقمية\n✔️ موظفي وسائل التواصل الاجتماعي\n✔️ الراغبين في تطوير مهارات التسويق الحديثة";
        }

        if (strpos($courseName, 'إدارة') !== false || strpos($courseName, 'قيادة') !== false) {
            return "✔️ المدراء والمشرفين في جميع المستويات\n✔️ رؤساء الأقسام والفرق\n✔️ الموظفين المرشحين للترقية لمناصب إدارية\n✔️ أصحاب المشاريع والشركات\n✔️ المهتمين بتطوير مهارات القيادة";
        }

        // فئة عامة للدورات الأخرى
        return "✔️ الخريجين الجدد الباحثين عن فرص عمل\n✔️ الموظفين الراغبين في تطوير مهاراتهم\n✔️ المدراء والمشرفين في المؤسسات\n✔️ أصحاب المشاريع الصغيرة والمتوسطة\n✔️ المهتمين بالتطوير المهني والشخصي";
    }

    /**
     * تنظيف الاستجابة
     */
    private function cleanResponse($response)
    {
        return trim(strip_tags($response));
    }

    /**
     * استجابات احتياطية
     */
    private function getFallbackShortDescription($courseName)
    {
        return "دورة تدريبية متخصصة في {$courseName} تهدف إلى تطوير المهارات المهنية وتعزيز القدرات العملية";
    }

    private function getFallbackObjectives($courseName)
    {
        return "✅ إكساب المشاركين المهارات الأساسية في {$courseName}\n✅ تطوير القدرات العملية والتطبيقية\n✅ تعزيز مهارات حل المشكلات\n✅ بناء الثقة المهنية";
    }

    private function getFallbackTargetAudience($courseName)
    {
        return "✔️ الخريجين الجدد\n✔️ الموظفين الراغبين في التطوير\n✔️ المدراء والمشرفين\n✔️ أصحاب المشاريع";
    }
}
