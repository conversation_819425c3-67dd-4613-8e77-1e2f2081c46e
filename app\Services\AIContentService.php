<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AIContentService
{
    private $apiKey;
    private $baseUrl;

    public function __construct()
    {
        // يمكن استخدام OpenAI أو أي API آخر
        $this->apiKey = env('OPENAI_API_KEY', 'demo-key');
        $this->baseUrl = 'https://api.openai.com/v1/chat/completions';
    }

    /**
     * توليد الوصف المختصر للدورة
     */
    public function generateShortDescription($courseName, $category = null)
    {
        $prompt = $this->buildShortDescriptionPrompt($courseName, $category);

        try {
            $response = $this->callAI($prompt);
            return $this->cleanResponse($response);
        } catch (\Exception $e) {
            Log::error('AI Short Description Error: ' . $e->getMessage());
            return $this->getFallbackShortDescription($courseName);
        }
    }

    /**
     * توليد أهداف الدورة
     */
    public function generateCourseObjectives($courseName, $description = null, $category = null)
    {
        $prompt = $this->buildObjectivesPrompt($courseName, $description, $category);

        try {
            $response = $this->callAI($prompt);
            return $this->cleanResponse($response);
        } catch (\Exception $e) {
            Log::error('AI Objectives Error: ' . $e->getMessage());
            return $this->getFallbackObjectives($courseName);
        }
    }

    /**
     * توليد الفئة المستهدفة
     */
    public function generateTargetAudience($courseName, $description = null, $category = null)
    {
        $prompt = $this->buildTargetAudiencePrompt($courseName, $description, $category);

        try {
            $response = $this->callAI($prompt);
            return $this->cleanResponse($response);
        } catch (\Exception $e) {
            Log::error('AI Target Audience Error: ' . $e->getMessage());
            return $this->getFallbackTargetAudience($courseName);
        }
    }

    /**
     * بناء prompt للوصف المختصر
     */
    private function buildShortDescriptionPrompt($courseName, $category)
    {
        return "بناءً على عنوان الدورة التدريبية '{$courseName}' فقط، اكتب وصفاً مختصراً وجذاباً.
                الوصف يجب أن يكون:
                - لا يتجاوز 120 حرف
                - باللغة العربية
                - جذاب ومحفز للتسجيل
                - يستنتج المحتوى من العنوان
                - مناسب لأكاديمية Leaders Vision للتدريب المهني في الجزائر

                اكتب الوصف فقط بدون أي إضافات:";
    }

    /**
     * بناء prompt لأهداف الدورة
     */
    private function buildObjectivesPrompt($courseName, $description, $category)
    {
        $descText = $description ? "وصف الدورة: {$description}" : "";
        $categoryText = $category ? "المجال: {$category}" : "";

        return "اكتب أهداف تعليمية واضحة لدورة '{$courseName}'. {$descText} {$categoryText}

                الأهداف يجب أن تكون:
                - 4-6 أهداف محددة وقابلة للقياس
                - تبدأ كل هدف بـ ✅
                - باللغة العربية
                - تركز على المهارات والمعرفة المكتسبة
                - مناسبة للتدريب المهني

                اكتب الأهداف فقط:";
    }

    /**
     * بناء prompt للفئة المستهدفة
     */
    private function buildTargetAudiencePrompt($courseName, $description, $category)
    {
        $descText = $description ? "وصف الدورة: {$description}" : "";
        $categoryText = $category ? "المجال: {$category}" : "";

        return "حدد الفئة المستهدفة لدورة '{$courseName}'. {$descText} {$categoryText}

                الفئة المستهدفة يجب أن تشمل:
                - 3-5 فئات مختلفة
                - تبدأ كل فئة بـ ✔️
                - باللغة العربية
                - محددة ودقيقة
                - تشمل المستويات المهنية المختلفة

                اكتب الفئات المستهدفة فقط:";
    }

    /**
     * استدعاء API الذكاء الاصطناعي
     */
    private function callAI($prompt)
    {
        // إذا لم يكن هناك API key، استخدم الاستجابة التجريبية
        if ($this->apiKey === 'demo-key') {
            return $this->getDemoResponse($prompt);
        }

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ])->timeout(30)->post($this->baseUrl, [
            'model' => 'gpt-3.5-turbo',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'أنت خبير في التدريب المهني والتعليم في الجزائر. تكتب محتوى تعليمي باللغة العربية لأكاديمية Leaders Vision.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => 300,
            'temperature' => 0.7,
        ]);

        if ($response->successful()) {
            return $response->json()['choices'][0]['message']['content'];
        }

        throw new \Exception('AI API Error: ' . $response->body());
    }

    /**
     * استجابة تجريبية للاختبار
     */
    private function getDemoResponse($prompt)
    {
        if (strpos($prompt, 'وصفاً مختصراً') !== false) {
            return "دورة تدريبية شاملة تهدف إلى تطوير المهارات المهنية وتعزيز القدرات العملية للمشاركين في بيئة عمل متطورة";
        }

        if (strpos($prompt, 'أهداف تعليمية') !== false) {
            return "✅ إكساب المشاركين المهارات الأساسية في المجال\n✅ تطوير القدرات العملية والتطبيقية\n✅ تعزيز مهارات حل المشكلات واتخاذ القرارات\n✅ بناء الثقة المهنية والشخصية\n✅ تحسين مهارات التواصل والعمل الجماعي";
        }

        if (strpos($prompt, 'الفئة المستهدفة') !== false) {
            return "✔️ الخريجين الجدد الباحثين عن فرص عمل\n✔️ الموظفين الراغبين في تطوير مهاراتهم\n✔️ المدراء والمشرفين في المؤسسات\n✔️ أصحاب المشاريع الصغيرة والمتوسطة\n✔️ المهتمين بالتطوير المهني والشخصي";
        }

        return "محتوى تم توليده بواسطة الذكاء الاصطناعي";
    }

    /**
     * تنظيف الاستجابة
     */
    private function cleanResponse($response)
    {
        return trim(strip_tags($response));
    }

    /**
     * استجابات احتياطية
     */
    private function getFallbackShortDescription($courseName)
    {
        return "دورة تدريبية متخصصة في {$courseName} تهدف إلى تطوير المهارات المهنية وتعزيز القدرات العملية";
    }

    private function getFallbackObjectives($courseName)
    {
        return "✅ إكساب المشاركين المهارات الأساسية في {$courseName}\n✅ تطوير القدرات العملية والتطبيقية\n✅ تعزيز مهارات حل المشكلات\n✅ بناء الثقة المهنية";
    }

    private function getFallbackTargetAudience($courseName)
    {
        return "✔️ الخريجين الجدد\n✔️ الموظفين الراغبين في التطوير\n✔️ المدراء والمشرفين\n✔️ أصحاب المشاريع";
    }
}
