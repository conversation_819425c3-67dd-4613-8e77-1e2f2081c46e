@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">
        <section class="content-header">
            <h1>إدارة اشتراكات الطلاب والدفعات</h1>
            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
                <li class="active">اشتراكات الطلاب</li>
            </ol>
        </section>

        <section class="content">

            <!-- إحصائيات سريعة -->
            <div class="row">
                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-aqua">
                        <div class="inner">
                            <h3>{{ $stats['total'] }}</h3>
                            <p>إجمالي الاشتراكات</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-users"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-green">
                        <div class="inner">
                            <h3>{{ number_format($stats['paid_amount'], 2) }}</h3>
                            <p>المبلغ المحصل (دج)</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-money"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-yellow">
                        <div class="inner">
                            <h3>{{ number_format($stats['remaining_amount'], 2) }}</h3>
                            <p>المبلغ المتبقي (دج)</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-clock-o"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-red">
                        <div class="inner">
                            <h3>{{ $stats['overdue'] }}</h3>
                            <p>دفعات متأخرة</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-warning"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات السريعة -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">إجراءات سريعة</h3>
                        </div>
                        <div class="box-body">
                            <a href="{{ route('dashboard.payments.create') }}" class="btn btn-primary">
                                <i class="fa fa-plus"></i> إضافة اشتراك جديد
                            </a>
                            <a href="{{ route('dashboard.subscriptions') }}" class="btn btn-info">
                                <i class="fa fa-dashboard"></i> متابعة الاشتراكات
                            </a>
                            <a href="{{ route('dashboard.payments.overdue') }}" class="btn btn-warning">
                                <i class="fa fa-warning"></i> الدفعات المتأخرة ({{ $stats['overdue'] }})
                            </a>
                            <a href="{{ route('dashboard.payments.due-today') }}" class="btn btn-default">
                                <i class="fa fa-calendar"></i> مستحقة اليوم
                            </a>
                            <a href="{{ route('dashboard.payments.statistics') }}" class="btn btn-success">
                                <i class="fa fa-bar-chart"></i> الإحصائيات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">البحث والفلترة</h3>
                        </div>
                        <div class="box-body">
                            <form method="GET" action="{{ route('dashboard.payments.index') }}">
                                <div class="row">
                                    <div class="col-md-3">
                                        <input type="text" name="search" class="form-control"
                                               placeholder="البحث..." value="{{ request('search') }}">
                                    </div>
                                    <div class="col-md-2">
                                        <select name="status" class="form-control">
                                            <option value="">جميع الحالات</option>
                                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                                            <option value="partial" {{ request('status') == 'partial' ? 'selected' : '' }}>دفع جزئي</option>
                                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select name="course_id" class="form-control">
                                            <option value="">جميع الدورات</option>
                                            @foreach($courses as $course)
                                                <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                                    {{ $course->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label>
                                            <input type="checkbox" name="overdue" value="1" {{ request('overdue') ? 'checked' : '' }}>
                                            متأخرة فقط
                                        </label>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-search"></i> بحث
                                        </button>
                                        <a href="{{ route('dashboard.payments.index') }}" class="btn btn-default">
                                            <i class="fa fa-refresh"></i>
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الدفعات -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">قائمة اشتراكات الطلاب</h3>
                        </div>
                        <div class="box-body table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الطالب</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الدورة</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>المدفوع</th>
                                        <th>المتبقي</th>
                                        <th>التقدم</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($payments as $payment)
                                        <tr class="{{ $payment->is_overdue ? 'danger' : '' }}">
                                            <td>{{ $payment->id }}</td>
                                            <td>
                                                <strong>{{ $payment->student_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $payment->student_phone }}</small>
                                            </td>
                                            <td>{{ $payment->student_email }}</td>
                                            <td>
                                                <span class="label label-info">{{ $payment->course_name }}</span>
                                            </td>
                                            <td>{{ number_format($payment->total_amount, 2) }} دج</td>
                                            <td>{{ number_format($payment->paid_amount, 2) }} دج</td>
                                            <td>{{ number_format($payment->remaining_amount, 2) }} دج</td>
                                            <td>
                                                <div class="progress progress-xs">
                                                    <div class="progress-bar progress-bar-{{ $payment->payment_progress >= 100 ? 'success' : ($payment->payment_progress >= 50 ? 'warning' : 'danger') }}"
                                                         style="width: {{ $payment->payment_progress }}%"></div>
                                                </div>
                                                <span class="badge">{{ $payment->payment_progress }}%</span>
                                            </td>
                                            <td>
                                                <span class="label label-{{ $payment->payment_status == 'completed' ? 'success' : ($payment->payment_status == 'partial' ? 'warning' : 'danger') }}">
                                                    {{ $payment->payment_status_arabic }}
                                                </span>
                                                @if($payment->is_overdue)
                                                    <br><small class="text-danger">متأخر {{ $payment->days_overdue }} يوم</small>
                                                @endif
                                            </td>
                                            <td>{{ $payment->enrollment_date->format('Y-m-d') }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown">
                                                        إجراءات <span class="caret"></span>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a href="{{ route('dashboard.payments.show', $payment) }}">
                                                                <i class="fa fa-eye"></i> عرض التفاصيل
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="{{ route('dashboard.payments.edit', $payment) }}">
                                                                <i class="fa fa-edit"></i> تعديل
                                                            </a>
                                                        </li>
                                                        <li class="divider"></li>
                                                        <li>
                                                            <a href="#" class="text-danger">
                                                                <i class="fa fa-trash"></i> حذف
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="11" class="text-center">لا توجد اشتراكات</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                        <div class="box-footer">
                            {{ $payments->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
            </div>

        </section>
    </div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تحديث تلقائي للصفحة كل 5 دقائق
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
@endpush
