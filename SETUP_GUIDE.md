# دليل تشغيل أكاديمية Leaders Vision
## Leaders Vision Academy Setup Guide

---

## 🚀 **متطلبات النظام**

### **البرامج المطلوبة:**
- **PHP**: الإصدار 7.2.5 أو أحدث (يفضل PHP 8.0+)
- **Composer**: لإدارة حزم PHP
- **MySQL**: قاعدة البيانات
- **Apache/Nginx**: خادم الويب
- **Node.js & NPM**: لإدارة ملفات CSS/JS (اختياري)

### **بيئات التطوير المقترحة:**
- **XAMPP** (Windows/Mac/Linux)
- **WAMP** (Windows)
- **MAMP** (Mac)
- **Laragon** (Windows)
- **Docker** (جميع الأنظمة)

---

## 📋 **خطوات التثبيت**

### **الخطوة 1: تحضير البيئة**

#### **أ. تثبيت XAMPP (الأسهل للمبتدئين):**
1. حمل XAMPP من: https://www.apachefriends.org/
2. ثبت البرنامج
3. شغل Apache و MySQL من لوحة تحكم XAMPP

#### **ب. تثبيت Composer:**
1. حمل من: https://getcomposer.org/download/
2. ثبت البرنامج
3. تأكد من التثبيت بتشغيل: `composer --version`

### **الخطوة 2: إعداد المشروع**

#### **أ. نسخ الملفات:**
```bash
# انسخ مجلد المشروع إلى مجلد htdocs في XAMPP
# مثال: C:\xampp\htdocs\leaders-vision-academy
```

#### **ب. تثبيت التبعيات:**
```bash
# افتح Command Prompt أو Terminal في مجلد المشروع
cd C:\xampp\htdocs\leaders-vision-academy

# ثبت حزم PHP
composer install

# ثبت حزم JavaScript (اختياري)
npm install
```

### **الخطوة 3: إعداد قاعدة البيانات**

#### **أ. إنشاء قاعدة البيانات:**
1. اذهب إلى: http://localhost/phpmyadmin
2. أنشئ قاعدة بيانات جديدة باسم: `academy`
3. اختر ترميز: `utf8mb4_unicode_ci`

#### **ب. تحديث ملف .env:**
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=academy
DB_USERNAME=root
DB_PASSWORD=
```

### **الخطوة 4: تشغيل المشروع**

#### **أ. تشغيل الأوامر الأساسية:**
```bash
# إنشاء مفتاح التطبيق
php artisan key:generate

# تشغيل migrations
php artisan migrate

# تشغيل seeders لإنشاء البيانات الأساسية
php artisan db:seed

# ربط مجلد التخزين
php artisan storage:link
```

#### **ب. تشغيل الخادم:**
```bash
# الطريقة الأولى: استخدام خادم Laravel المدمج
php artisan serve

# الطريقة الثانية: استخدام XAMPP
# ضع المشروع في مجلد htdocs وادخل على:
# http://localhost/leaders-vision-academy/public
```

---

## 🌐 **روابط الوصول**

### **إذا استخدمت `php artisan serve`:**
- **الموقع الرئيسي**: http://127.0.0.1:8000
- **لوحة التحكم**: http://127.0.0.1:8000/dashboard
- **تسجيل الدخول**: http://127.0.0.1:8000/login

### **إذا استخدمت XAMPP:**
- **الموقع الرئيسي**: http://localhost/leaders-vision-academy/public
- **لوحة التحكم**: http://localhost/leaders-vision-academy/public/dashboard
- **تسجيل الدخول**: http://localhost/leaders-vision-academy/public/login

---

## 🔐 **بيانات تسجيل الدخول**

### **المدير العام:**
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `123123123`

### **إنشاء مستخدم جديد (إذا لم تعمل البيانات أعلاه):**
```bash
php artisan tinker

# ثم اكتب:
$user = App\Models\User::create([
    'name' => 'Admin',
    'email' => '<EMAIL>',
    'password' => bcrypt('password123')
]);

$user->attachRole('super_admin');
exit
```

---

## 🛠️ **حل المشاكل الشائعة**

### **مشكلة 1: خطأ في قاعدة البيانات**
```bash
# تأكد من تشغيل MySQL في XAMPP
# تأكد من صحة بيانات .env
# شغل:
php artisan migrate:fresh --seed
```

### **مشكلة 2: خطأ في الصلاحيات**
```bash
# في Linux/Mac:
sudo chmod -R 775 storage bootstrap/cache

# في Windows: تأكد من تشغيل Command Prompt كمدير
```

### **مشكلة 3: خطأ في Composer**
```bash
# حديث Composer
composer self-update

# ثبت التبعيات مرة أخرى
composer install --no-dev --optimize-autoloader
```

### **مشكلة 4: خطأ في مفتاح التطبيق**
```bash
php artisan key:generate
```

### **مشكلة 5: خطأ في الصور**
```bash
php artisan storage:link
```

---

## 📱 **أقسام الموقع**

### **الواجهة الأمامية:**
- **الصفحة الرئيسية**: عرض الأكاديمية والدورات
- **الدورات**: عرض جميع الدورات المتاحة
- **المدربين**: عرض المدربين
- **الشهادات**: طلب الشهادات
- **المؤسس**: معلومات المؤسس
- **اتصل بنا**: نموذج التواصل

### **لوحة التحكم (AdminLTE):**
- **الإحصائيات**: عدد المستخدمين والدورات
- **إدارة المستخدمين**: إضافة وتعديل المستخدمين
- **إدارة الدورات**: إضافة وتعديل الدورات
- **إدارة المدربين**: إضافة وتعديل المدربين
- **إدارة الشهادات**: إصدار الشهادات
- **الإعدادات**: إعدادات الموقع العامة

---

## 🎨 **تخصيص الموقع**

### **تغيير الألوان:**
- **ملف الألوان**: `public/css/leaders-vision-colors.css`
- **ملف الثيم**: `public/css/leaders-vision-theme.css`

### **تغيير الشعار:**
- **الشعار الحالي**: `public/images/logo.png`
- **أحجام مختلفة**: يمكن تعديل الأحجام في ملفات العرض

### **تغيير المحتوى:**
- **الصفحة الرئيسية**: `resources/views/home/<USER>
- **لوحة التحكم**: `resources/views/dashboard/`

---

## 📞 **الدعم الفني**

### **في حالة وجود مشاكل:**
1. **تحقق من logs**: `storage/logs/laravel.log`
2. **تأكد من متطلبات PHP**: `php -v`
3. **تأكد من Composer**: `composer --version`
4. **تأكد من قاعدة البيانات**: اختبر الاتصال في phpMyAdmin

### **أوامر مفيدة:**
```bash
# مسح الكاش
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# إعادة تحميل التطبيق
php artisan config:cache
php artisan route:cache

# عرض الروابط
php artisan route:list
```

---

## 🚀 **بدء التشغيل السريع**

### **للمبتدئين (استخدم XAMPP):**
1. ثبت XAMPP
2. شغل Apache و MySQL
3. انسخ المشروع إلى `C:\xampp\htdocs\`
4. شغل `composer install`
5. أنشئ قاعدة بيانات `academy`
6. شغل `php artisan migrate --seed`
7. ادخل على: `http://localhost/اسم-المجلد/public`

### **للمطورين (استخدم خادم Laravel):**
1. شغل `composer install`
2. أنشئ قاعدة بيانات
3. شغل `php artisan migrate --seed`
4. شغل `php artisan serve`
5. ادخل على: `http://127.0.0.1:8000`

---

**الموقع جاهز للاستخدام! 🎉**

**بيانات الدخول:**
- البريد: `<EMAIL>`
- كلمة المرور: `123123123`
