@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">
        <section class="content-header">
            <h1>
                متابعة الاشتراكات
                <small>لوحة تحكم شاملة لمتابعة اشتراكات الطلاب</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
                <li><a href="{{ route('dashboard.payments.index') }}">اشتراكات الطلاب</a></li>
                <li class="active">متابعة الاشتراكات</li>
            </ol>
        </section>

        <section class="content">

            <!-- إحصائيات عامة -->
            <div class="row">
                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-blue">
                        <div class="inner">
                            <h3>{{ $generalStats['total_subscriptions'] }}</h3>
                            <p>إجمالي الاشتراكات</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-users"></i>
                        </div>
                        <a href="{{ route('dashboard.payments.index') }}" class="small-box-footer">
                            عرض التفاصيل <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-green">
                        <div class="inner">
                            <h3>{{ $generalStats['active_subscriptions'] }}</h3>
                            <p>اشتراكات نشطة</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-check-circle"></i>
                        </div>
                        <a href="{{ route('dashboard.payments.index') }}?status=pending" class="small-box-footer">
                            عرض النشطة <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-yellow">
                        <div class="inner">
                            <h3>{{ number_format($generalStats['total_revenue'], 0) }}</h3>
                            <p>إجمالي الإيرادات (دج)</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-money"></i>
                        </div>
                        <a href="{{ route('dashboard.payments.statistics') }}" class="small-box-footer">
                            عرض الإحصائيات <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-red">
                        <div class="inner">
                            <h3>{{ count($subscriptionsByStatus['overdue']) }}</h3>
                            <p>اشتراكات متأخرة</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-warning"></i>
                        </div>
                        <a href="{{ route('dashboard.payments.overdue') }}" class="small-box-footer">
                            عرض المتأخرة <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- الاشتراكات حسب الحالة -->
            <div class="row">
                <!-- في الانتظار -->
                <div class="col-md-6">
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-clock-o"></i> في الانتظار
                                <span class="badge bg-yellow">{{ count($subscriptionsByStatus['pending']) }}</span>
                            </h3>
                        </div>
                        <div class="box-body" style="max-height: 300px; overflow-y: auto;">
                            @forelse($subscriptionsByStatus['pending'] as $subscription)
                                <div class="subscription-item" style="border-bottom: 1px solid #f0f0f0; padding: 10px 0;">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <strong>{{ $subscription->first_name }} {{ $subscription->last_name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $subscription->name_course }}</small>
                                        </div>
                                        <div class="col-md-4 text-left">
                                            <span class="label label-warning">في الانتظار</span>
                                            <br>
                                            <small class="text-muted">{{ $subscription->created_at->diffForHumans() }}</small>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <p class="text-center text-muted">لا توجد اشتراكات في الانتظار</p>
                            @endforelse
                        </div>
                        @if(count($subscriptionsByStatus['pending']) > 0)
                        <div class="box-footer">
                            <a href="{{ route('dashboard.payments.index') }}?status=pending" class="btn btn-warning btn-sm">
                                عرض الكل ({{ count($subscriptionsByStatus['pending']) }})
                            </a>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- دفع جزئي -->
                <div class="col-md-6">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-pie-chart"></i> دفع جزئي
                                <span class="badge bg-blue">{{ count($subscriptionsByStatus['partial']) }}</span>
                            </h3>
                        </div>
                        <div class="box-body" style="max-height: 300px; overflow-y: auto;">
                            @forelse($subscriptionsByStatus['partial'] as $subscription)
                                <div class="subscription-item" style="border-bottom: 1px solid #f0f0f0; padding: 10px 0;">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <strong>{{ $subscription->student_name ?? 'غير محدد' }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $subscription->course_name ?? 'غير محدد' }}</small>
                                            <div class="progress progress-xs" style="margin-top: 5px;">
                                                <div class="progress-bar progress-bar-info" style="width: 50%"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 text-left">
                                            <span class="label label-info">50%</span>
                                            <br>
                                            <small class="text-muted">قريباً</small>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <p class="text-center text-muted">لا توجد اشتراكات بدفع جزئي حالياً</p>
                            @endforelse
                        </div>
                        @if(count($subscriptionsByStatus['partial']) > 0)
                        <div class="box-footer">
                            <a href="{{ route('dashboard.payments.index') }}?status=partial" class="btn btn-info btn-sm">
                                عرض الكل ({{ count($subscriptionsByStatus['partial']) }})
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- الاشتراكات المتأخرة والدفعات المستحقة -->
            <div class="row">
                <!-- المتأخرة -->
                <div class="col-md-6">
                    <div class="box box-danger">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-warning"></i> اشتراكات متأخرة
                                <span class="badge bg-red">{{ count($subscriptionsByStatus['overdue']) }}</span>
                            </h3>
                        </div>
                        <div class="box-body" style="max-height: 300px; overflow-y: auto;">
                            @forelse($subscriptionsByStatus['overdue'] as $subscription)
                                <div class="subscription-item" style="border-bottom: 1px solid #f0f0f0; padding: 10px 0;">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <strong>{{ $subscription->student_name ?? 'غير محدد' }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $subscription->course_name ?? 'غير محدد' }}</small>
                                            <br>
                                            <small class="text-danger">
                                                <i class="fa fa-clock-o"></i> قريباً
                                            </small>
                                        </div>
                                        <div class="col-md-4 text-left">
                                            <span class="label label-danger">متأخر</span>
                                            <br>
                                            <small class="text-muted">قريباً</small>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <p class="text-center text-muted">لا توجد اشتراكات متأخرة حالياً</p>
                            @endforelse
                        </div>
                        @if(count($subscriptionsByStatus['overdue']) > 0)
                        <div class="box-footer">
                            <a href="{{ route('dashboard.payments.overdue') }}" class="btn btn-danger btn-sm">
                                عرض الكل ({{ count($subscriptionsByStatus['overdue']) }})
                            </a>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- المستحقة قريباً -->
                <div class="col-md-6">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-calendar"></i> مستحقة خلال 7 أيام
                                <span class="badge bg-blue">{{ count($upcomingPayments) }}</span>
                            </h3>
                        </div>
                        <div class="box-body" style="max-height: 300px; overflow-y: auto;">
                            @forelse($upcomingPayments as $payment)
                                <div class="subscription-item" style="border-bottom: 1px solid #f0f0f0; padding: 10px 0;">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <strong>{{ $payment->student_name ?? 'غير محدد' }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $payment->course_name ?? 'غير محدد' }}</small>
                                            <br>
                                            <small class="text-primary">
                                                <i class="fa fa-calendar"></i> قريباً
                                            </small>
                                        </div>
                                        <div class="col-md-4 text-left">
                                            <span class="label label-primary">مستحق</span>
                                            <br>
                                            <small class="text-muted">قريباً</small>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <p class="text-center text-muted">لا توجد دفعات مستحقة قريباً حالياً</p>
                            @endforelse
                        </div>
                        @if(count($upcomingPayments) > 0)
                        <div class="box-footer">
                            <a href="{{ route('dashboard.payments.due-today') }}" class="btn btn-primary btn-sm">
                                عرض المستحقة اليوم
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- الاشتراكات حسب الدورة -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-graduation-cap"></i> الاشتراكات حسب الدورة
                            </h3>
                        </div>
                        <div class="box-body table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>الدورة</th>
                                        <th>إجمالي الاشتراكات</th>
                                        <th>مكتملة</th>
                                        <th>في الانتظار</th>
                                        <th>جزئية</th>
                                        <th>إجمالي المبلغ</th>
                                        <th>المحصل</th>
                                        <th>معدل الإكمال</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($subscriptionsByCourse as $course)
                                        <tr>
                                            <td>
                                                <strong>{{ $course->name }}</strong>
                                                <br>
                                                <small class="text-muted">#{{ $course->id }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-blue">{{ $course->student_payments_count }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-green">{{ $course->completed_count }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-yellow">{{ $course->pending_count }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-orange">{{ $course->partial_count }}</span>
                                            </td>
                                            <td>{{ number_format($course->total_amount_sum ?? 0, 0) }} دج</td>
                                            <td>{{ number_format($course->paid_amount_sum ?? 0, 0) }} دج</td>
                                            <td>
                                                @php
                                                    $completionRate = $course->student_payments_count > 0
                                                        ? round(($course->completed_count / $course->student_payments_count) * 100, 1)
                                                        : 0;
                                                @endphp
                                                <div class="progress progress-xs">
                                                    <div class="progress-bar progress-bar-{{ $completionRate >= 70 ? 'success' : ($completionRate >= 40 ? 'warning' : 'danger') }}"
                                                         style="width: {{ $completionRate }}%"></div>
                                                </div>
                                                <span class="badge">{{ $completionRate }}%</span>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center">لا توجد اشتراكات</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الاشتراكات الحديثة -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-clock-o"></i> الاشتراكات الحديثة
                            </h3>
                            <div class="box-tools pull-right">
                                <a href="{{ route('dashboard.payments.index') }}" class="btn btn-info btn-sm">
                                    عرض الكل
                                </a>
                            </div>
                        </div>
                        <div class="box-body table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>الطالب</th>
                                        <th>الدورة</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($recentSubscriptions as $subscription)
                                        <tr>
                                            <td>
                                                <strong>{{ $subscription->student_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $subscription->student_email }}</small>
                                            </td>
                                            <td>{{ $subscription->course_name }}</td>
                                            <td>{{ number_format($subscription->total_amount, 0) }} دج</td>
                                            <td>
                                                <span class="label label-{{ $subscription->payment_status == 'completed' ? 'success' : ($subscription->payment_status == 'partial' ? 'warning' : 'danger') }}">
                                                    {{ $subscription->payment_status_arabic }}
                                                </span>
                                            </td>
                                            <td>{{ $subscription->enrollment_date->format('Y-m-d') }}</td>
                                            <td>
                                                <a href="{{ route('dashboard.purchases.index') }}" class="btn btn-xs btn-info">
                                                    <i class="fa fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center">لا توجد اشتراكات حديثة</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </section>
    </div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تحديث تلقائي للصفحة كل 10 دقائق
    setTimeout(function() {
        location.reload();
    }, 600000);

    // تحديث الوقت المتبقي للدفعات المستحقة
    setInterval(function() {
        $('.time-remaining').each(function() {
            // يمكن إضافة منطق تحديث الوقت هنا
        });
    }, 60000); // كل دقيقة
});
</script>
@endpush

@push('styles')
<style>
.subscription-item:hover {
    background-color: #f9f9f9;
    transition: background-color 0.3s ease;
}

.small-box .icon {
    font-size: 60px;
}

.progress-xs {
    height: 5px;
}

.badge {
    font-size: 11px;
}

.box-body {
    min-height: 200px;
}

.text-orange {
    color: #ff851b !important;
}

.bg-orange {
    background-color: #ff851b !important;
}
</style>
@endpush
