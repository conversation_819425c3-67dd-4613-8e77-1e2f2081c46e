<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class IndividualPayment extends Model
{
    protected $fillable = [
        'payment_tracking_id',
        'amount',
        'payment_date',
        'payment_method',
        'receipt_number',
        'receipt_image',
        'transaction_id',
        'notes',
        'received_by',
        'status'
    ];

    protected $dates = [
        'payment_date'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'date'
    ];

    // العلاقات
    public function paymentTracking()
    {
        return $this->belongsTo(StudentPaymentTracking::class, 'payment_tracking_id');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeVerified($query)
    {
        return $query->where('status', 'verified');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('payment_date', Carbon::today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('payment_date', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('payment_date', Carbon::now()->month)
                    ->whereYear('payment_date', Carbon::now()->year);
    }

    // Accessors
    public function getStatusArabicAttribute()
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'verified' => 'تم التحقق',
            'rejected' => 'مرفوضة'
        ];

        return $statuses[$this->status] ?? 'غير محدد';
    }

    public function getReceiptImagePathAttribute()
    {
        if (!$this->receipt_image) return null;
        return asset('uploads/receipts/' . $this->receipt_image);
    }

    public function getPaymentMethodArabicAttribute()
    {
        $methods = [
            'cash' => 'نقداً',
            'bank_transfer' => 'تحويل بنكي',
            'credit_card' => 'بطاقة ائتمان',
            'mobile_payment' => 'دفع عبر الهاتف',
            'check' => 'شيك',
            'other' => 'أخرى'
        ];

        return $methods[$this->payment_method] ?? $this->payment_method;
    }

    // Methods
    public function verify($receivedBy = null)
    {
        $this->status = 'verified';
        $this->received_by = $receivedBy;
        $this->save();
        
        // تحديث متابعة الدفعات
        $this->paymentTracking->verifyPayment($this->id);
        
        return $this;
    }

    public function reject($reason = null)
    {
        $this->status = 'rejected';
        if ($reason) {
            $this->notes = $reason;
        }
        $this->save();
        
        return $this;
    }
}
