@extends('layouts.home.app')

@section('content')

	<!-- Breadcrumbs Start -->
	<div class="rs-breadcrumbs breadcrumbs-overlay pt-5">
        <div class="breadcrumbs-img">
            <img src="{{ asset('home_file/assets/images/breadcrumbs/2.jpg') }}" alt="Breadcrumbs Image">
        </div>
        <div class="breadcrumbs-text white-color">
            <h1 class="page-title text-center">مؤسس الاكاديميه </h1>
            <ul class="text-center">
                <li>
                    <a class="active" href="/">الرئسيه</a>
                </li>
                <li>مؤسس الاكاديميه </li>
            </ul>
        </div>
    </div>

    <!-- Breadcrumbs End -->
    <!-- Founder Section Start -->
    <div id="rs-about" class="rs-about style4 pt-100 pb-100 md-pt-80 md-pb-80 bg-hidden-bits">
        <div class="container">
            @if(isset($founders) && $founders)
                <div class="row align-items-center">
                    <!-- معلومات المؤسس -->
                    <div class="col-lg-6">
                        <div class="founder-content">
                            <!-- العنوان الرئيسي -->
                            <div class="sec-title mb-4">
                                <div class="sub-title arabic-text text-primary-custom mb-3">
                                    مؤسس الأكاديمية
                                </div>
                                <h1 class="title text-light arabic-heading mb-3">
                                    {{ $founders->name ?? 'مؤسس الأكاديمية' }}
                                </h1>
                                <div class="founder-position text-warning mb-4">
                                    <i class="fa fa-star ml-2"></i>
                                    {{ $founders->job ?? 'المؤسس والرئيس التنفيذي' }}
                                </div>
                            </div>

                            <!-- وصف المؤسس -->
                            <div class="founder-description mb-4">
                                <p class="desc text-light arabic-text rtl-spacing">
                                    {{ $founders->description ?? 'رائد أعمال ومؤسس أكاديمية Leaders vision academy، يتمتع بخبرة واسعة في مجال التعليم والتدريب التقني، ويسعى لإعداد جيل من القادة المؤهلين في عصر التحول الرقمي.' }}
                                </p>
                            </div>

                            <!-- الإنجازات والخبرات -->
                            <div class="founder-achievements mb-4">
                                <h3 class="text-light arabic-heading mb-3">الإنجازات والخبرات</h3>
                                <div class="achievement-list">
                                    <div class="achievement-item mb-2">
                                        <i class="fa fa-check-circle text-success ml-2"></i>
                                        <span class="text-light arabic-text">أكثر من 15 سنة في مجال التعليم التقني</span>
                                    </div>
                                    <div class="achievement-item mb-2">
                                        <i class="fa fa-check-circle text-success ml-2"></i>
                                        <span class="text-light arabic-text">تدريب أكثر من 5000 متدرب</span>
                                    </div>
                                    <div class="achievement-item mb-2">
                                        <i class="fa fa-check-circle text-success ml-2"></i>
                                        <span class="text-light arabic-text">شهادات معتمدة في القيادة والإدارة</span>
                                    </div>
                                    <div class="achievement-item mb-2">
                                        <i class="fa fa-check-circle text-success ml-2"></i>
                                        <span class="text-light arabic-text">خبير في التحول الرقمي</span>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات الاتصال -->
                            <div class="founder-contact">
                                <h3 class="text-light arabic-heading mb-3">تواصل مع المؤسس</h3>
                                <ul class="contact-list">
                                    @if(isset($founders->phone) && $founders->phone)
                                    <li class="contact-item mb-3">
                                        <div class="contact-icon">
                                            <i class="fa fa-phone fa-lg text-primary-custom"></i>
                                        </div>
                                        <div class="contact-info">
                                            <span class="contact-label text-light arabic-text">رقم الهاتف</span>
                                            <a href="tel:{{ $founders->phone }}" class="contact-value text-warning">
                                                {{ $founders->phone }}
                                            </a>
                                        </div>
                                    </li>
                                    @endif

                                    @if(isset($founders->email) && $founders->email)
                                    <li class="contact-item mb-3">
                                        <div class="contact-icon">
                                            <i class="fa fa-envelope fa-lg text-primary-custom"></i>
                                        </div>
                                        <div class="contact-info">
                                            <span class="contact-label text-light arabic-text">البريد الإلكتروني</span>
                                            <a href="mailto:{{ $founders->email }}" class="contact-value text-warning">
                                                {{ $founders->email }}
                                            </a>
                                        </div>
                                    </li>
                                    @endif

                                    <li class="contact-item mb-3">
                                        <div class="contact-icon">
                                            <i class="fa fa-linkedin fa-lg text-primary-custom"></i>
                                        </div>
                                        <div class="contact-info">
                                            <span class="contact-label text-light arabic-text">لينكد إن</span>
                                            <a href="#" class="contact-value text-warning">
                                                تواصل عبر لينكد إن
                                            </a>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- صورة المؤسس -->
                    <div class="col-lg-6">
                        <div class="founder-image-section">
                            <div class="founder-image-container">
                                @if(isset($founders->image_path) && $founders->image_path)
                                    <img class="founder-image"
                                         src="{{ $founders->image_path }}"
                                         alt="{{ $founders->name ?? 'مؤسس الأكاديمية' }}">
                                @else
                                    <div class="founder-placeholder">
                                        <i class="fa fa-user fa-5x text-primary-custom"></i>
                                        <p class="text-light arabic-text mt-3">صورة المؤسس</p>
                                    </div>
                                @endif

                                <!-- تأثيرات بصرية -->
                                <div class="founder-bg-effects">
                                    <div class="circle-effect circle-1"></div>
                                    <div class="circle-effect circle-2"></div>
                                    <div class="circle-effect circle-3"></div>
                                </div>
                            </div>

                            <!-- اقتباس ملهم -->
                            <div class="founder-quote mt-4">
                                <blockquote class="quote-text text-center">
                                    <i class="fa fa-quote-right fa-2x text-warning mb-3"></i>
                                    <p class="text-light arabic-text">
                                        "نؤمن بأن التعليم هو المفتاح لبناء مستقبل أفضل، ونسعى لإعداد جيل من القادة القادرين على مواجهة تحديات العصر الرقمي"
                                    </p>
                                    <footer class="quote-author text-warning">
                                        - {{ $founders->name ?? 'مؤسس أكاديمية Leaders vision academy' }}
                                    </footer>
                                </blockquote>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <!-- رسالة في حالة عدم وجود بيانات المؤسس -->
                <div class="row">
                    <div class="col-12 text-center">
                        <div class="no-founder-data">
                            <i class="fa fa-user-circle fa-5x text-primary-custom mb-4"></i>
                            <h2 class="text-light arabic-heading mb-3">معلومات المؤسس</h2>
                            <p class="text-light arabic-text">
                                سيتم إضافة معلومات المؤسس قريباً
                            </p>
                            <a href="{{ route('home') }}" class="btn btn-primary-custom mt-3">
                                <i class="fa fa-home ml-2"></i>
                                العودة للرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Blog Section End -->

@endsection