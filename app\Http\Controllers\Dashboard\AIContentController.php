<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\AIContentService;
use Illuminate\Http\Request;
use App\Models\Category;

class AIContentController extends Controller
{
    protected $aiService;

    public function __construct(AIContentService $aiService)
    {
        $this->aiService = $aiService;
    }

    /**
     * توليد الوصف المختصر
     */
    public function generateShortDescription(Request $request)
    {
        $request->validate([
            'course_name' => 'required|string|max:255',
            'category_id' => 'nullable|exists:categories,id'
        ]);

        try {
            $categoryName = null;
            if ($request->category_id) {
                $category = Category::find($request->category_id);
                $categoryName = $category ? $category->name : null;
            }

            $shortDescription = $this->aiService->generateShortDescription(
                $request->course_name,
                $categoryName
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'short_description' => $shortDescription
                ],
                'message' => 'تم توليد الوصف المختصر بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في توليد الوصف المختصر: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * توليد أهداف الدورة
     */
    public function generateObjectives(Request $request)
    {
        $request->validate([
            'course_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:categories,id'
        ]);

        try {
            $categoryName = null;
            if ($request->category_id) {
                $category = Category::find($request->category_id);
                $categoryName = $category ? $category->name : null;
            }

            $objectives = $this->aiService->generateCourseObjectives(
                $request->course_name,
                $request->description,
                $categoryName
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'objectives' => $objectives
                ],
                'message' => 'تم توليد أهداف الدورة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في توليد أهداف الدورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * توليد الفئة المستهدفة
     */
    public function generateTargetAudience(Request $request)
    {
        $request->validate([
            'course_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:categories,id'
        ]);

        try {
            $categoryName = null;
            if ($request->category_id) {
                $category = Category::find($request->category_id);
                $categoryName = $category ? $category->name : null;
            }

            $targetAudience = $this->aiService->generateTargetAudience(
                $request->course_name,
                $request->description,
                $categoryName
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'target_audience' => $targetAudience
                ],
                'message' => 'تم توليد الفئة المستهدفة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في توليد الفئة المستهدفة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * توليد جميع المحتويات دفعة واحدة
     */
    public function generateAllContent(Request $request)
    {
        $request->validate([
            'course_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:categories,id'
        ]);

        try {
            $categoryName = null;
            if ($request->category_id) {
                $category = Category::find($request->category_id);
                $categoryName = $category ? $category->name : null;
            }

            // توليد جميع المحتويات
            $shortDescription = $this->aiService->generateShortDescription(
                $request->course_name,
                $categoryName
            );

            $objectives = $this->aiService->generateCourseObjectives(
                $request->course_name,
                $request->description,
                $categoryName
            );

            $targetAudience = $this->aiService->generateTargetAudience(
                $request->course_name,
                $request->description,
                $categoryName
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'short_description' => $shortDescription,
                    'objectives' => $objectives,
                    'target_audience' => $targetAudience
                ],
                'message' => 'تم توليد جميع المحتويات بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في توليد المحتويات: ' . $e->getMessage()
            ], 500);
        }
    }
}
