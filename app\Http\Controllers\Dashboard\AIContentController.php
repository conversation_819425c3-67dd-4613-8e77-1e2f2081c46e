<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\AIContentService;
use Illuminate\Http\Request;
use App\Models\Category;

class AIContentController extends Controller
{
    protected $aiService;

    public function __construct(AIContentService $aiService)
    {
        $this->aiService = $aiService;
    }

    /**
     * توليد الوصف المختصر
     */
    public function generateShortDescription(Request $request)
    {
        $request->validate([
            'course_name' => 'required|string|max:255'
        ]);

        try {
            $shortDescription = $this->aiService->generateShortDescription(
                $request->course_name,
                null // لا نحتاج للقسم، سنعتمد على العنوان فقط
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'short_description' => $shortDescription
                ],
                'message' => 'تم توليد الوصف المختصر بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في توليد الوصف المختصر: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * توليد أهداف الدورة
     */
    public function generateObjectives(Request $request)
    {
        $request->validate([
            'course_name' => 'required|string|max:255'
        ]);

        try {
            $objectives = $this->aiService->generateCourseObjectives(
                $request->course_name,
                null, // لا نحتاج للوصف
                null  // لا نحتاج للقسم
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'objectives' => $objectives
                ],
                'message' => 'تم توليد أهداف الدورة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في توليد أهداف الدورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * توليد الفئة المستهدفة
     */
    public function generateTargetAudience(Request $request)
    {
        $request->validate([
            'course_name' => 'required|string|max:255'
        ]);

        try {
            $targetAudience = $this->aiService->generateTargetAudience(
                $request->course_name,
                null, // لا نحتاج للوصف
                null  // لا نحتاج للقسم
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'target_audience' => $targetAudience
                ],
                'message' => 'تم توليد الفئة المستهدفة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في توليد الفئة المستهدفة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * توليد الوصف التفصيلي
     */
    public function generateDetailedDescription(Request $request)
    {
        $request->validate([
            'course_name' => 'required|string|max:255'
        ]);

        try {
            $detailedDescription = $this->aiService->generateDetailedDescription(
                $request->course_name,
                null, // لا نحتاج للوصف المختصر
                null  // لا نحتاج للقسم
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'detailed_description' => $detailedDescription
                ],
                'message' => 'تم توليد الوصف التفصيلي بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في توليد الوصف التفصيلي: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * توليد جميع المحتويات دفعة واحدة
     */
    public function generateAllContent(Request $request)
    {
        $request->validate([
            'course_name' => 'required|string|max:255'
        ]);

        try {
            // توليد جميع المحتويات بناءً على عنوان الدورة فقط
            $shortDescription = $this->aiService->generateShortDescription(
                $request->course_name,
                null
            );

            $detailedDescription = $this->aiService->generateDetailedDescription(
                $request->course_name,
                null,
                null
            );

            $objectives = $this->aiService->generateCourseObjectives(
                $request->course_name,
                null,
                null
            );

            $targetAudience = $this->aiService->generateTargetAudience(
                $request->course_name,
                null,
                null
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'short_description' => $shortDescription,
                    'detailed_description' => $detailedDescription,
                    'objectives' => $objectives,
                    'target_audience' => $targetAudience
                ],
                'message' => 'تم توليد جميع المحتويات بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في توليد المحتويات: ' . $e->getMessage()
            ], 500);
        }
    }
}
