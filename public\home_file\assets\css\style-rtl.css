@import url('https://fonts.googleapis.com/css?family=Rubik:300,400,500,600,700,800');

@import url('https://fonts.googleapis.com/css?family=Nunito:300,400,600,700,800,900');
@charset "utf-8";
/** 
* 
* -----------------------------------------------------------------------------
*
* Template : Educavo - Education HTML Template
* Author : rs-theme
* Author URI : http://www.rstheme.com/ 
*
* -----------------------------------------------------------------------------
* 
**//* TABLE OF CONTENT
---------------------------------------------------------
    01. General CSS
    02. Global Class CSS
    03. Header Section CSS
    04. Sticky Menu CSS
    05. Banner Section CSS
    06. Breadcrumbs Section CSS
    07. About Section CSS
    08. Services Section CSS
    09. Subject Section CSS
    10. Categories Section CSS
    11. Popular Courses Section CSS
    12. Course Single Section CSS
    13. Accordion Section CSS
    14. Why Choose US Section CSS
    15. Team Section CSS
    16. Team Single Section CSS
    17. Testimonial Section CSS
    18. Blog Section CSS
    19. Latest Events Section CSS
    20. Partner Section CSS
    21. Gallery Section CSS
    22. CTA Section CSS
    23. Counter Section CSS
    24. Newsletter Section CSS
    25. Publication Section CSS
    26. Facilities Section CSS
    27. Faq Section CSS
    28. Error Section CSS
    29. Shop Section CSS
    30. Single Shop Section CSS
    31. Cart Section CSS
    32. Header Cart Section CSS
    33. Checkout Section CSS
    34. Login Section CSS
    35. Register Section CSS
    36. Contact Section CSS
    37. Footer Section CSS
    38. Scroll Up CSS
    39. Preloader CSS

--------------------------------------------------------*/
/* -----------------------------------
    01. General CSS
-------------------------------------*/
/*html,*/
* {
  direction: ltr;
  text-align: right;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;

}
html {
    direction: rtl;
       font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
    /*font-family: 'Amiri', serif;
      font-family: 'Cairo', sans-serif;*/
}
body {
  font-size: 15px;
  color: #505050;
  /*font-family: 'Rubik', sans-serif;*/
  vertical-align: baseline;
  line-height: 26px;
  font-weight: 400;
  overflow-x: hidden;
  direction: ltr;
  text-align: left;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
  /*font-family: 'Amiri', serif;
  font-family: 'Cairo', sans-serif;*/
  /*flex-direction: row-reverse!important;*/
}
img {
  max-width: 100%;
  height: auto;
}
p {
  margin: 0 0 26px;
  line-height: 1.8;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Nunito', sans-serif;
  color: #111111;
  margin: 0 0 26px;
  line-height: 1.2;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
h1 {
  font-size: 70px;
  font-weight: 700;
      font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
h2 {
  font-size: 36px;
  font-weight: 700;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
h3 {
  font-size: 13px;
  font-weight: 700;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
h4 {
  font-size: 24px;
  font-weight: 700;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
h5 {
  font-size: 18px;
  font-weight: 700;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
h6 {
  font-size: 16px;
  font-weight: 700;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
.home-style2 h1,
.home-style2 h2,
.home-style2 h3,
.home-style2 h4,
.home-style2 h5,
.home-style2 h6 {
  color: #112958;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
.uppercase {
  text-transform: uppercase !important;
}
.capitalize {
  text-transform: capitalize !important;
}
.bold-text {
  font-size: 20px;
  font-weight: bold;
  font-family: 'Nunito', sans-serif;
}
.extra-bold {
  font-weight: 800 !important;
}
a {
  color: #fff;
  transition: all 0.3s ease;
  text-decoration: none !important;
  outline: none !important;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
a:active,
a:hover {
  text-decoration: none;
  outline: 0 none;
  color: #fff;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
ul {
  list-style: outside none none;
  margin: 0;
  padding: 0;
}
.clear {
  clear: both;
}
::-moz-selection {
  background: #21a7d0;
  text-shadow: none;
  color: #ffffff;
}
::selection {
  background: #21a7d0;
  text-shadow: none;
  color: #ffffff;
}
.border-none:last-child {
  border-right: none !important;
}
.width-unset {
  width: unset !important;
}
.white-bg {
  background-color: #ffffff;
}
.primary-bg {
  background: #21a7d0;
}
.secondary-bg {
  background: #273c66;
}
.title-bg {
  background: #111111;
}
.gray-bg {
  background: #f3f8f9;
}
.gray-bg3 {
  background: #e7f9fb;
}
.gray-bg2 {
  background: #e7f6f9;
}
.event-bg {
  background: #f9f8f8;
}
.event2-bg {
  background: #F4F6F5;
}
.dark-red {
  background: #1D0E15;
}
.bg-light-gray {
  background: #f9f9f9;
}
.transparent-bg {
  background: transparent;
}
.home5color {
  color: #54647b !important;
}
.bg-fixed {
  background-attachment: fixed;
  background-repeat: no-repeat;
}
.bg1 {
  background: url(assets/images/bg/bg1.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.bg2 {
  background: url(assets/images/bg/bg2.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.bg3 {
  background: url(assets/images/bg/bg3.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top center;
}
.bg4 {
  background: url(assets/images/bg/home6/bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.bg5 {
  background: url(assets/images/bg/home7/bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 5px;
}
.bg6 {
  background: url(assets/images/bg/home-8-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
.bg7 {
  background: url(assets/images/bg/home8-bg2.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.bg8 {
  background: url(assets/images/bg/home12/dotted.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  padding: 50px 0 50px;
}
.bg9 {
  background: url(assets/images/bg/home13/counter-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
.bg10 {
  background: url(assets/images/bg/home13/logo-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
.bg11 {
  background: url(assets/images/bg/home13/subscribe-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
.bg12 {
  background: url(assets/images/bg/home13/footer-bg.html);
  background-size: cover;
  background-repeat: no-repeat;
}
.course-shape-bg {
  background: url(assets/images/bg/course-shape-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.bg-unset {
  background: unset !important;
}
.rs-menu-toggle {
  color: #21a7d0;
  text-align: right;
}
button {
  cursor: pointer;
}
.glyph-icon:before {
  margin: 0;
}
input:focus,
select:focus,
textarea:focus,
button:focus {
  outline: none !important;
}
.pagenav-link ul {
  border: 1px solid #f2f2f2;
  border-radius: 3px;
  display: inline-block;
  padding: 0 3px;
}
.pagenav-link ul li {
  display: inline-block;
  padding: 0 18px;
  margin: 14px 0;
  border-right: 1px solid #E6E6E6;
  color: #21a7d0;
  font-size: 16px;
}
.pagenav-link ul li a {
  color: #505050;
}
.pagenav-link ul li a i:before {
  color: #505050;
  font-size: 13px;
  transition: all 0.3s ease;
}
.pagenav-link ul li a:hover {
  color: #21a7d0;
}
.pagenav-link ul li a:hover i:before {
  color: #21a7d0;
}
.pagenav-link ul li:last-child {
  border-right: none;
}
.pagenav-link.orange-color ul li,
.pagenav-link.orange-color ul li a:hover,
.pagenav-link.orange-color ul li a:hover i:before {
  color: #ff5421;
}
.gridFilter button {
  background: transparent;
  border: 0;
  font-size: 15px;
  font-weight: 700;
  outline: none;
  color: #505050;
  cursor: pointer;
}
.gridFilter button.active {
  color: #ff5421;
}
.gridFilter button + button {
  margin-left: 20px;
}
.gridFilter.style2 button {
  background: #f9f7f8;
  border: none;
  border-radius: 3px;
  font-weight: 500;
  font-size: 16px;
  color: #101010;
  padding: 10px 30px;
  outline: none;
  margin: 0 10px 20px;
}
.gridFilter.style2 button.active,
.gridFilter.style2 button:hover {
  background: #fff;
  color: #ff5421;
  box-shadow: 0 0 30px #eee;
}
blockquote {
  margin: 35px 0;
  padding: 40px;
  color: #666;
  position: relative;
  background: #fff;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 55px;
  font-weight: 400;
  font-style: italic;
  text-align: left;
  clear: both;
  box-shadow: 0 0 150px #eee;
}
blockquote:before {
  position: absolute;
  content: "";
  display: block;
  left: 0;
  top: 0;
  height: 100%;
  width: 10px;
  background: #21a7d0;
}
blockquote p {
  font-size: 16px;
  margin: 0;
}
.check-square li {
  position: relative;
  padding-left: 35px;
  line-height: 34px;
  border: none;
  color: #ffffff;
}
.check-square li:before {
  position: absolute;
  top: 2px;
  left: 0;
  z-index: 0;
  content: "\f046";
  font-family: fontawesome;
  color: #ff5421;
  font-size: 20px;
}
/*-- Blog Button Css --*/
.blog-btn {
  color: #111111;
  display: inline-block;
  position: relative;
  font-size: 15px;
  padding-right: 43px;
  text-transform: capitalize;
  font-weight: 600;
}
.blog-btn:after {
  position: absolute;
  content: "\f133";
  right: 20px;
  top: 50%;
  font-size: 15px;
  font-weight: 600;
  color: #111111;
  font-family: "Flaticon";
  transform: translateY(-50%);
  transition: all 0.3s ease;
}
.blog-btn:hover {
  color: #21a7d0;
}
.blog-btn:hover:after {
  right: 17px;
  color: #21a7d0;
}
/* ------------------------------------
    02. Global Class CSS
---------------------------------------*/
.y-middle {
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.y-bottom {
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -ms-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: flex-end;
  -webkit-align-items: flex-end;
  align-items: flex-end;
}
.readon {
  outline: none;
  padding: 12px 40px;
  border: 1px solid #21a7d0;
  border-radius: 30px;
  display: inline-block;
  text-transform: uppercase;
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
  font-weight: 700;
  color: #111111;
  background: #f3f8f9;
}
.readon.banner-style {
  color: #ffffff;
  background: #21a7d0;
  padding: 16px 58px;
}
.readon.banner-style:hover {
  background: #1e95ba;
}
.readon.purple-btn {
  color: #ffffff;
  background: #625eaa;
  border: none;
  padding: 16px 40px;
}
.readon.purple-btn:hover {
  background: #4e49a1;
}
.readon.green-btn {
  color: #FFFFFF;
  background: #0C8B51;
  border-color: #02010100;
  border-radius: 30px;
  font-size: 14px;
  padding: 10px 30px 10px 30px;
}
.readon.green-btn:hover {
  opacity: 0.9;
  background: #0C8B51;
}
.readon.green-banner {
  font-size: 16px;
  line-height: 38px;
  font-weight: 700;
  padding: 8px 30px;
  border-radius: 4px;
  background-color: #0c8b51;
  border-color: transparent;
  color: #ffffff;
}
.readon.green-banner:hover {
  background: #08a355;
}
.readon.white-color {
  color: #ffffff !important;
  background: none !important;
  border: 1px solid #ffffff;
  padding: 16px 40px;
}
.readon.white-color:hover {
  background: #ffffff !important;
  color: #4e49a1 !important;
}
.readon.orange-btn {
  color: #ffffff;
  background: #ff5421;
  border-color: #ff5421;
  border-radius: 5px 5px 5px 5px;
  outline: none;
  padding: 12px 35px;
  display: inline-block;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
}
.readon.orange-btn.main-home {
  padding: 10px 20px 10px 20px !important;
}
.readon.orange-btn.main-home:hover {
  color: #ffffff;
  background: #171F32;
  border-color: #171F32;
}
.readon.orange-btn:hover {
  color: #ffffff;
  background: #ed3600;
  border-color: #ed3600;
}
.readon.orange-btn.transparent:hover {
  color: #101010;
  background: #ffffff;
  border-color: #ffffff;
}
.readon.yellow-btn {
  background: #F4BF00;
  border-color: #F4BF00;
  border-radius: 4px 4px 4px 4px;
}
.readon.yellow-btn:hover {
  color: #ffffff;
  background: #171F32;
  border-color: #171F32;
}
.readon.yellow-btn.transparent3:hover {
  color: #1c335f;
  background: #ffffff;
  border-color: #ffffff;
}
.readon.transparent2 {
  background: #21a7d0;
  color: #111111;
  border: 1px solid #21a7d0;
}
.readon.transparent2:hover {
  color: #111111;
  background: transparent;
  border: 1px solid #21a7d0;
}
.readon.register-btn {
  padding: 10px 50px;
  color: #ffffff;
  background: #ff5421;
  border-color: #ff5421;
  font-weight: 500;
  width: 100%;
  border-radius: 5px;
}
.readon.register-btn:hover {
  background: #ff4007;
}
.readon.submit-btn {
  border: 2px solid;
  border-color: #ff5421;
  padding: 10px 50px;
  line-height: normal;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 500;
  background: #ff5421;
  color: #ffffff;
  border-radius: 5px;
  margin-bottom: 12px;
  width: 100%;
}
.readon.submit-btn:hover {
  background: transparent;
  color: #ff5421;
}
.readon.submit-requset {
  background-color: #0FCB75;
  margin: 0px 0px 0px 0px;
  padding: 13px 35px 13px 35px;
  outline: none;
  border: none;
  padding: 12px 40px;
  border-radius: 3px;
  display: inline-block;
  text-transform: capitalize;
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  color: #ffffff;
}
.readon.submit-requset:hover {
  opacity: 0.9;
  background-color: #0FCB75;
}
.readon.border-less {
  border: none;
}
.readon.border-less:hover {
  background: rgba(243, 248, 239, 0.8);
  color: #111111;
}
.readon:hover {
  background: #21a7d0;
  color: #ffffff;
}
.readon2 {
  outline: none;
  border: none;
  padding: 12px 40px;
  border-radius: 3px;
  display: inline-block;
  text-transform: capitalize;
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  color: #ffffff;
  background: #21a7d0;
  position: relative;
  overflow: hidden;
}
.readon2.banner-style {
  padding: 16px 40px;
}
.readon2.mod {
  text-transform: uppercase;
  padding: 14px 31px;
}
.readon2.transparent {
  background: transparent;
  color: #21a7d0;
  border: 1px solid #21a7d0;
}
.readon2.transparent:hover {
  color: #ffffff;
  background: #21a7d0;
}
.readon2.orange-transparent {
  background: transparent;
  color: #ff5421;
  border: 1px solid #ff5421;
}
.readon2.orange-transparent:hover {
  color: #ffffff;
  background: #ff5421;
}
.readon2.orange {
  background: #ff5421;
}
.readon2.orange:hover {
  background: #ff4007;
}
.readon2:hover {
  background: #1e95ba;
  color: #ffffff;
}
.readon2.gym-btn {
  background: #ff5421;
  color: #ffffff;
  border-radius: 4px;
  font-weight: 500;
  font-size: 16px;
  text-transform: capitalize;
  padding: 17px 28px;
}
.readon2.gym-btn:hover {
  opacity: 0.80;
}
.readon2.gym-btn.get-now:hover {
  background: #ffffff;
  color: #ff5421;
}
.readon2.cta-btn {
  background: #ff5421;
  color: #ffffff;
  border-radius: 3px 3px 3px 3px;
  font-family: "Rubik", Sans-serif;
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
  padding: 12px 30px 12px 30px;
}
.readon2.cta-btn:hover {
  color: #F0E3E3;
  background: #FF5421;
}
.readon2.cta-btn {
  background: #ff5421;
  color: #ffffff;
  border-radius: 3px 3px 3px 3px;
  font-family: "Rubik", Sans-serif;
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
  padding: 12px 30px 12px 30px;
}
.readon2.cta-btn:hover {
  color: #F0E3E3;
  background: #FF5421;
}
.readon2.apply {
  padding: 10px 30px 10px 30px;
}
.readon2.apply:hover {
  color: unset;
}
.readon3 {
  outline: none;
  padding: 12px 50px;
  border-radius: 30px;
  display: inline-block;
  text-transform: uppercase;
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  color: #ffffff;
  background: #21a7d0;
  position: relative;
}
.readon3:after,
.readon3:before {
  position: absolute;
  content: '';
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #ffffff;
  transition: all 0.3s ease;
}
.readon3:after {
  right: 30px;
}
.readon3:before {
  left: 30px;
}
.readon3.active {
  background: #f3f8f9;
  color: #21a7d0;
}
.readon3.active:after,
.readon3.active:before {
  background: #21a7d0;
}
.readon3.active:hover {
  color: #ffffff;
  background: #21a7d0;
}
.readon3.active:hover:after,
.readon3.active:hover:before {
  background: #ffffff;
}
.readon3.dark-hov:hover {
  background: #111111;
  color: #ffffff;
}
.readon3.dark-hov:hover:after,
.readon3.dark-hov:hover:before {
  background: #ffffff;
}
.readon3:hover {
  background: #f3f8f9;
  color: #21a7d0;
}
.readon3:hover:after,
.readon3:hover:before {
  background: #21a7d0;
}
.readon-arrow {
  color: #111111;
  padding-right: 22px;
  position: relative;
  font-weight: 500;
}
.readon-arrow:after {
  position: absolute;
  content: "\f136";
  font-family: Flaticon;
  font-style: normal;
  top: 50%;
  transform: translateY(-50%);
  right: 5px;
  color: #111111;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 14px;
}
.readon-arrow:hover {
  color: #21a7d0;
}
.readon-arrow:hover:after {
  right: 0;
  color: #21a7d0;
}
.btn-shop {
  outline: none;
  font-weight: 400;
  border: none;
  text-transform: uppercase;
  background: #21a7d0;
  font-size: 15px;
  padding: 8px 25px;
  color: #ffffff;
  border-radius: 5px;
  transition: all 0.3s ease 0s;
}
.btn-shop:hover {
  background: #273c66;
  color: #ffffff;
}
.btn-shop.orange-color {
  background: #ff5421;
  color: #ffffff;
}
.btn-shop.orange-color:hover {
  background: #ed3600;
}
/*Pagination*/
.pagination-area .pagination-part {
  display: inline-block;
  padding: 0 5px 0 10px;
  height: 60px;
  line-height: 60px;
  box-shadow: 0px 8px 26px 0px rgba(0, 0, 0, 0.1);
}
.pagination-area .pagination-part li {
  position: relative;
  display: inline-block;
  padding: 0 20px 0 15px;
  text-align: center;
  cursor: pointer;
}
.pagination-area .pagination-part li a {
  display: inline-flex;
  align-items: center;
  color: #111111;
}
.pagination-area .pagination-part li a:hover {
  color: #21a7d0;
}
.pagination-area .pagination-part li i {
  margin-left: 10px;
}
.pagination-area .pagination-part li i:before {
  font-size: 28px;
  margin: 0;
  line-height: 60px;
}
.pagination-area .pagination-part li:before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #d6fbff;
}
.pagination-area .pagination-part li:last-child:before {
  display: none;
}
.pagination-area .pagination-part li:hover a,
.pagination-area .pagination-part li.active a {
  color: #21a7d0;
}
.pagination-area.orange-color .pagination-part li a:hover {
  color: #ff5421;
}
.pagination-area.orange-color .pagination-part li:before {
  background: #ff5421;
}
.pagination-area.orange-color .pagination-part li:hover a,
.pagination-area.orange-color .pagination-part li.active a {
  color: #ff5421;
}
/*Video Icon*/
.media-icon {
  position: relative;
  display: inline-block;
  z-index: 1;
}
.media-icon .popup-videos {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  background: #21a7d0;
  width: 70px;
  height: 70px;
  border-radius: 100%;
  text-align: center;
}
.media-icon .popup-videos:before {
  content: "";
  position: absolute;
  z-index: 0;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: block;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  animation: pulse-border 1500ms ease-out infinite;
  transition: all 0.3s ease;
  z-index: -1;
}
.media-icon .popup-videos i {
  margin-left: 5px;
}
.media-icon .popup-videos i:before {
  font-size: 30px;
  line-height: 70px;
}
.media-icon .popup-videos:hover:before {
  background: #21a7d0;
}
.media-icon.orange-color .popup-videos {
  color: #ff5421;
  background: #ffffff;
}
.media-icon.orange-color .popup-videos i:before {
  font-size: 30px;
  line-height: 70px;
}
.media-icon.orange-color .popup-videos:hover:before {
  background: #ffffff;
}
.media-icon.orange-color2 .popup-videos {
  color: #ffffff;
  background: #ff5421;
  animation: circle-ripple 3s linear linear infinite;
  -webkit-animation: circle-ripple 3s linear infinite;
}
.media-icon.orange-color2 .popup-videos:before {
  display: none;
}
.media-icon.orange-color2 .popup-videos:hover:before {
  background: #ff5421;
}
.media-icon.yellow-color .popup-videos {
  color: #273c66;
  background: #f4bf00;
}
.media-icon.yellow-color .popup-videos:hover:before {
  background: #1c335f;
}
.rs-video-home9 {
  background: url(assets/images/video/video2.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  padding: 300px 0 300px;
}
.rs-video-home9 .media-icon {
  display: block;
}
.rs-call-action {
  position: relative;
}
.rs-call-action .spiners {
  position: absolute;
  bottom: 0;
}
.rs-call-action .spiners.one {
  top: 12%;
}
.rs-call-action .spiners.two {
  bottom: 18%;
  right: 56%;
}
.rs-call-action .spiners.three {
  top: 16%;
  right: 39%;
}
.margin-remove {
  margin: 0 !important;
}
.no-gutter {
  margin-left: 0;
  margin-right: 0;
}
.no-gutter [class*="col-"] {
  padding-left: 0;
  padding-right: 0;
}
[class^="flaticon-"]:before,
[class*=" flaticon-"]:before,
[class^="flaticon-"]:after,
[class*=" flaticon-"]:after {
  margin: 0;
}
.container-fluid {
  padding-left: 60px;
  padding-right: 60px;
}
.display-table {
  display: table;
  height: 100%;
  width: 100%;
}
.display-table-cell {
  display: table-cell;
  vertical-align: middle;
}
.relative {
  position: relative;
}
.body-color {
  color: #505050 !important;
}
.white-color {
  color: #ffffff !important;
}
.title-color {
  color: #111111 !important;
}
.title-color2 {
  color: #112958 !important;
}
.secondary-color {
  color: #273c66;
}
.primary-color {
  color: #21a7d0 !important;
}
.orangeColor {
  color: #ff5421 !important;
}
.dark-parimary-bg {
  background: #203154 !important;
}
ul.listing-style li {
  position: relative;
  padding-left: 30px;
  line-height: 34px;
}
ul.listing-style li:before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  content: "\f05d";
  font-family: 'FontAwesome';
  color: #21a7d0;
  font-size: 20px;
}
ul.listing-style.modify li {
  padding-left: 23px;
}
ul.listing-style.modify li:before {
  font-size: 15px;
}
.rs-carousel.nav-style1 {
  position: relative;
}
.rs-carousel.nav-style1 .owl-nav {
  display: block;
}
.rs-carousel.nav-style1 .owl-nav .owl-next,
.rs-carousel.nav-style1 .owl-nav .owl-prev {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 30px;
  width: 42px;
  height: 42px;
  line-height: 42px;
  border-radius: 50%;
  background: #21a7d0;
  text-align: center;
  color: #ffffff;
  transition: all 0.5s;
  transition-delay: 0.70s;
  opacity: 0;
  visibility: hidden;
}
.rs-carousel.nav-style1 .owl-nav .owl-next i:before,
.rs-carousel.nav-style1 .owl-nav .owl-prev i:before {
  content: "\f138";
  font-family: Flaticon;
}
.rs-carousel.nav-style1 .owl-nav .owl-next {
  right: 30px;
  left: unset;
}
.rs-carousel.nav-style1 .owl-nav .owl-next i:before {
  content: "\f137";
}
.rs-carousel.nav-style1:hover .owl-nav .owl-next,
.rs-carousel.nav-style1:hover .owl-nav .owl-prev {
  left: -50px;
  transition-delay: 0s;
  visibility: visible;
  opacity: 1;
}
.rs-carousel.nav-style1:hover .owl-nav .owl-next {
  right: -50px;
  left: unset;
}
.rs-carousel.nav-style2 {
  position: relative;
}
.rs-carousel.nav-style2 .owl-nav {
  display: block;
  position: absolute;
  top: -80px;
  right: 0;
}
.rs-carousel.nav-style2 .owl-nav .owl-prev,
.rs-carousel.nav-style2 .owl-nav .owl-next {
  display: inline-block;
}
.rs-carousel.nav-style2 .owl-nav .owl-prev i,
.rs-carousel.nav-style2 .owl-nav .owl-next i {
  transition: all 0.3s ease;
}
.rs-carousel.nav-style2 .owl-nav .owl-prev i:before,
.rs-carousel.nav-style2 .owl-nav .owl-next i:before {
  font-family: Flaticon;
  font-size: 22px;
}
.rs-carousel.nav-style2 .owl-nav .owl-prev:hover i,
.rs-carousel.nav-style2 .owl-nav .owl-next:hover i {
  color: #21a7d0;
}
.rs-carousel.nav-style2 .owl-nav .owl-prev i:before {
  content: "\f134";
}
.rs-carousel.nav-style2 .owl-nav .owl-prev:after {
  content: "/";
  padding: 0 5px 0 5px;
  position: relative;
  top: -3px;
}
.rs-carousel.nav-style2 .owl-nav .owl-next i:before {
  content: "\f133";
}
.rs-carousel.orange-color .owl-nav .owl-prev:hover i,
.rs-carousel.orange-color .owl-nav .owl-next:hover i {
  color: #ff5421;
}
.rs-carousel .owl-dots {
  text-align: center;
  margin: 40px auto 0;
  line-height: 15px;
  display: block;
}
.rs-carousel .owl-dots .owl-dot {
  width: 30px;
  height: 10px;
  display: inline-block;
  margin: 0 6px;
  padding: 3px 0;
  border-radius: 30px;
  border: 1px solid #21a7d0;
  background: transparent;
  opacity: 0.7;
  cursor: pointer;
}
.rs-carousel .owl-dots .owl-dot:hover {
  background: #21a7d0;
}
.rs-carousel .owl-dots .active {
  background: #21a7d0;
  opacity: 1;
}
.owl-carousel .owl-item img {
  width: auto;
}
.sec-title .sub-title {
  font-size: 18px;
  line-height: 28px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 8px;
}
.sec-title .sub-title.primary {
  color: #21a7d0;
}
.sec-title .sub-title.secondary {
  color: #273c66;
}
.sec-title .sub-title.white {
  color: #ffffff;
}
.sec-title .sub-title.dark {
  color: #111111;
}
.sec-title .sub-title.orange {
  color: #ff5421;
}
.sec-title .desc.big {
  font-size: 18px;
  line-height: 30px;
}
.sec-title .midline {
  font-size: 36px;
  color: #111111;
}
.sec-title2 .sub-title {
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;
  color: #21a7d0;
  text-transform: uppercase;
  margin-bottom: 10px;
}
.sec-title2 .sub-title.white-color {
  color: #ffffff;
}
.sec-title2 .title {
  font-size: 42px;
  line-height: 52px;
  font-weight: 800;
  color: #031a3d;
}
.sec-title2 .title.white-color {
  color: #ffffff;
}
.sec-title2 .title.purple-color {
  color: #4e49a1 !important;
}
.sec-title3 .sub-title {
  font-size: 16px;
  line-height: 28px;
  font-weight: 500;
  color: #ff5421;
  text-transform: uppercase;
  margin-bottom: 10px;
}
.sec-title3 .sub-title.yellow-color {
  color: #f4bf00;
}
.sec-title3 .sub-title.green-color {
  color: #0c8b51;
}
.sec-title3 .sub-title.no-big {
  text-transform: capitalize;
  color: #ffffff;
  letter-spacing: 3px;
}
.sec-title3 .sub-title.big {
  font-size: 18px  !important;
  color: #f4bf00 !important;
  font-weight: 500;
}
.sec-title3 .title {
  font-size: 36px;
  line-height: 46px;
  font-weight: 800;
  color: #031a3d;
}
.sec-title3 .title.white-color {
  color: #ffffff;
}
.sec-title3 .title.black-color {
  color: #101010;
}
.sec-title3 .title.new-title {
  color: #101010;
  line-height: 1.2;
  font-weight: 700;
}
.sec-title3 .title.title2 {
  font-size: 42px;
  line-height: 55px;
  font-weight: 700;
  color: #ffffff;
}
.sec-title3 .title.title3 {
  font-size: 42px;
  line-height: 55px;
  font-weight: 800;
  color: #ffffff;
}
.sec-title3 .desc {
  font-size: 16px;
  line-height: 26px;
  color: #363636;
}
.sec-title3 .desc.white-color {
  color: #ffffff;
}
.sec-title3 .new-desc {
  font-size: 18px;
  line-height: 31px;
  color: #333333;
}
.sec-title4 .sub-title {
  font-size: 16px;
  line-height: 28px;
  font-weight: 500;
  color: #0c8b51;
  text-transform: uppercase;
  margin-bottom: 10px;
}
.sec-title4 .title {
  font-size: 36px;
  line-height: 46px;
  font-weight: 700;
  color: #101010;
  position: relative;
  padding: 0px 0px 20px 0px;
}
.sec-title4 .title:after {
  content: "";
  position: absolute;
  border: 0;
  width: 50px;
  height: 4px;
  z-index: 9;
  left: 0;
  right: 0;
  margin: 0 auto;
  bottom: 0;
  background: #0c8b51;
}
.sec-title4 .desc {
  font-size: 16px;
  line-height: 26px;
  color: #363636;
}
.sec-title5 .description {
  color: #ffffff;
}
.sec-title5 .description.title-color {
  color: #7A7A7A;
  font-weight: 400;
}
.sec-title5 .description span a {
  color: #ff5421;
}
.sec-title5 .description span a i {
  margin-left: 5px;
}
.sec-title5 .description span a i:before {
  top: 2.5px;
  position: relative;
}
.sec-title6 .title {
  font-size: 36px;
  line-height: 46px;
  font-weight: 700;
  color: #031a3d;
}
.sec-title6 .title.title2 {
  font-weight: 800;
}
.left-top-shape {
  position: absolute;
  left: -55px;
  top: 65px;
}
.right-top-shape {
  position: absolute;
  right: -115px;
  top: 25px;
}
/* -----------------------
    03. Header Section CSS
--------------------------*/
.full-width-header .rs-header {
  z-index: 99;
}
.full-width-header .rs-header .topbar-area {
  background: #273c66;
}
.full-width-header .rs-header .topbar-area .topbar-contact li {
  display: inline-block;
  line-height: 50px;
  height: 50px;
  margin-right: 17px;
  padding-right: 20px;
  color: #ffffff;
  border-right: 1px solid #374A71;
}
.full-width-header .rs-header .topbar-area .topbar-contact li i {
  margin-right: 2px;
}
.full-width-header .rs-header .topbar-area .topbar-contact li i.flaticon-email:before {
  bottom: -1.5px;
  position: relative;
}
.full-width-header .rs-header .topbar-area .topbar-contact li i:before {
  font-size: 16px;
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area .topbar-contact li a {
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area .topbar-contact li a:hover {
  color: #21a7d0;
}
.full-width-header .rs-header .topbar-area .topbar-contact li:last-child {
  margin: 0;
  border: none;
}
.full-width-header .rs-header .topbar-area .topbar-right li {
  display: inline;
  margin-right: 30px;
}
.full-width-header .rs-header .topbar-area .topbar-right li.login-register {
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area .topbar-right li.login-register i {
  margin-right: 10px;
  color: #21a7d0;
}
.full-width-header .rs-header .topbar-area .topbar-right li.login-register a {
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area .topbar-right li.login-register a:hover {
  color: #21a7d0;
}
.full-width-header .rs-header .topbar-area .topbar-right li a.apply-btn {
  color: #ffffff;
  background: #21a7d0;
  padding: 12px 35px;
  display: inline-block;
}
.full-width-header .rs-header .topbar-area .topbar-right li a.apply-btn:hover {
  background: #2db4de;
}
.full-width-header .rs-header .topbar-area .topbar-right li:last-child {
  margin: 0;
}
.full-width-header .rs-header .topbar-area.home8-topbar {
  background: #f9f8f8 !important;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-contact li {
  color: #363636;
  border-right: 1px solid #fff;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-contact li i {
  margin-right: 2px;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-contact li i:before {
  font-size: 16px;
  color: #363636;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-contact li a {
  color: #363636;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-contact li a:hover {
  color: #ff5421;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-contact li:last-child {
  margin: 0;
  border: none;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-right li.login-register {
  color: #363636;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-right li.login-register i {
  margin-right: 10px;
  color: #363636;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-right li.login-register a {
  color: #363636;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-right li.login-register a:hover {
  color: #ff5421;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-right li a.apply-btn {
  background: #ff5421;
  text-transform: uppercase;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-right li a.apply-btn:hover {
  color: #ffffff;
  background: #ed3600;
}
.full-width-header .rs-header .topbar-area.home8-topbar .topbar-right li:last-child {
  margin: 0;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part {
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-contact li {
  color: #ffffff !important;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-contact li i {
  margin-right: 2px;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-contact li i:before {
  font-size: 16px;
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-contact li a {
  color: #ffffff !important;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-contact li a:hover {
  color: #ff5421;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-contact li:last-child {
  margin: 0;
  border: none !important;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-right li.login-register {
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-right li.login-register i {
  margin-right: 10px;
  color: #ffffff !important;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-right li.login-register a {
  color: #ffffff !important;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-right li.login-register a:hover {
  color: #ff5421;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-right li a.apply-btn {
  background: #ff5421;
  text-transform: uppercase;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-right li a.apply-btn:hover {
  color: #ffffff;
  background: #ed3600;
}
.full-width-header .rs-header .topbar-area.home8-topbar.inner-part .topbar-right li:last-child {
  margin: 0;
}
.full-width-header .rs-header .topbar-area.home9-topbar {
  background: #273c66 !important;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-contact li {
  color: #ffffff;
  border-right: 1px solid #1b315e;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-contact li i {
  margin-right: 2px;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-contact li i:before {
  font-size: 16px;
  color: #f4bf00;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-contact li a {
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-contact li a:hover {
  color: #f4bf00;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-contact li:last-child {
  margin: 0;
  border: none;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-right li.login-register {
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-right li.login-register i {
  margin-right: 10px;
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-right li.login-register a {
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-right li.login-register:hover i,
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-right li.login-register:hover a {
  color: #f4bf00;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-right li a.apply-btn {
  background: #f4bf00;
  text-transform: uppercase;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-right li a.apply-btn:hover {
  color: #ffffff;
  background: #c19700;
}
.full-width-header .rs-header .topbar-area.home9-topbar .topbar-right li:last-child {
  margin: 0;
}
.full-width-header .rs-header .topbar-area.home11-topbar {
  background: #0c8b51 !important;
}
.full-width-header .rs-header .topbar-area.home11-topbar .topbar-contact li {
  color: #ffffff;
  border-right: 1px solid #00822b;
}
.full-width-header .rs-header .topbar-area.home11-topbar .topbar-contact li i {
  margin-right: 4px;
}
.full-width-header .rs-header .topbar-area.home11-topbar .topbar-contact li i:before {
  font-size: 14px;
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area.home11-topbar .topbar-contact li a {
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area.home11-topbar .topbar-contact li a:hover {
  color: #d1d1d1;
}
.full-width-header .rs-header .topbar-area.home11-topbar .topbar-contact li:last-child {
  margin: 0;
  border: none;
}
.full-width-header .rs-header .topbar-area.home11-topbar .toolbar-sl-share .opening {
  color: #ffffff;
  display: inline-block;
  border-right: 1px solid #00822b;
  font-size: 14px;
  line-height: 50px;
  margin-right: 15px;
  padding-right: 18px;
}
.full-width-header .rs-header .topbar-area.home11-topbar .toolbar-sl-share .opening i {
  margin-right: 5px;
}
.full-width-header .rs-header .topbar-area.home11-topbar .toolbar-sl-share .opening i:before {
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area.home11-topbar .toolbar-sl-share li {
  display: inline-block;
  margin-right: 14px;
}
.full-width-header .rs-header .topbar-area.home11-topbar .toolbar-sl-share li a {
  line-height: 30px;
  color: #ffffff;
}
.full-width-header .rs-header .topbar-area.home11-topbar .toolbar-sl-share li a:hover {
  color: #ff5421;
}
.full-width-header .rs-header .topbar-area.home11-topbar .toolbar-sl-share li:last-child {
  margin: 0;
  border: none;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 {
  background: #f9f7f8 !important;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 .topbar-contact li {
  color: #333333;
  border-right: 1px solid #f0ecee;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 .topbar-contact li i {
  margin-right: 4px;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 .topbar-contact li i:before {
  font-size: 14px;
  color: #ff5421;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 .topbar-contact li a {
  color: #333333;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 .topbar-contact li a:hover {
  color: #ff5421;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 .toolbar-sl-share .opening {
  color: #333333;
  border-right: 1px solid #f0ecee;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 .toolbar-sl-share .opening i:before {
  color: #ff5421;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 .toolbar-sl-share li a {
  color: #333333;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 .toolbar-sl-share li a:hover {
  color: #ff5421;
}
.full-width-header .rs-header .topbar-area.home11-topbar.modify1 .toolbar-sl-share li:last-child {
  margin: 0;
  border: none;
}
.full-width-header .rs-header .logo-part {
  position: relative;
  z-index: 9;
}
.full-width-header .rs-header .logo-part img {
  /*max-height: 40px;*/
}
.full-width-header .rs-header .menu-area {
  transition: all 0.3s ease;
}
.full-width-header .rs-header .menu-area .logo-area {
  position: relative;
  height: 90px;
  line-height: 90px;
}
.full-width-header .rs-header .menu-area .logo-area img {
  transition: 0.4s;
  -webkit-transition: 0.4s;
  max-height: 48px;
}
.full-width-header .rs-header .menu-area .logo-area .dark {
  display: none;
}
.full-width-header .rs-header .menu-area .logo-area .light {
  display: inherit;
}
.full-width-header .rs-header .menu-area .responsive-logo {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: none;
}
.full-width-header .rs-header .menu-area .responsive-logo img {
  max-height: 30px;
}
.full-width-header .rs-header .menu-area .logo-cat-wrap {
  display: flex;
  align-items: center;
  height: 120px;
  line-height: 120px;
}
.full-width-header .rs-header .menu-area .logo-cat-wrap .categories-btn .cat-menu-inner {
  top: 120px;
}
.full-width-header .rs-header .menu-area .logo-cat-wrap .categories-btn .cat-menu-inner li {
  height: 45px;
  line-height: 45px;
}
.full-width-header .rs-header .menu-area .logo-cat-wrap .categories-btn .cat-menu-inner li a {
  padding: 0 30px;
}
.full-width-header .rs-header .menu-area .rs-menu-area {
  display: flex;
  align-items: center;
  justify-content: center;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.nav-menu li {
  display: inline-block;
  margin-right: 50px;
  padding: 0;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.nav-menu li a {
  transition: all 0.3s ease;
  font-size: 15px;
  font-weight: 500;
  text-transform: uppercase;
  height: 120px;
  line-height: 120px;
  padding-right: 12px;
  margin-right: -12px;
  color: #ffffff;
  z-index: 1;
  position: relative;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.nav-menu li a:hover:before {
  /*content: "-";*/
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.nav-menu li a:before {
  /*content: "+";*/
  position: absolute;
  left: auto;
  right: 0;
  top: 50%;
  text-align: center;
  display: block;
  cursor: pointer;
  transform: translateY(-50%);
  transition: all .5s ease;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.nav-menu li:last-child {
  margin-right: 0!important;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.nav-menu li:last-child i {
  margin: 0;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.nav-menu.onepage-menu .active-menu a {
  color: #21a7d0 !important;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu {
  background: #21a7d0;
  margin: 0;
  padding: 15px 0;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu li {
  margin: 0;
  border: none;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu li a {
  height: unset !important;
  line-height: unset !important;
  color: #ffffff !important;
  font-weight: 400 !important;
  text-transform: capitalize;
  padding: 10px 30px !important;
  text-align: right;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #273c66 !important;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu li a:before {
  display: none;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu li:last-child {
  margin: 0;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu li.current-menu-item > a,
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu li.active > a {
  color: #273c66 !important;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu li.children-mega-menu .child-mega-menu {
  width: 100%;
  opacity: 1 !important;
  position: unset;
  transform: translateY(0px);
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu li.children-mega-menu .child-mega-menu .child-single-megamenu {
  width: 50%;
  float: left;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu li.children-mega-menu .child-mega-menu .child-single-megamenu .sub-menu {
  padding: 0;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.sub-menu.right-menu {
  left: unset;
  right: 0;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.mega-menu {
  background: #273c66;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.mega-menu .mega-menu-container {
  padding: 0;
  margin: 0;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.mega-menu .mega-menu-container .single-megamenu {
  width: 50%;
  float: left;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.mega-menu .mega-menu-container .single-megamenu .sub-menu .menu-title {
  font-size: 17px;
  font-weight: 600;
  padding: 10px 30px;
  color: #ffffff;
  text-transform: capitalize;
  transition: all 0.3s ease;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul.mega-menu .mega-menu-container .single-megamenu:hover .sub-menu .menu-title {
  color: #ffffff;
}
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul li a:hover,
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul li.active a,
.full-width-header .rs-header .menu-area .main-menu .rs-menu ul li.current-menu-item > a {
  color: #21a7d0 !important;
}
.full-width-header .rs-header .menu-area .nav-expander {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
}
.full-width-header .rs-header .menu-area .nav-expander span {
  height: 2px;
  width: 30px;
  display: block;
  background: #ffffff;
  margin: 6px 0;
  transition: all 0.3s ease;
}
.full-width-header .rs-header .menu-area .nav-expander span.dot1 {
  margin-top: 0;
}
.full-width-header .rs-header .menu-area .nav-expander span.dot3 {
  margin-bottom: 0;
}
.full-width-header .rs-header .menu-area .nav-expander:hover span {
  background: #ff5421;
}
.full-width-header .rs-header .menu-area .nav-expander.style2 span {
  background: #111111;
}
.full-width-header .rs-header .menu-area .nav-expander.style2:hover span {
  background: #ff5421;
}
.full-width-header .rs-header .menu-area .nav-expander.style3 span {
  background: #111111;
}
.full-width-header .rs-header .menu-area .nav-expander.style3:hover span {
  background: #21a7d0;
}
.full-width-header .rs-header .menu-area .nav-expander.style4 span {
  background: #ffffff;
}
.full-width-header .rs-header .menu-area .nav-expander.style4:hover span {
  background: #21a7d0;
}
.full-width-header .rs-header .menu-area .nav-expander.style5 span {
  background: #ffffff;
}
.full-width-header .rs-header .menu-area .nav-expander.style5:hover span {
  background: #ccc;
}
.full-width-header .rs-header .menu-area .nav-expander.style6 span {
  background: #ffffff;
}
.full-width-header .rs-header .menu-area .nav-expander.style6:hover span {
  background: #f4bf00;
}
.full-width-header .rs-header .menu-area .appointment-cart {
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 9;
}
.full-width-header .rs-header .menu-area .appointment-cart ul.cart {
  text-align: right;
}
.full-width-header .rs-header .menu-area .appointment-cart ul.cart li {
  display: inline-block;
  position: relative;
  cursor: pointer;
  padding-right: 15px;
}
.full-width-header .rs-header .menu-area .appointment-cart ul.cart li i {
  color: #ffffff;
  transition: all 0.3s ease;
}
.full-width-header .rs-header .menu-area .appointment-cart ul.cart li i:hover {
  color: #ffffff;
}
.full-width-header .rs-header .menu-area .expand-btn-inner {
  margin-right: 55px;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li {
  display: inline-block;
  margin-right: 15px;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li.cart-inner {
  position: relative;
  margin-right: 15px;
  padding-right: 20px;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li.cart-inner .cart-icon {
  position: relative;
  height: 120px;
  line-height: 120px;
  z-index: 1;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li.cart-inner .cart-icon .cart-count {
  position: absolute;
  min-width: 18px;
  min-height: 18px;
  line-height: 18px;
  font-size: 11px;
  border-radius: 50%;
  background: #f2f2f2;
  color: #505050;
  top: -13px;
  right: -17px;
  text-align: center;
  transition: all 0.3s ease;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li.cart-inner:before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 20px;
  background: #7d8aa3;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li.cart-inner.no-border {
  padding-right: 0;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li.cart-inner.no-border:before {
  display: none;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li > a {
  color: #ffffff;
  display: inline-block;
  cursor: pointer;
  z-index: 1;
  position: relative;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li > a.short-border {
  position: relative;
  padding-right: 20px;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li > a.short-border:before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 20px;
  background: #7d8aa3;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li > a i:before {
  font-size: 15px;
  font-weight: 600;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li > a i.flaticon-shopping-bag-1:before {
  font-weight: normal;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li > a:hover {
  color: #ffffff;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li a.apply-btn {
  text-transform: uppercase;
  color: #ffffff;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li a.apply-btn:hover {
  color: #21a7d0;
}
.full-width-header .rs-header .menu-area .expand-btn-inner li:last-child {
  margin-right: 0 !important;
}
.full-width-header .rs-header .menu-area .categories-btn .cat-btn {
  display: inline-flex;
  align-items: center;
  background: transparent;
  padding: 0;
  border: none;
  outline: unset;
  color: #ffffff;
  text-transform: uppercase;
}
.full-width-header .rs-header .menu-area .categories-btn .cat-btn i {
  margin-right: 5px;
  transition: all 0.3s ease;
}
.full-width-header .rs-header .menu-area .categories-btn .cat-btn:hover i {
  color: #21a7d0;
}
.full-width-header .rs-header .menu-area .categories-btn .cat-menu-inner {
  position: absolute;
  top: 120px;
  width: 200px;
  background: #21a7d0;
  padding: 15px 0;
}
.full-width-header .rs-header .menu-area .categories-btn .cat-menu-inner li a {
  color: #ffffff;
  padding: 10px 30px;
  display: inline-block;
  text-transform: capitalize;
  width: 100%;
}
.full-width-header .rs-header .menu-area .categories-btn .cat-menu-inner li a:hover {
  color: #273c66;
}
.full-width-header .rs-header .menu-area.sticky {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(39, 60, 102, 0.9) !important;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.2);
}
.full-width-header .rs-header .menu-area.sticky .logo-area img {
  max-height: 35px;
}
.full-width-header .rs-header .menu-area.sticky .logo-area .dark {
  display: inherit;
}
.full-width-header .rs-header .menu-area.sticky .logo-area .light {
  display: none;
}
.full-width-header .rs-header .menu-area.sticky .logo-cat-wrap {
  height: 90px;
  line-height: 90px;
}
.full-width-header .rs-header .menu-area.sticky .logo-cat-wrap .categories-btn .cat-menu-inner {
  top: 90px;
}
.full-width-header .rs-header .menu-area.sticky .main-menu .rs-menu ul.nav-menu > li > a {
  height: 90px;
  line-height: 90px;
  color: #494949;
  font-size: 25px
}
.full-width-header .rs-header .menu-area.sticky .main-menu .rs-menu ul.nav-menu > li > a:hover {
  color: #21a7d0;
}
.full-width-header .rs-header .menu-area.sticky .nav-expander span {
  background: #111111;
}
.full-width-header .rs-header .menu-area.sticky .nav-expander:hover span {
  background: #ff5421;
}
.full-width-header .rs-header .menu-area.sticky .nav-expander.style3 span {
  background: #111111;
}
.full-width-header .rs-header .menu-area.sticky .nav-expander.style3:hover span {
  background: #21a7d0;
}
.full-width-header .rs-header .menu-area.sticky .nav-expander.style5 span {
  background: #ffffff;
}
.full-width-header .rs-header .menu-area.sticky .nav-expander.style5:hover span {
  background: #ccc;
}
.full-width-header .rs-header .menu-area.sticky .appointment-cart ul.cart li i {
  color: #494949;
}
.full-width-header .rs-header .menu-area.sticky .appointment-cart ul.cart li i:hover {
  color: #21a7d0;
}
.full-width-header .rs-header .menu-area.sticky .expand-btn-inner li.cart-inner .cart-icon {
  height: 90px;
  line-height: 90px;
}
.full-width-header .rs-header .menu-area.sticky .expand-btn-inner li.cart-inner .woocommerce-mini-cart {
  padding-top: 90px;
}
.full-width-header .rs-header .menu-area.sticky .expand-btn-inner li .cart-icon .cart-count {
  background: #21a7d0;
  color: #ffffff;
}
.full-width-header .rs-header .menu-area.sticky .expand-btn-inner li > a {
  color: #494949;
}
.full-width-header .rs-header .menu-area.sticky .expand-btn-inner li > a:hover {
  color: #21a7d0;
}
.full-width-header .rs-header .right_menu_togle {
  background: #fafafa;
  padding: 30px 50px;
  width: 500px;
  right: -500px;
  z-index: 999999;
  text-align: center;
  transition: all 0.3s ease;
}
.full-width-header .rs-header .right_menu_togle .close-btn {
  overflow: visible;
  padding: 0;
}
.full-width-header .rs-header .right_menu_togle .close-btn .line {
  width: 40px;
  height: 40px;
  line-height: 40px;
}
.full-width-header .rs-header .right_menu_togle .close-btn .line span {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 1px !important;
  height: 20px !important;
  background: #ffffff;
  border: none !important;
  transition: all 0.3s ease;
}
.full-width-header .rs-header .right_menu_togle .close-btn .line span.line2 {
  transform: translate(-50%, -50%) rotate(-45deg);
}
.full-width-header .rs-header .right_menu_togle .close-btn #nav-close {
  position: relative;
  float: unset;
  width: 40px;
  height: 40px;
  line-height: 40px;
  border: unset;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #21a7d0;
}
.full-width-header .rs-header .right_menu_togle .close-btn #nav-close:hover {
  transform: rotate(180deg);
}
.full-width-header .rs-header .right_menu_togle .canvas-logo {
  padding-left: 0;
  padding-bottom: 30px;
  margin-top: 50px;
}
.full-width-header .rs-header .right_menu_togle .canvas-logo img {
  max-height: 35px;
}
.full-width-header .rs-header .right_menu_togle .offcanvas-gallery {
  display: inline-block;
}
.full-width-header .rs-header .right_menu_togle .offcanvas-gallery .gallery-img {
  width: 33%;
  float: left;
  padding: 0 5px 10px;
}
.full-width-header .rs-header .right_menu_togle .offcanvas-gallery .gallery-img .image-popup {
  display: block;
  overflow: hidden;
}
.full-width-header .rs-header .right_menu_togle .offcanvas-gallery .gallery-img .image-popup img {
  transition: all 0.3s ease;
}
.full-width-header .rs-header .right_menu_togle .offcanvas-gallery .gallery-img .image-popup:hover img {
  transform: scale(1.1);
}
.full-width-header .rs-header .right_menu_togle .map-img {
  margin-top: 25px;
}
.full-width-header .rs-header .right_menu_togle .sidebarnav_menu li a {
  font-size: 17px;
  color: #222;
  padding-left: 0;
}
.full-width-header .rs-header .right_menu_togle .sidebarnav_menu li a:hover {
  color: #21a7d0;
}
.full-width-header .rs-header .right_menu_togle .canvas-contact {
  padding: 50px 0 0;
}
.full-width-header .rs-header .right_menu_togle .canvas-contact .social li {
  display: inline-block;
  padding-right: 20px;
}
.full-width-header .rs-header .right_menu_togle .canvas-contact .social li a {
  color: #111111;
}
.full-width-header .rs-header .right_menu_togle .canvas-contact .social li a i {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}
.full-width-header .rs-header .right_menu_togle .canvas-contact .social li a:hover {
  color: #21a7d0;
}
.full-width-header .rs-header .right_menu_togle .canvas-contact .social li:last-child {
  padding: 0;
}
.full-width-header.header-style1 {
  width: 100%;
  z-index: 999;
}
.full-width-header.header-style1 .rs-header .menu-area {
  background: #273c66;
}
.full-width-header.header-style1 .rs-header .menu-area .main-menu .mobile-logo-part {
  display: none;
}
.full-width-header.header-style1 .rs-header .menu-area .main-menu .rs-menu ul.nav-menu li.logo-part {
  padding: 0;
  margin: 0 90px 0 60px;
}
.full-width-header.header-style1 .rs-header .menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu {
  left: unset;
  right: 0;
  background: #ffffff;
  min-width: 770px;
}
.full-width-header.header-style1 .rs-header .menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu {
  width: 33.33%;
  float: left;
}
.full-width-header.header-style1 .rs-header .menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu .sub-menu {
  background: unset;
  padding-right: 30px;
  padding-left: 30px;
  padding-bottom: 30px;
  padding-top: 30px;
}
.full-width-header.header-style1 .rs-header .menu-area.sticky {
  background: #273c66 !important;
}
.full-width-header.header-style1 .rs-header .menu-area.sticky .main-menu .rs-menu ul.nav-menu > li > a {
  color: #ffffff;
}
.full-width-header.header-style1 .rs-header .menu-area.sticky .expand-btn-inner li > a {
  color: #ffffff;
}
.full-width-header.header-style1 .rs-header .menu-area.sticky .expand-btn-inner li > a:hover {
  color: #ffffff;
}
.full-width-header.header-style1 .rs-header .menu-area.sticky .expand-btn-inner li a.apply-btn {
  color: #ffffff;
}
.full-width-header.header-style1 .rs-header .menu-area.sticky .expand-btn-inner li a.apply-btn:hover {
  color: #21a7d0;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area {
  background: #ffffff !important;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area {
  justify-content: flex-end;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li {
  margin-left: 35px;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #333333;
  font-weight: 500;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #0c8b51 !important;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover,
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li.current-menu-item > a {
  color: #0c8b51 !important;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #0c8b51;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li {
  margin-left: 35px;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  color: #333333;
  font-weight: 500;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #0c8b51 !important;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #0c8b51 !important;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .nav-expander {
  color: #111111;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .rs-menu-area .main-menu .nav-expander:hover {
  color: #273c66;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .expand-btn-inner {
  margin-right: 0;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .expand-btn-inner li .woocommerce-mini-cart .cart-bottom-part {
  background: #0c8b51;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .expand-btn-inner li .woocommerce-mini-cart .crt-btn {
  background: #ffffff;
  color: #111111;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .expand-btn-inner li .woocommerce-mini-cart .crt-btn:hover {
  background: #f2f2f2;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .expand-btn-inner li.cart-inner {
  border-color: #a9b1c2;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .expand-btn-inner li > a {
  color: #394459;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area .expand-btn-inner li > a:hover {
  color: #0c8b51 !important;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area.sticky {
  background: #ffffff !important;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area.sticky .logo-cat-wrap {
  display: flex;
  align-items: center;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area.sticky .logo-cat-wrap .logo-part .dark-logo {
  display: none;
}
.full-width-header.header-style1.home12-modifiy .rs-header .menu-area.sticky .logo-cat-wrap .logo-part .light-logo {
  display: inherit;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area {
  justify-content: flex-end;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  margin-right: 0;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #ff5421 !important;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover,
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li.current-menu-item > a {
  color: #ff5421 !important;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #ff5421 !important;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  color: #ffffff !important;
  margin-right: 0;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #ccc !important;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #ccc !important;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu.right {
  left: auto;
  right: 100%;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu {
  left: unset;
  right: 0;
  background: #ff5421;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu {
  width: 33.33%;
  float: left;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu .sub-menu {
  padding-right: 30px;
  padding-left: 40px;
  padding-bottom: 40px;
  padding-top: 35px;
  background: unset;
}
.full-width-header.header-style1.home14-style .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu .sub-menu li a {
  color: #f00;
}
.full-width-header.header-style1.home14-style .expand-btn-inner li a:hover i {
  color: #ff5421;
}
.full-width-header.header-style1.home14-style .expand-btn-inner li.cart-inner {
  border-color: #1c2f5e !important;
}
.full-width-header.header-style1.home14-style .expand-btn-inner li > a {
  color: #111111 !important;
}
.full-width-header.header-style1.home14-style .expand-btn-inner li > a:hover {
  color: #ff5421 !important;
}
.full-width-header.header-style1.home14-style .expand-btn-inner li .woocommerce-mini-cart .cart-bottom-part {
  background: #ff5421;
}
.full-width-header.header-style1.home14-style .expand-btn-inner li .woocommerce-mini-cart .crt-btn {
  background: #ffffff;
  color: #111111;
}
.full-width-header.header-style1.home14-style .expand-btn-inner li .woocommerce-mini-cart .crt-btn:hover {
  background: #f2f2f2;
}
.full-width-header.header-style2 {
  width: 100%;
  z-index: 999;
}
.full-width-header.header-style2 .rs-header .menu-area {
  background: #ffffff;
}
.full-width-header.header-style2 .rs-header .menu-area .logo-cat-wrap .logo-part .dark-logo {
  display: inherit;
}
.full-width-header.header-style2 .rs-header .menu-area .logo-cat-wrap .logo-part .light-logo {
  display: none;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area {
  justify-content: flex-end;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li {
  margin-right: 35px;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #394459;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #21a7d0;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li {
  margin: 0;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  font-weight: 400 !important;
  color: #ffffff;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #ddd !important;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #ddd !important;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu {
  left: unset;
  right: 0;
  background: #21a7d0;
  min-width: 770px;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu {
  width: 33.33%;
  float: left;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu .sub-menu {
  padding-right: 30px;
  padding-left: 30px;
  padding-bottom: 30px;
  padding-top: 30px;
}
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li a:hover,
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li.active a,
.full-width-header.header-style2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li.current-menu-item > a {
  color: #21a7d0 !important;
}
.full-width-header.header-style2 .rs-header .menu-area .expand-btn-inner li.cart-inner {
  border-color: #a9b1c2;
}
.full-width-header.header-style2 .rs-header .menu-area .expand-btn-inner li > a {
  color: #394459;
}
.full-width-header.header-style2 .rs-header .menu-area .expand-btn-inner li > a:hover {
  color: #21a7d0;
}
.full-width-header.header-style2 .rs-header .menu-area .categories-btn .cat-btn {
  color: #394459;
}
.full-width-header.header-style2 .rs-header .menu-area .categories-btn .cat-btn i {
  margin-right: 10px;
}
.full-width-header.header-style2 .rs-header .menu-area .categories-btn .cat-btn:hover i {
  color: #21a7d0;
}
.full-width-header.header-style2 .rs-header .menu-area .categories-btn .cat-menu-inner {
  background: #21a7d0;
}
.full-width-header.header-style2 .rs-header .menu-area .categories-btn .cat-menu-inner li a {
  color: #ffffff;
}
.full-width-header.header-style2 .rs-header .menu-area .categories-btn .cat-menu-inner li a:hover {
  color: #273c66;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky {
  background: #273c66 !important;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky .logo-cat-wrap {
  display: flex;
  align-items: center;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky .logo-cat-wrap .logo-part .dark-logo {
  display: none;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky .logo-cat-wrap .logo-part .light-logo {
  display: inherit;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky .main-menu .rs-menu ul.nav-menu > li > a {
  color: #ffffff;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky .expand-btn-inner li > a {
  color: #ffffff;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky .expand-btn-inner li > a:hover {
  color: #21a7d0;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky .nav-expander.style3 span {
  background: #ffffff;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky .nav-expander.style3:hover span {
  background: #ccc;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky .categories-btn .cat-btn {
  color: #ffffff;
}
.full-width-header.header-style2 .rs-header .menu-area.sticky .categories-btn .cat-btn:hover i {
  color: #21a7d0;
}
.full-width-header.header-style2.modify1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area {
  background: transparent;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #ffffff;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #21a7d0;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  font-weight: 400 !important;
  color: #ffffff;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #ddd !important;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #ddd !important;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li a:hover,
.full-width-header.header-style2.modify1 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li.active a,
.full-width-header.header-style2.modify1 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li.current-menu-item > a {
  color: #21a7d0 !important;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .expand-btn-inner li.cart-inner {
  border-color: #a9b1c2;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .expand-btn-inner li > a {
  color: #ffffff;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .expand-btn-inner li > a:hover {
  color: #21a7d0;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .categories-btn .cat-btn {
  color: #ffffff;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area .categories-btn .cat-btn:hover i {
  color: #21a7d0;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky {
  background: black !important;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky .logo-cat-wrap {
  display: flex;
  align-items: center;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky .logo-cat-wrap .logo-part .dark-logo {
  display: none;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky .logo-cat-wrap .logo-part .light-logo {
  display: inherit;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky .main-menu .rs-menu ul.nav-menu > li > a {
  color: #ffffff;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky .expand-btn-inner li > a {
  color: #ffffff;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky .expand-btn-inner li > a:hover {
  color: #21a7d0;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky .categories-btn .cat-btn {
  color: #ffffff;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky .categories-btn .cat-btn:hover i {
  color: #21a7d0;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky .nav-expander span {
  background: #ffffff;
}
.full-width-header.header-style2.modify1 .rs-header .menu-area.sticky .nav-expander:hover span {
  background: #ccc;
}
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul.nav-menu li {
  margin-right: 35px;
  background-color: black;
  color: #fff !important;
}
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #ffffff;
  font-size: 15px;
  font-family: 'DroidArabicKufiRegular';
  font-weight: normal;
  font-style: normal;
}
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #f4bf00 !important;
}
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: black !important;
}
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul.sub-menu li {
  margin: 0;
}
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  font-weight: 400 !important;
  color: #fff !important;
}
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #f4bf00 !important;
}
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #f4bf00 !important;
}
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu {
  background: #ffffff;
}
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul li a:hover,
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul li.active a,
.full-width-header.header-style2.modify1.header-home6 .rs-header .rs-menu-area .main-menu .rs-menu ul li.current-menu-item > a {
  color: #f4bf00 !important;
}
.full-width-header.header-style3 {
  width: 100%;
  z-index: 999;
}
.full-width-header.header-style3 .rs-header .menu-area {
  background: #21a7d0;
}
.full-width-header.header-style3 .rs-header .menu-area .logo-part {
  position: absolute;
  left: -260px;
  top: 50%;
  transform: translateY(-50%);
}
.full-width-header.header-style3 .rs-header .menu-area .logo-part .small-logo {
  display: none;
}
.full-width-header.header-style3 .rs-header .menu-area .logo-cat-wrap .categories-btn {
  padding-left: 45px;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li {
  margin-right: 35px;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #394459;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #21a7d0;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li {
  margin: 0;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  font-weight: 400 !important;
  color: #ffffff;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #273c66 !important;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #273c66 !important;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu {
  left: unset;
  right: 0;
  background: #21a7d0;
  min-width: 780px;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu {
  width: 33.33%;
  float: left;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu .sub-menu {
  padding-right: 32px;
  padding-left: 32px;
  padding-bottom: 32px;
  padding-top: 32px;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li a:hover,
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li.active a,
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li.current-menu-item > a {
  color: #21a7d0 !important;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .nav-expander {
  color: #111111;
}
.full-width-header.header-style3 .rs-header .menu-area .rs-menu-area .main-menu .nav-expander:hover {
  color: #273c66;
}
.full-width-header.header-style3 .rs-header .menu-area .expand-btn-inner li.cart-inner {
  border-color: #a9b1c2;
}
.full-width-header.header-style3 .rs-header .menu-area .expand-btn-inner li > a {
  color: #394459;
}
.full-width-header.header-style3 .rs-header .menu-area .expand-btn-inner li > a:hover {
  color: #21a7d0;
}
.full-width-header.header-style3 .rs-header .menu-area .categories-btn .cat-btn {
  color: #394459;
}
.full-width-header.header-style3 .rs-header .menu-area .categories-btn .cat-btn i {
  margin-right: 10px;
}
.full-width-header.header-style3 .rs-header .menu-area .categories-btn .cat-btn:hover i {
  color: #21a7d0;
}
.full-width-header.header-style3 .rs-header .menu-area .categories-btn .cat-menu-inner {
  background: #21a7d0;
}
.full-width-header.header-style3 .rs-header .menu-area .categories-btn .cat-menu-inner li a {
  color: #ffffff;
}
.full-width-header.header-style3 .rs-header .menu-area .categories-btn .cat-menu-inner li a:hover {
  color: #273c66;
}
.full-width-header.header-style3 .rs-header .menu-area.sticky {
  background: #21a7d0 !important;
}
.full-width-header.header-style3 .rs-header .menu-area.sticky .logo-cat-wrap {
  display: flex;
  align-items: center;
}
.full-width-header.header-style3 .rs-header .menu-area.sticky .logo-cat-wrap .logo-part .dark-logo {
  display: none;
}
.full-width-header.header-style3 .rs-header .menu-area.sticky .logo-cat-wrap .logo-part .light-logo {
  display: inherit;
}
.full-width-header.header-style3 .rs-header .right_menu_togle .close-btn #nav-close {
  background: #4e49a1;
}
.full-width-header.header-style3 .rs-header .right_menu_togle .close-btn #nav-close:hover {
  transform: rotate(180deg);
}
.full-width-header.header-style3 .rs-header .right_menu_togle .canvas-logo {
  padding-left: 0;
  padding-bottom: 30px;
  margin-top: 50px;
}
.full-width-header.header-style3 .rs-header .right_menu_togle .canvas-logo img {
  max-height: 35px;
}
.full-width-header.header-style3 .rs-header .right_menu_togle .canvas-contact {
  padding: 50px 0 0;
}
.full-width-header.header-style3 .rs-header .right_menu_togle .canvas-contact .social li a:hover {
  color: #4e49a1;
}
.full-width-header.header-style3 .rs-header .right_menu_togle .canvas-contact .social li:last-child {
  padding: 0;
}
.full-width-header.header-style3.modify .rs-header .menu-area {
  display: flex;
  background: #ffffff;
}
.full-width-header.header-style3.modify .rs-header .menu-area .logo-part {
  background: #21a7d0;
  left: 0;
  height: 120px;
  line-height: 120px;
  width: 350px;
  text-align: center;
}
.full-width-header.header-style3.modify .rs-header .menu-area .logo-cat-wrap {
  display: flex;
  align-items: center;
  float: left;
}
.full-width-header.header-style3.modify .rs-header .menu-area .rs-menu-area {
  float: right;
  margin-right: -130px;
}
.full-width-header.header-style3.modify .rs-header .menu-area .expand-btn-inner {
  margin: 0;
  margin-left: 50px;
}
.full-width-header.header-style3.modify .rs-header .menu-area .expand-btn-inner .apply-btn {
  background: #21a7d0;
  color: #ffffff;
  line-height: 1;
  padding: 11px 24px;
  border-radius: 30px;
}
.full-width-header.header-style3.modify .rs-header .menu-area .expand-btn-inner .apply-btn:hover {
  background: #1a84a4;
}
.full-width-header.header-style3.modify .rs-header .menu-area .expand-btn-inner .nav-expander {
  top: 0;
  right: 0;
  width: 130px;
  height: 120px;
  line-height: 120px;
  background: #21a7d0;
  text-align: center;
  transform: unset;
}
.full-width-header.header-style3.modify .rs-header .menu-area .expand-btn-inner .nav-expander .bar {
  display: inline-block;
}
.full-width-header.header-style3.modify .rs-header .menu-area .expand-btn-inner .nav-expander:hover {
  color: #e6e6e6;
}
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .main-menu .rs-menu ul.nav-menu > li > a,
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .categories-btn .cat-btn,
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .expand-btn-inner li > a {
  color: #ffffff;
}
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .main-menu .rs-menu ul.nav-menu > li > a:hover,
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .categories-btn .cat-btn:hover,
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .expand-btn-inner li > a:hover {
  color: #111111 !important;
}
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .main-menu .rs-menu ul.nav-menu > li > a:hover i,
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .categories-btn .cat-btn:hover i,
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .expand-btn-inner li > a:hover i {
  color: #111111 !important;
}
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .main-menu .rs-menu ul li.current-menu-item > a {
  color: #111111 !important;
}
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .logo-part {
  height: 90px;
  line-height: 90px;
}
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .expand-btn-inner {
  margin: 0;
  margin-left: 50px;
}
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .expand-btn-inner li.cart-inner {
  border-color: #ffffff;
}
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .expand-btn-inner .apply-btn {
  background: #ffffff;
  color: #21a7d0;
}
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .expand-btn-inner .apply-btn:hover {
  color: #111111;
}
.full-width-header.header-style3.modify .rs-header .menu-area.sticky .expand-btn-inner .nav-expander {
  height: 90px;
  line-height: 90px;
}
.full-width-header.header-style3.modify.home-7-modify2 .rs-header .menu-area .categories-btn .cat-btn {
  color: #505050;
}
.full-width-header.header-style3.modify.home-7-modify2 .rs-header .menu-area .categories-btn .cat-btn:hover {
  color: #21a7d0;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area {
  display: flex;
  background: #ffffff;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .logo-part {
  left: 0;
  height: 120px;
  line-height: 120px;
  width: 350px;
  text-align: center;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .logo-cat-wrap {
  float: left;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .logo-cat-wrap .categories-btn .cat-btn {
  color: #394459;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .logo-cat-wrap .categories-btn .cat-btn i {
  margin-right: 10px;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .logo-cat-wrap .categories-btn .cat-btn:hover i {
  color: #4e49a1;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .logo-cat-wrap .categories-btn .cat-menu-inner {
  background: #4e49a1;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .logo-cat-wrap .categories-btn .cat-menu-inner li a {
  color: #ffffff;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .logo-cat-wrap .categories-btn .cat-menu-inner li a:hover {
  color: #21a7d0;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area {
  justify-content: flex-end;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li {
  margin-right: 35px;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #394459;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #4e49a1;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li {
  margin: 0;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  font-weight: 400 !important;
  color: #ffffff !important;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #ccc !important;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #ccc !important;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu {
  background: #4e49a1;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li a:hover,
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul li.current-menu-item > a {
  color: #4e49a1 !important;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .expand-btn-inner {
  margin: 0;
  margin-left: 50px;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .expand-btn-inner li .woocommerce-mini-cart .cart-bottom-part {
  background: #4e49a1;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .expand-btn-inner li .woocommerce-mini-cart .crt-btn {
  background: #ffffff;
  color: #111111;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .expand-btn-inner li .woocommerce-mini-cart .crt-btn:hover {
  background: #f2f2f2;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .expand-btn-inner .apply-btn {
  background: #4e49a1;
  color: #ffffff;
  line-height: 1;
  padding: 16px 24px;
  border-radius: 30px;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .expand-btn-inner .apply-btn:hover {
  background: #625eaa;
  color: #ffffff;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .expand-btn-inner .nav-expander {
  top: 0;
  right: 0;
  width: 130px;
  height: 120px;
  line-height: 120px;
  background: #4e49a1;
  text-align: center;
  transform: unset;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .expand-btn-inner .nav-expander .bar {
  display: inline-block;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area .expand-btn-inner .nav-expander:hover {
  color: #e6e6e6;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky {
  background: #ffffff !important;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .main-menu .rs-menu ul.nav-menu > li > a,
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .categories-btn .cat-btn,
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .expand-btn-inner li > a {
  color: #394459;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .main-menu .rs-menu ul.nav-menu > li > a:hover,
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .categories-btn .cat-btn:hover,
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .expand-btn-inner li > a:hover {
  color: #111111 !important;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .main-menu .rs-menu ul li.current-menu-item > a {
  color: #4e49a1 !important;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .expand-btn-inner li.cart-inner {
  border-color: #ffffff;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .expand-btn-inner .apply-btn {
  background: #4e49a1;
  color: #ffffff;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .expand-btn-inner .apply-btn:hover {
  background: #625eaa;
  color: #ffffff !important;
}
.full-width-header.header-style3.home11-modify2 .rs-header .menu-area.sticky .expand-btn-inner .nav-expander {
  height: 90px;
  line-height: 90px;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area {
  background: #ffffff !important;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #111111 !important;
  font-weight: 600 !important;
  padding-right: 18px !important;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0 !important;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:last-child {
  margin-right: none !important;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li.current-menu-item > a {
  color: #21a7d0 !important;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #ffffff !important;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li {
  margin: 0;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  font-weight: 400 !important;
  color: #111111 !important;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #21a7d0 !important;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #21a7d0 !important;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu {
  background: #fff;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area.sticky {
  background: #ffffff !important;
}
.full-width-header.header-style3.home-7-modify2 .rs-header .menu-area.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0;
}
.full-width-header.home8-style4 {
  width: 100%;
  z-index: 999;
}
.full-width-header.home8-style4 .rs-header .menu-area {
  background: #ffffff !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .logo-part {
  position: relative;
  z-index: 9;
}
.full-width-header.home8-style4 .rs-header .menu-area .logo-area {
  position: relative;
  height: 90px;
  line-height: 90px;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #111111 !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #ff5421 !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:last-child {
  margin-right: none !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li.current-menu-item > a {
  color: #ff5421 !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #ffffff !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li {
  margin: 0;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  font-weight: 400 !important;
  color: #111111 !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #ff5421 !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #ff5421 !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu {
  left: unset;
  right: 0;
  background: #ffffff;
  min-width: 770px;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu {
  width: 33.33%;
  float: left;
}
.full-width-header.home8-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu .sub-menu {
  padding-right: 30px;
  padding-left: 30px;
  padding-top: 30px;
  padding-bottom: 30px;
}
.full-width-header.home8-style4 .rs-header .menu-area .expand-btn-inner .cart-icon i {
  font-size: 20px;
}
.full-width-header.home8-style4 .rs-header .menu-area .expand-btn-inner li.cart-inner {
  border-color: #1c2f5e !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .expand-btn-inner li > a {
  color: #111111 !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .expand-btn-inner li > a:hover {
  color: #ff5421 !important;
}
.full-width-header.home8-style4 .rs-header .menu-area .expand-btn-inner li .woocommerce-mini-cart .cart-bottom-part {
  background: #ff5421;
}
.full-width-header.home8-style4 .rs-header .menu-area .expand-btn-inner li .woocommerce-mini-cart .crt-btn {
  background: #ffffff;
  color: #111111;
}
.full-width-header.home8-style4 .rs-header .menu-area .expand-btn-inner li .woocommerce-mini-cart .crt-btn:hover {
  background: #f2f2f2;
}
.full-width-header.home8-style4 .rs-header .menu-area .expand-btn-inner .apply-btn {
  background: #ff5421;
  color: #ffffff !important;
  font-size: 15px;
  line-height: 24px;
  padding: 10px 24px;
  border-radius: 30px;
}
.full-width-header.home8-style4 .rs-header .menu-area .expand-btn-inner .nav-expander {
  color: #111111;
}
.full-width-header.home8-style4 .rs-header .menu-area.sticky {
  background: #ffffff !important;
}
.full-width-header.home8-style4 .rs-header .menu-area.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0;
}
.full-width-header.home8-style4 .rs-header .right_menu_togle .close-btn #nav-close {
  background: #ff5421;
}
.full-width-header.home8-style4 .rs-header .right_menu_togle .sidebarnav_menu li a:hover {
  color: #ff5421;
}
.full-width-header.home8-style4 .rs-header .right_menu_togle .canvas-contact .social li a:hover {
  color: #ff5421;
}
.full-width-header.home8-style4.main-home {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.full-width-header.home8-style4.main-home .menu-area {
  background: transparent !important;
}
.full-width-header.home8-style4.main-home .menu-area .logo-part .sticky-logo {
  display: none;
}
.full-width-header.home8-style4.main-home .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li {
  margin-right: 45px;
}
.full-width-header.home8-style4.main-home .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #ffffff !important;
}
.full-width-header.home8-style4.main-home .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #ffffff !important;
  min-width: 250px;
  padding-bottom: 30px;
  padding-top: 30px;
  border-radius: 0px;
  border-bottom: 5px solid #ff5421;
  border-color: #ff5421 !important;
}
.full-width-header.home8-style4.main-home .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li {
  margin: 0;
}
.full-width-header.home8-style4.main-home .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  font-weight: 400 !important;
  color: #111111 !important;
}
.full-width-header.home8-style4.main-home .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #ff5421 !important;
}
.full-width-header.home8-style4.main-home .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #ff5421 !important;
}
.full-width-header.home8-style4.main-home .menu-area .rs-menu-area .main-menu .rs-menu-toggle {
  color: #ffffff !important;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .logo-part .sticky-logo {
  display: block;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .logo-part .normal-logo {
  display: none;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #111111 !important;
  padding-right: 18px !important;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #ff5421 !important;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:last-child {
  margin-right: none !important;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li.current-menu-item > a {
  color: #ff5421 !important;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .expand-btn-inner li .cart-icon .cart-count {
  background: #21a7d0;
  color: #ffffff;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .expand-btn-inner li > a {
  color: #494949;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .expand-btn-inner li > a i:before {
  color: #111111 !important;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .expand-btn-inner li > a:hover {
  color: #21a7d0;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .expand-btn-inner li > a:hover i:before {
  color: #ff5421 !important;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .expand-btn-inner .user-icon > a {
  line-height: 90px;
  height: 90px;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .expand-btn-inner .woocommerce-mini-cart {
  padding-top: 90px;
}
.full-width-header.home8-style4.main-home .menu-area.menu-sticky.sticky .expand-btn-inner .nav-expander {
  color: #111111 !important;
}
.full-width-header.home8-style4.main-home .expand-btn-inner .cart-icon i {
  font-size: 20px;
}
.full-width-header.home8-style4.main-home .expand-btn-inner li a i:before {
  color: #ffffff !important;
}
.full-width-header.home8-style4.main-home .expand-btn-inner li a:hover i:before {
  color: #ff5421 !important;
}
.full-width-header.home8-style4.main-home .expand-btn-inner .user-icon > a {
  position: relative;
  line-height: 120px;
  height: 120px;
  display: flex;
  align-items: center;
}
.full-width-header.home8-style4.main-home .expand-btn-inner .user-icon > a i {
  color: #fff;
  border: 1px solid #eee;
  width: 32px;
  display: block;
  text-align: center;
  border-radius: 50%;
  font-size: 14px;
  height: 32px;
  line-height: 29px;
}
.full-width-header.home8-style4.main-home .expand-btn-inner .user-icon > a i:before {
  color: #ffffff;
}
.full-width-header.home8-style4.main-home .expand-btn-inner .nav-expander {
  color: #ffffff !important;
}
.full-width-header.home8-style4.main-home .expand-btn-inner .nav-expander:hover {
  color: #ff5421 !important;
}
.full-width-header.home8-style4.main-home .right_menu_togle .close-btn #nav-close {
  background: #ff5421;
}
.full-width-header.home8-style4.main-home .right_menu_togle .sidebarnav_menu li a:hover {
  color: #ff5421;
}
.full-width-header.home8-style4.main-home .right_menu_togle .canvas-contact .social li a:hover {
  color: #ff5421;
}
.full-width-header.home8-style4.home9 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.full-width-header.home8-style4.home9 .menu-area {
  background: transparent !important;
  box-shadow: unset !important;
}
.full-width-header.home8-style4.home9 .menu-area .logo-part .sticky-logo {
  display: none;
}
.full-width-header.home8-style4.home9 .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #ffffff !important;
  font-weight: 600 !important;
  padding-right: 18px !important;
}
.full-width-header.home8-style4.home9 .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #f4bf00 !important;
}
.full-width-header.home8-style4.home9 .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:last-child {
  margin-right: none !important;
}
.full-width-header.home8-style4.home9 .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li.current-menu-item > a {
  color: #f4bf00 !important;
}
.full-width-header.home8-style4.home9 .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #ffffff !important;
  min-width: 250px;
  padding-bottom: 30px;
  padding-top: 30px;
  border-radius: 0px;
  border-bottom: 5px solid #f4bf00;
  border-color: #f4bf00 !important;
}
.full-width-header.home8-style4.home9 .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li {
  margin: 0;
}
.full-width-header.home8-style4.home9 .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  font-weight: 400 !important;
  color: #111111 !important;
}
.full-width-header.home8-style4.home9 .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #f4bf00 !important;
}
.full-width-header.home8-style4.home9 .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #f4bf00 !important;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .logo-part .sticky-logo {
  display: block;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .logo-part .normal-logo {
  display: none;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #111111 !important;
  padding-right: 18px !important;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #f4bf00 !important;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:last-child {
  margin-right: none !important;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li.current-menu-item > a {
  color: #f4bf00 !important;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .expand-btn-inner li .cart-icon .cart-count {
  background: #21a7d0;
  color: #ffffff;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .expand-btn-inner li > a {
  color: #494949;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .expand-btn-inner li > a i:before {
  color: #111111 !important;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .expand-btn-inner li > a:hover {
  color: #21a7d0;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .expand-btn-inner li > a:hover i:before {
  color: #f4bf00 !important;
}
.full-width-header.home8-style4.home9 .menu-area.menu-sticky.sticky .expand-btn-inner .user-icon > a {
  line-height: 90px;
  height: 90px;
}
.full-width-header.home8-style4.home9 .expand-btn-inner .cart-icon i {
  font-size: 20px;
}
.full-width-header.home8-style4.home9 .expand-btn-inner li a i:before {
  color: #ffffff !important;
}
.full-width-header.home8-style4.home9 .expand-btn-inner li a:hover i:before {
  color: #f4bf00 !important;
}
.full-width-header.home8-style4.home9 .expand-btn-inner li .woocommerce-mini-cart .cart-bottom-part {
  background: #f4bf00 !important;
}
.full-width-header.home8-style4.home9 .expand-btn-inner .user-icon > a {
  position: relative;
  line-height: 120px;
  height: 120px;
  display: flex;
  align-items: center;
}
.full-width-header.home8-style4.home9 .expand-btn-inner .user-icon > a i:before {
  color: #ffffff;
}
.full-width-header.home8-style4.home9 .right_menu_togle .close-btn #nav-close {
  background: #f4bf00;
}
.full-width-header.home8-style4.home9 .right_menu_togle .sidebarnav_menu li a:hover {
  color: #f4bf00;
}
.full-width-header.home8-style4.home9 .right_menu_togle .canvas-contact .social li a:hover {
  color: #f4bf00;
}
.full-width-header.home8-style4.home13 .menu-area .rs-menu-area {
  justify-content: flex-end;
}
.full-width-header.home8-style4.home13 .menu-area .rs-menu .nav-menu .rs-mega-menu .mega-menu {
  background: #fff;
  left: unset;
  right: 0;
}
.full-width-header.home8-style4.home13 .menu-area .rs-menu .nav-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu {
  width: 33.33%;
  float: left;
}
.full-width-header.home8-style4.home13 .menu-area .rs-menu .nav-menu .rs-mega-menu .mega-menu .mega-menu-container .single-megamenu .sub-menu {
  background: unset;
  padding-right: 30px;
  padding-left: 41px;
}
.full-width-header.home8-style4.home13 .expand-btn-inner {
  margin-right: unset;
}
.full-width-header.home8-style4.home13 .expand-btn-inner li a i {
  border-right: 1px solid #fff;
  padding-right: 24px;
}
.full-width-header.home8-style4.home13 .expand-btn-inner .user-icon > a i {
  border: unset;
}
.full-width-header.transparent {
  position: absolute;
}
.full-width-header.transparent .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #ffffff !important;
}
.full-width-header.transparent .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0;
}
.full-width-header.transparent .rs-header .menu-area .expand-btn-inner li.cart-inner {
  border-color: #1c2f5e !important;
}
.full-width-header.transparent .rs-header .menu-area .expand-btn-inner li > a {
  color: #ffffff !important;
}
.full-width-header.transparent .rs-header .menu-area .expand-btn-inner li > a:hover {
  color: #21a7d0;
}
.full-width-header.transparent .rs-header .menu-area.menu-sticky {
  background: none;
  border-bottom: none;
}
.full-width-header.home7-style4 .rs-header .menu-area {
  background: #ffffff !important;
}
.full-width-header.home7-style4 .rs-header .menu-area .logo-cat-wrap .logo-part {
  background: red !important;
}
.full-width-header.home7-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #111111 !important;
}
.full-width-header.home7-style4 .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0;
}
.full-width-header.home7-style4 .rs-header .menu-area .expand-btn-inner .nav-expander {
  right: -196px;
  position: absolute;
  color: #ffffff !important;
  background: #21a7d0;
  padding: 40px 40px;
}
.full-width-header.home1-modifiy .rs-header .menu-area {
  background: #ffffff !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li {
  margin-right: 40px !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a {
  color: #111111 !important;
  font-weight: 600 !important;
  padding-right: 18px !important;
  margin-right: unset !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0 !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:last-child {
  margin-right: 0 !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.nav-menu li.current-menu-item > a {
  color: #21a7d0 !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu {
  background: #ffffff !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li {
  margin: 0;
}
.full-width-header.home1-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a {
  font-weight: 400 !important;
  color: #111111 !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li a:hover {
  color: #21a7d0 !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .rs-menu-area .main-menu .rs-menu ul.sub-menu li.active a {
  color: #21a7d0 !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .expand-btn-inner .cart-icon i {
  font-size: 20px;
}
.full-width-header.home1-modifiy .rs-header .menu-area .expand-btn-inner li.cart-inner {
  border-color: #1c2f5e !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .expand-btn-inner li > a {
  color: #111111 !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .expand-btn-inner li > a:hover {
  color: #21a7d0 !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area .expand-btn-inner .apply-btn {
  background: #21a7d0;
  color: #ffffff !important;
  font-size: 15px;
  line-height: 24px;
  padding: 10px 24px;
  border-radius: 30px;
}
.full-width-header.home1-modifiy .rs-header .menu-area .expand-btn-inner .nav-expander {
  color: #111111;
}
.full-width-header.home1-modifiy .rs-header .menu-area.sticky {
  background: #ffffff !important;
}
.full-width-header.home1-modifiy .rs-header .menu-area.sticky .rs-menu-area .main-menu .rs-menu ul.nav-menu li a:hover {
  color: #21a7d0;
}
body .search-modal .modal-content {
  background: transparent;
  position: initial;
  border: 0;
}
body .search-modal .search-block input {
  height: 60px;
  line-height: 60px;
  padding: 0 15px;
  background: transparent;
  border-width: 0 0 1px 0;
  border-radius: 0;
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: none;
  color: #ffffff;
  font-weight: 600;
  font-size: 18px;
}
body .search-modal .close {
  color: #ffffff;
  margin-top: 20px;
  font-size: 14px;
  background-color: transparent;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  opacity: 1;
  outline: none;
  transition: all 0.3s ease;
}
body .search-modal .close:hover {
  color: #ff5421 !important;
  opacity: 1 !important;
}
body .modal-backdrop {
  opacity: 0.95 !important;
}
/*------------------------------------
    04. Sticky Menu CSS
--------------------------------------*/
.menu-sticky {
  background: #fff;
  margin: 0;
  z-index: 999;
  width: 100%;
  top: 0;
  position: relative;
}
@-webkit-keyframes sticky-animation {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
  }
}
@keyframes sticky-animation {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
/* ------------------------------------
    05. Banner Section CSS
---------------------------------------*/
.rs-banner.style1 {
  padding: 125px 0 425px;
  background: url(assets/images/banner/home1.jpg);
  background-size: cover;
  background-position: center;
}
.rs-banner.style1 .banner-content {
  max-width: 750px;
  margin: 0 auto;
}
.rs-banner.style1 .banner-content .banner-title {
  font-size: 55px;
  margin-bottom: 16px;
}
.rs-banner.style1 .banner-content .desc {
  font-size: 20px;
  line-height: 30px;
}
.rs-banner.style3 {
  background: url(assets/images/banner/home3.jpg);
  background-size: cover;
  background-position: center;
  height: 100vh;
  display: grid;
  align-items: center;
}
.rs-banner.style3 .banner-content .banner-title {
  font-size: 70px;
  line-height: 1.2;
  margin-bottom: 19px;
  font-family: 'Amiri', serif;
  font-family: 'Cairo', sans-serif;
}
.rs-banner.style3 .banner-content .banner-desc {
  font-size: 18px;
  line-height: 30px;
  margin-bottom: 41px;
  position: relative;
  z-index: 9;
}
.rs-banner.style3 .banner-content .banner-btn li {
  display: inline;
  margin-right: 30px;
}
.rs-banner.style3 .banner-content .banner-btn li:last-child {
  margin: 0;
}
.rs-banner.style3 .banner-content .banner-image {
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
}
.rs-banner.style4 {
  background: url(assets/images/banner/home4.jpg);
  background-size: cover;
  background-position: center;
  min-height: 935px;
  display: grid;
  align-items: center;
}
.rs-banner.style4 .banner-content .banner-title {
  font-size: 70px;
  line-height: 1.2;
  margin-bottom: 5px;
}
.rs-banner.style4 .banner-line-shape {
  position: absolute;
  top: -205px;
  left: -25px;
}
.rs-banner.style4 .banner-circle-shape {
  text-align: right;
  position: absolute;
  bottom: -125px;
  right: -15px;
}
.rs-banner.style4 .banner-dots-shape {
  position: absolute;
  bottom: -220px;
  left: -200px;
}
.rs-banner.style5 {
  position: relative;
  background-color: #f3fafc;
  min-height: 750px;
  display: grid;
  align-items: flex-end;
  z-index: 9;
}
.rs-banner.style5 .banner-content {
  padding-bottom: 200px;
}
.rs-banner.style5 .banner-content .sub-title {
  color: #111111;
  font-size: 22px;
  line-height: 1.2;
  font-weight: 500;
  margin-bottom: 20px;
}
.rs-banner.style5 .banner-content .banner-title {
  color: #111111;
  font-size: 75px;
  line-height: 1.2;
  margin-bottom: 39px;
}
.rs-banner.style5 .banner-img {
  position: absolute;
  bottom: 0;
  right: 0;
}
.rs-banner.style6 {
  background: black;
  /*background: url(assets/images/banner/home6/bg.png);*/
  background-size: cover;
  background-position: center;
  margin-bottom: 54px;
}
.rs-banner.style6 .container {
  position: relative;
}
.rs-banner.style6 .banner-content {
  padding: 232px 0 388px;
}
.rs-banner.style6 .banner-content .banner-title {
  font-weight: 700;
  line-height: 80px;
  font-size: 60px;
     font-family: 'DroidArabicKufiRegular';
   font-weight: normal;
   font-style: normal;
}
.rs-banner.style6 .banner-content .desc {
  font-size: 18px;
  line-height: 30px;
}
.rs-banner.style6 .shape-img {
  position: absolute;
  bottom: -54px;
}
.rs-banner.style6 .shape-img.left {
  left: 15px;
  z-index: 1;
}
.rs-banner.style6 .shape-img.center {
  left: 50%;
  transform: translateX(-50%);
}
.rs-banner.style6 .shape-img.center .inner {
  width: 810px;
  height: 385px;
  margin: 0 auto;
  position: relative;
}
.rs-banner.style6 .shape-img.center .inner .spiner {
  position: absolute;
  content: '';
}
.rs-banner.style6 .shape-img.center .inner .spiner.one {
  top: 0;
  left: 37%;
}
.rs-banner.style6 .shape-img.center .inner .spiner.one img:nth-child(1) {
  position: relative;
  right: 4px;
  top: 9px;
}
.rs-banner.style6 .shape-img.center .inner .spiner.two {
  top: 56%;
  left: 0;
}
.rs-banner.style6 .shape-img.center .inner .spiner.two img:nth-child(1) {
  max-width: 16px;
}
.rs-banner.style6 .shape-img.center .inner .spiner.two img:nth-child(2) {
  position: relative;
  right: -3px;
  top: -4px;
  max-width: 25px;
}
.rs-banner.style6 .shape-img.center .inner .spiner.three {
  top: 40%;
  right: 0;
}
.rs-banner.style6 .shape-img.center .inner .spiner.three img:nth-child(1) {
  max-width: 16px;
  position: relative;
  top: 29px;
  left: 31px;
}
.rs-banner.style6 .shape-img.center .inner .spiner.three img:nth-child(2) {
  position: relative;
  right: 7px;
  top: 1px;
  max-width: 25px;
}
.rs-banner.style6 .shape-img.right {
  right: 15px;
}
.rs-banner.style7 {
  background: url(assets/images/banner/home7/banner-bg.jpg);
  background-size: cover;
  background-position: center;
  position: relative;
  min-height: 870px;
}
.rs-banner.style7 .banner-content {
  position: relative;
  padding: 200px 0;
}
.rs-banner.style7 .banner-content .banner-title {
  font-size: 70px;
  line-height: 80px;
  font-weight: 800;
}
.rs-banner.style7 .icons {
  position: absolute;
}
.rs-banner.style7 .icons.one {
  top: 136px;
  right: 50px;
}
.rs-banner.style7 .icons.two {
  left: 50%;
  bottom: 100px;
}
.rs-banner.style7 .icons.three {
  top: 95px;
  left: -160px;
}
.rs-banner.style7 .icons.four {
  bottom: 210px;
  left: -62px;
}
.rs-banner.style7 .img-part {
  position: absolute;
  right: 0;
  bottom: 88px;
}
.rs-banner.style8 {
  background: url(assets/images/banner/home9.jpg);
  background-size: cover;
  background-position: center;
  display: grid;
  align-items: center;
  min-height: 960px;
}
.rs-banner.style8 .banner-content {
  margin-top: 30px;
}
.rs-banner.style8 .banner-content .sl-sub-title {
  font-size: 48px;
  line-height: 50px;
  font-weight: 400;
  color: #ffffff;
  margin-bottom: 22px;
}
.rs-banner.style8 .banner-content .sl-title {
  font-size: 90px;
  line-height: 90px;
  font-weight: 700;
  color: #ffffff;
  padding-right: 374px;
  margin-bottom: 45px;
}
.rs-banner.style9 {
  background: url(assets/images/banner/home11/banner.jpg);
  background-size: cover;
  display: grid;
  align-items: center;
  min-height: 880px;
  position: relative;
}
.rs-banner.style9 .banner-content {
  max-width: 615px;
}
.rs-banner.style9 .banner-content .banner-title {
  font-size: 70px;
  line-height: 1.3;
  color: #4e49a1;
}
.rs-banner.style9 .banner-content .desc {
  font-size: 18px;
  line-height: 32px;
  font-weight: 400;
  color: #4e49a1;
}
.rs-banner.style9 .shape-img .spiner {
  position: absolute;
  bottom: 0;
}
.rs-banner.style9 .shape-img .spiner.one {
  top: -40%;
  right: 70%;
}
.rs-banner.style9 .shape-img .spiner.two {
  left: 56%;
}
.rs-banner.style9 .shape-img .spiner.three {
  left: 290px;
  bottom: -118px;
}
.rs-banner.style9 .shape-img .spiner.four {
  top: 0%;
  left: -14%;
}
.rs-banner.style9 .social-icon {
  position: absolute;
  bottom: 35px;
  left: 70px;
}
.rs-banner.style9 .social-icon .icon-cart li {
  display: inline;
  margin-right: 8px;
}
.rs-banner.style9 .social-icon .icon-cart li a {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 44px;
  border-radius: 100%;
  background: #ffffff;
  color: #4e49a1;
  text-align: center;
}
.rs-banner.style9 .social-icon .icon-cart li a:hover {
  color: #21a7d0;
}
.rs-banner.style9 .social-icon .icon-cart li i {
  font-size: 20px;
}
.rs-banner.style10 {
  background: url(assets/images/banner/home12/banner-home12.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  display: grid;
  align-items: center;
  min-height: 820px;
  position: relative;
}
.rs-banner.style10 .banner-content {
  padding: 120px 0 100px;
}
.rs-banner.style10 .banner-content .sl-sub-title {
  font-size: 20px;
  line-height: 25px;
  font-weight: 400;
  color: #0c8b51;
  text-transform: uppercase;
  padding-bottom: 10px;
}
.rs-banner.style10 .banner-content .sl-title {
  font-size: 50px;
  line-height: 70px;
  font-weight: 800;
  color: #171f32;
  text-transform: uppercase;
  padding-bottom: 10px;
}
.rs-banner.style10 .img-part {
  width: 550px;
  position: absolute;
  top: 70px;
  left: 17%;
  z-index: 111;
}
.rs-banner.style10 .banner-intro-box .shape-img {
  position: absolute;
  top: 55px;
  left: 45px;
}
.rs-banner.style10 .banner-intro-box .shape-img img {
  width: 110px;
}
.rs-banner.style10 .banner-intro-box .intro-img {
  position: absolute;
  right: 8%;
  top: 60px;
}
.rs-banner.style10 .banner-intro-box .intro-img img {
  width: 600px;
}
.rs-banner.style11 {
  background: url(assets/images/banner/home13/banner.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  display: grid;
  align-items: center;
  min-height: 950px;
  z-index: 1;
}
.rs-banner.style11 .content-part {
  padding: 180px 0 100px;
}
.rs-banner.style11 .content-part .sub-title {
  font-size: 16px;
  line-height: 27px;
  font-weight: 400;
  color: #ff5421;
  text-transform: uppercase;
  display: block;
  margin-bottom: 15px;
}
.rs-banner.style11 .content-part .title {
  font-size: 70px;
  line-height: 75px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 32px;
}
.rs-banner.style11 .images-part {
  padding-top: 100px;
}
.rs-banner.style11 .images-part img {
  max-width: unset;
  width: 700px;
}
.rs-banner.style11 .icons-spiner .circle {
  top: 18%;
  right: 81%;
  position: absolute;
}
.rs-banner.style11 .icons-spiner .squre-img {
  position: absolute;
  right: 60%;
  bottom: 20%;
}
.rs-banner.style11 .icons-spiner .dot-img {
  position: absolute;
  right: 15%;
  top: 20%;
  z-index: -111;
}
.rs-banner.style12 {
  background-color: #F9F7F8;
  background-image: url(assets/images/banner/home14/border.png);
  background-position: bottom left;
  background-repeat: no-repeat;
  padding: 100px 0px 100px 0px;
}
.rs-banner.style12 .banner-content .sub-text {
  font-size: 16px;
  line-height: 27px;
  font-weight: 500;
  color: #ff5421;
  text-transform: uppercase;
  display: block;
  margin-bottom: 10px;
}
.rs-banner.style12 .banner-content .title {
  font-size: 48px;
  line-height: 62px;
  font-weight: 800;
  color: #101010;
  margin-bottom: 30px;
}
.rs-banner.style12 .banner-content .title span {
  color: #ff5421;
}
.rs-banner.style12 .banner-content .desc {
  font-size: 20px;
  line-height: 32px;
  font-weight: 400;
  color: #333333;
  margin-bottom: 45px;
}
.rs-banner.style12 .banner-content .search-widget .search-wrap {
  position: relative;
}
.rs-banner.style12 .banner-content .search-widget .search-wrap [type=search] {
  outline: none;
  padding: 20px 30px;
  border: none;
  border-radius: 3px;
  box-shadow: none;
  padding-right: 77px;
  width: 100%;
}
.rs-banner.style12 .banner-content .search-widget .search-wrap button {
  background: transparent;
  border: medium none;
  color: #ff5421;
  padding: 11px 15px 12px;
  position: absolute;
  display: block;
  right: 10px;
  top: 10px;
  z-index: 10;
  font-size: 20px;
  font-weight: 700;
}
.rs-banner.style12 .banner-img img {
  max-width: unset;
  width: 620px;
}
.rs-banner .left-shape {
  position: absolute;
  top: -30px;
  left: 40px;
  z-index: -1;
}
.rs-banner .right-shape {
  position: absolute;
  top: 65px;
  right: -90px;
  z-index: -1;
}
.rs-banner .bottom-shape {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -1;
}
.rs-banner .bottom-shape img {
  width: 100%;
}
/* ------------------------------------
    06. Slider Section CSS
---------------------------------------*/
.rs-slider.style1 {
  position: relative;
}
.rs-slider.style1 .slider-content {
  padding: 250px 0;
}
.rs-slider.style1 .slider-content .sl-sub-title {
  font-size: 48px;
  line-height: 58px;
  font-family: 'Nunito', sans-serif;
  margin-bottom: 24px;
}
.rs-slider.style1 .slider-content .sl-title {
  font-size: 90px;
  line-height: 100px;
  margin-bottom: 39px;
  margin-left: -5px;
}
.rs-slider.style1 .slider-content.slide1 {
  background: url(assets/images/slider/h2-1.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.rs-slider.style1 .slider-content.slide2 {
  background: url(assets/images/slider/h2-2.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.rs-slider.style1 .owl-nav .owl-next,
.rs-slider.style1 .owl-nav .owl-prev {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 30px;
  width: 42px;
  height: 42px;
  line-height: 42px;
  border-radius: 50%;
  background: #21a7d0;
  text-align: center;
  color: #ffffff;
  transition: all 0.5s;
  transition-delay: 0.70s;
  opacity: 0;
  visibility: hidden;
}
.rs-slider.style1 .owl-nav .owl-next i:before,
.rs-slider.style1 .owl-nav .owl-prev i:before {
  content: "\f138";
  font-family: Flaticon;
}
.rs-slider.style1 .owl-nav .owl-next {
  right: 30px;
  left: unset;
}
.rs-slider.style1 .owl-nav .owl-next i:before {
  content: "\f137";
}
.rs-slider.style1:hover .owl-nav .owl-next,
.rs-slider.style1:hover .owl-nav .owl-prev {
  left: 50px;
  transition-delay: 0s;
  visibility: visible;
  opacity: 1;
}
.rs-slider.style1:hover .owl-nav .owl-next {
  right: 50px;
  left: unset;
}
.rs-slider.style2 .slide-part {
  background: #f9f8f8;
  padding: 100px 0 0;
}
.rs-slider.style2 .slide-part .img-part {
  margin-bottom: -80px;
  overflow: hidden;
}
.rs-slider.style2 .slide-part .content {
  padding: 100px 0 100px;
}
.rs-slider.style2 .slide-part .content .title {
  font-size: 55px;
  line-height: 70px;
  font-family: 'Nunito', sans-serif;
  margin-bottom: 40px;
  color: #031a3d;
}
.rs-slider.style2 .slide-part .content .sub-title {
  font-size: 16px;
  line-height: 25px;
  color: #ff5421;
  margin-bottom: 20px;
  text-transform: uppercase;
}
.rs-slider.style2 .owl-nav .owl-next,
.rs-slider.style2 .owl-nav .owl-prev {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 30px;
  width: 42px;
  height: 42px;
  line-height: 42px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  cursor: pointer;
  text-align: center;
  color: #ffffff;
  transition: all 0.5s;
  transition-delay: 0.70s;
  opacity: 0;
  visibility: hidden;
}
.rs-slider.style2 .owl-nav .owl-next i:before,
.rs-slider.style2 .owl-nav .owl-prev i:before {
  content: "\f138";
  font-family: Flaticon;
}
.rs-slider.style2 .owl-nav .owl-next:hover,
.rs-slider.style2 .owl-nav .owl-prev:hover {
  background: #ff5421;
  color: #ffffff;
}
.rs-slider.style2 .owl-nav .owl-next {
  right: 30px;
  left: unset;
}
.rs-slider.style2 .owl-nav .owl-next i:before {
  content: "\f137";
}
.rs-slider.style2:hover .owl-nav .owl-next,
.rs-slider.style2:hover .owl-nav .owl-prev {
  left: 50px;
  transition-delay: 0s;
  visibility: visible;
  opacity: 1;
}
.rs-slider.style2:hover .owl-nav .owl-next {
  right: 50px;
  left: unset;
}
.rs-slider.style2 .owl-carousel .owl-stage-outer {
  padding: 0 0 80px;
  margin: 0 0 -80px;
}
.rs-slider.main-home {
  position: relative;
}
.rs-slider.main-home .slider-content {
  text-align: center;
}
.rs-slider.main-home .slider-content .content-part {
  padding: 250px 35px 400px;
  max-width: 900px;
  margin: 0 auto;
}
.rs-slider.main-home .slider-content .content-part .sl-sub-title {
  font-size: 22px;
  line-height: 30px;
  font-weight: 500;
  color: #ff5421;
  margin-bottom: 22px;
  text-transform: uppercase;
}
.rs-slider.main-home .slider-content .content-part .sl-title {
  font-size: 70px;
  line-height: 80px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 65px;
}
.rs-slider.main-home .slider-content.slide1 {
  background: url(assets/images/slider/main-home/1.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.rs-slider.main-home .slider-content.slide2 {
  background: url(assets/images/slider/main-home/2.jpg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.rs-slider.main-home .owl-nav .owl-next,
.rs-slider.main-home .owl-nav .owl-prev {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 30px;
  width: 42px;
  height: 42px;
  line-height: 42px;
  border-radius: 50%;
  background: #ff5421;
  text-align: center;
  color: #ffffff;
  transition: all 0.5s;
  transition-delay: 0.70s;
  opacity: 0;
  visibility: hidden;
}
.rs-slider.main-home .owl-nav .owl-next i:before,
.rs-slider.main-home .owl-nav .owl-prev i:before {
  content: "\f138";
  font-family: Flaticon;
}
.rs-slider.main-home .owl-nav .owl-next:hover,
.rs-slider.main-home .owl-nav .owl-prev:hover {
  background: #ed3600;
}
.rs-slider.main-home .owl-nav .owl-next {
  right: 30px;
  left: unset;
}
.rs-slider.main-home .owl-nav .owl-next i:before {
  content: "\f137";
}
.rs-slider.main-home:hover .owl-nav .owl-next,
.rs-slider.main-home:hover .owl-nav .owl-prev {
  left: 50px;
  transition-delay: 0s;
  visibility: visible;
  opacity: 1;
}
.rs-slider.main-home:hover .owl-nav .owl-next {
  right: 50px;
  left: unset;
}
/* ------------------------------------
    06. Breadcrumbs Section CSS
---------------------------------------*/
.rs-breadcrumbs {
  position: relative;
}
.rs-breadcrumbs .breadcrumbs-img img {
  width: 100%;
}
.rs-breadcrumbs .breadcrumbs-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-align: center;
}
.rs-breadcrumbs .breadcrumbs-text .page-title {
  font-size: 48px;
  margin-bottom: 20px;
  color: #ff5421;
}
.rs-breadcrumbs .breadcrumbs-text ul {
  padding: 0;
}
.rs-breadcrumbs .breadcrumbs-text ul li {
  color: #505050;
  display: inline-block;
  font-size: 16px;
  font-weight: 400;
}
.rs-breadcrumbs .breadcrumbs-text ul li a {
  position: relative;
  padding-right: 30px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  color: #505050;
}
.rs-breadcrumbs .breadcrumbs-text ul li a:before {
  right: 13px;
}
.rs-breadcrumbs .breadcrumbs-text ul li a:hover {
  color: #ff5421;
}
.rs-breadcrumbs .breadcrumbs-text.white-color .page-title {
  color: #ffffff;
}
.rs-breadcrumbs .breadcrumbs-text.white-color ul li {
  color: #ffffff;
}
.rs-breadcrumbs .breadcrumbs-text.white-color ul li a {
  color: #ffffff;
}
.rs-breadcrumbs .breadcrumbs-text.white-color ul li a:before,
.rs-breadcrumbs .breadcrumbs-text.white-color ul li a:after {
  background-color: #ffffff;
}
.rs-breadcrumbs .breadcrumbs-text.white-color ul li a:before {
  right: 13px;
}
.rs-breadcrumbs .breadcrumbs-text.white-color ul li a:hover {
  color: #ff5421;
}
.rs-breadcrumbs .breadcrumbs-text.padding {
  padding-top: 100px;
}
.breadcrumbs-overlay:after {
  content: '';
  position: absolute;
  background-color: rgba(17, 17, 17, 0.8);
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.breadcrumbs1 {
  background-image: url(assets/images/breadcrumbs/1.jpg);
  background-size: cover;
  background-position: center;
  background-position: center top;
}
/* ------------------------------------
    07. About Section CSS
---------------------------------------*/
.rs-about .histort-part {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  border: none;
  display: block;
}
.rs-about .histort-part .single-history {
  display: block;
  margin-bottom: 30px;
}
.rs-about .histort-part .single-history a {
  padding: 28px 20px 22px;
  box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 5px;
  width: 230px;
  font-size: 20px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  background: #171F32;
  transition: all 0.3s ease;
}
.rs-about .histort-part .single-history a .icon-part {
  display: block;
  margin-bottom: 15px;
}
.rs-about .histort-part .single-history a .icon-part i:before {
  font-size: 45px;
  line-height: 1;
  color: #ffffff;
}
.rs-about .histort-part .single-history a:hover,
.rs-about .histort-part .single-history a.active {
  background: #ff5421;
}
.rs-about .histort-part .last-item {
  margin-bottom: 0;
}
.rs-about .history-list .title {
  font-size: 22px;
  padding: 0;
  margin-bottom: 15px;
  font-family: 'Nunito', sans-serif;
  font-weight: 700;
  color: #111111;
}
.rs-about .history-list .title:before {
  display: none;
}
.rs-about .history-list li {
  position: relative;
  padding-left: 40px;
  margin-bottom: 10px;
}
.rs-about .history-list li:before {
  content: '';
  position: absolute;
  top: 10px;
  left: 25px;
  width: 5px;
  height: 5px;
  background: #111111;
}
.rs-about .history-list li:last-child {
  margin-bottom: 0;
}
.rs-about.style1 .about-part {
  background: #e7f4f6;
  padding: 60px 60px 70px;
  border-radius: 5px;
  margin-top: -60px;
}
.rs-about.style1 .about-part .desc {
  font-size: 18px;
  line-height: 30px;
  color: #505050;
}
.rs-about.style1 .about-part .sign-part {
  display: flex;
  align-items: center;
}
.rs-about.style1 .about-part .sign-part .img-part {
  padding-right: 30px;
}
.rs-about.style1 .about-part .sign-part .author-part span {
  display: block;
}
.rs-about.style1 .about-part .sign-part .author-part .post {
  font-size: 18px;
}
.rs-about.style1 .notice-bord.style1 {
  margin-top: -60px;
}
.rs-about.style1 .notice-bord.style1 .title {
  background: #21a7d0;
  font-size: 20px;
  text-transform: uppercase;
  padding: 18px 25px;
  text-align: center;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 10px;
}
.rs-about.style1 .notice-bord.style1 li {
  position: relative;
  background: #e7f4f6;
  margin-bottom: 12px;
  border-radius: 3px;
  padding: 20px;
  padding-left: 0 !important;
  overflow: hidden;
}
.rs-about.style1 .notice-bord.style1 li .date {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  color: #21a7d0;
  border-right: 1px solid #21a7d0;
  padding-right: 10px;
  font-weight: 600;
  width: 60px;
}
.rs-about.style1 .notice-bord.style1 li .date span {
  font-size: 22px;
  font-family: 'Nunito', sans-serif;
  display: block;
}
.rs-about.style1 .notice-bord.style1 li .desc {
  padding-left: 95px;
  font-weight: 500;
}
.rs-about.style1 .notice-bord.style1 li:last-child {
  margin-bottom: 0;
}
.rs-about.style1 .img-part {
  position: relative;
}
.rs-about.style1 .img-part .shape {
  position: absolute;
}
.rs-about.style1 .img-part .top-center {
  top: -30px;
  left: 49%;
  z-index: -1;
}
.rs-about.style1 .img-part .left-bottom {
  left: 0;
  bottom: 0;
}
.rs-about.style1 .img-part .left-bottom.second {
  bottom: -40px;
}
.rs-about.style2 .about-intro {
  border-radius: 3px;
  background: url(assets/images/bg/about-intro-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  padding: 88px 50px 95px;
  box-shadow: 0 0 20px 0px rgba(17, 41, 88, 0.2);
}
.rs-about.style2 .couter-area .counter-item {
  text-align: center;
  padding: 34px 15px;
  border-radius: 3px;
}
.rs-about.style2 .couter-area .counter-item .number {
  margin-bottom: 7px;
  font-size: 42px;
}
.rs-about.style2 .couter-area .counter-item .title {
  font-size: 22px;
  color: #394459;
}
.rs-about.style2 .couter-area .counter-item.one {
  background: #fbf8cc;
}
.rs-about.style2 .couter-area .counter-item.two {
  background: #e9fbd5;
}
.rs-about.style2 .couter-area .counter-item.three {
  background: #dcf1fd;
}
.rs-about.style2 .grid-area .image-grid img {
  border-radius: 3px;
}
.rs-about.style3 .couter-area .counter-item {
  text-align: center;
  padding: 34px 15px;
  border-radius: 10px;
}
.rs-about.style3 .couter-area .counter-item .count-img {
  display: block;
  margin: 0 auto 23px;
}
.rs-about.style3 .couter-area .counter-item .number {
  margin-bottom: 7px;
  font-size: 42px;
  color: #111111;
}
.rs-about.style3 .couter-area .counter-item .title {
  font-size: 22px;
  color: #505050;
}
.rs-about.style3 .couter-area .counter-item.one {
  background: #fbf8cc;
}
.rs-about.style3 .couter-area .counter-item.two {
  background: #e9fbd5;
}
.rs-about.style3 .couter-area .counter-item.three {
  background: #dcf1fd;
}
.rs-about.style4 .img-part {
  position: relative;
}
.rs-about.style4 .img-part .about-main {
  position: relative;
  z-index: 9;
}
.rs-about.style4 .img-part .shape {
  position: absolute;
  z-index: -1;
  left: 0;
  bottom: 0;
}
.rs-about.style4 .img-part .circle-bg {
  z-index: 1;
}
.rs-about.style4 .about-content .title {
  font-size: 36px;
  font-weight: 800;
}
.rs-about.style4 .about-content .sub-title {
  color: #54647b;
  font-size: 20px;
  font-weight: 500;
  text-transform: capitalize;
  margin-bottom: 20px;
}
.rs-about.style4 .about-content .contact-part li {
  display: inline-flex;
  align-items: center;
  /*margin: 0 30px 0 0;*/
  /*float: left;*/
}
.rs-about.style4 .about-content .contact-part li span {
  display: block;
}
.rs-about.style4 .about-content .contact-part li .img-part {
  float: left;
  width: 70px;
  height: 70px;
  line-height: 60px;
  border-radius: 50%;
  border: 5px solid #e5e8eb;
  text-align: center;
  margin-right: 30px;
}
.rs-about.style4 .about-content .contact-part li .desc {
  font-size: 18px;
  font-weight: 500;
  color: #54647b;
}
.rs-about.style4 .about-content .contact-part li .desc span a {
  font-size: 24px;
  color: #111111;
}
.rs-about.style4 .about-content .contact-part li .desc a {
  color: #031a3d;
  font-size: 24px;
  font-weight: bold;
  margin-top: 10px;
  display: block;
}
.rs-about.style4 .about-content .contact-part li .desc a:hover {
  color: #21a7d0;
}
.rs-about.style4 .about-content .contact-part li:last-child {
  margin: 0;
}
.rs-about.style4 .about-content .book-part {
  position: absolute;
  right: 0;
  bottom: -25px;
  width: 775px;
}
.rs-about.style4 .about-content .book-part .single-book {
  padding: 20px 60px;
  background: #f3f8f9;
  float: left;
}
.rs-about.style5 {
  background-image: url(assets/images/bg/about-bg.jpg);
  background-size: cover;
  background-position: center;
}
.rs-about.style5 .about-content .title {
  font-size: 42px;
  font-weight: 800;
}
.rs-about.style5 .about-content .sub-title {
  color: #505050;
  font-size: 20px;
  font-weight: 500;
  text-transform: capitalize;
  margin-bottom: 20px;
}
.rs-about.style5 .about-content .contact-part li {
  position: relative;
  margin-bottom: 40px;
}
.rs-about.style5 .about-content .contact-part li span {
  display: block;
}
.rs-about.style5 .about-content .contact-part li .img-part {
  position: absolute;
  left: 0;
  width: 70px;
  height: 70px;
  line-height: 60px;
  border-radius: 50%;
  border: 5px solid #e5e8eb;
  text-align: center;
  margin-right: 30px;
}
.rs-about.style5 .about-content .contact-part li .desc {
  font-size: 18px;
  font-weight: 500;
  color: #505050;
  padding-left: 95px;
}
.rs-about.style5 .about-content .contact-part li .desc p {
  color: #031a3d;
  font-size: 24px;
  font-weight: bold;
  margin: 10px 0 0;
}
.rs-about.style5 .about-content .contact-part li .desc a {
  color: #111111;
  font-size: 24px;
  font-weight: bold;
  margin-top: 10px;
  display: block;
}
.rs-about.style5 .about-content .contact-part li .desc a:hover {
  color: #21a7d0;
}
.rs-about.style5 .about-content .contact-part li .desc .address {
  color: #111111;
  font-size: 22px;
  font-weight: bold;
  margin-top: 10px;
  display: block;
}
.rs-about.style5 .about-content .contact-part li:last-child {
  margin-bottom: 0;
}
.rs-about.style6 .desc {
  font-size: 16px;
  color: #505050;
  font-weight: 400;
}
.rs-about.style6 .shape-animate {
  position: relative;
}
.rs-about.style6 .shape-animate .transparent {
  position: absolute;
}
.rs-about.style6 .shape-animate .transparent.left {
  left: 27%;
}
.rs-about.style6 .shape-animate .transparent.right {
  right: 20%;
}
.rs-about.style9 .content .sub-title {
  font-size: 18px;
  line-height: 28px;
  font-weight: 500;
  color: #f4bf00;
  text-transform: uppercase;
}
.rs-about.style9 .content .sl-title {
  font-size: 36px;
  line-height: 45px;
  font-weight: 700;
  color: #111111;
}
.rs-about.style9 .content .desc {
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;
  color: #505050;
}
.rs-about.video-style {
  position: relative;
}
.rs-about.video-style .icon-shape {
  position: absolute;
  top: -130px;
  left: -130px;
}
.rs-about.video-style .dot-shape {
  position: absolute;
  right: -90px;
  bottom: 30px;
}
.rs-about.video-style .about-content {
  padding: 70px 70px 70px 160px;
  margin-left: -120px;
  margin-top: -70px;
}
.rs-about.video-img {
  background: url(assets/images/video/bg.png);
  background-repeat: no-repeat;
  background-position: center top;
  background-size: cover;
  border-radius: 10px;
  position: relative;
  padding-bottom: 500px;
}
.rs-about.video-img .shape-img {
  position: absolute;
  bottom: 0;
}
.rs-about.video-img .shape-img.left {
  left: 100px;
}
.rs-about.video-img .shape-img.center {
  left: 50%;
  transform: translateX(-50%);
}
.rs-about.video-img .shape-img.right {
  right: 100px;
}
.rs-about.style8 .main-content {
  padding: 40px 33px;
  background: #f9f8f8;
  position: relative;
  border-radius: 6px;
}
.rs-about.style8 .main-content .img-part {
  position: absolute;
  left: -240px;
  top: -50px;
  z-index: 111;
}
.rs-about.style8 .main-content .img-part img {
  border-radius: 6px;
}
.rs-about.style8 .main-content .images-title img {
  border-radius: 6px;
}
.rs-about.style9 .content-part {
  background-color: #F9F8F8;
  padding: 65px 30px 70px 100px;
  position: relative;
}
.rs-about.style9 .content-part .about-img {
  position: absolute;
  z-index: 111;
  left: -480px;
  top: 83px;
}
.rs-about.style9 .content-part .about-img img {
  border-radius: 6px;
}
.rs-about.orange-color .histort-part .single-history .icon-part i:before {
  color: #ff5421;
}
.rs-about.style10 {
  position: relative;
}
.rs-about.style10 .shape-icons .shape {
  position: absolute;
  bottom: 0;
}
.rs-about.style10 .shape-icons .shape.one {
  right: 49%;
  bottom: 28%;
}
.rs-about.style10 .shape-icons .shape.two {
  top: 19%;
  left: 23%;
}
/* ------------------------------------
    08. Services Section CSS
---------------------------------------*/
.rs-services.style1 .service-item {
  position: relative;
  overflow: hidden;
}
.rs-services.style1 .service-item .scale-img {
  transition: all 0.3s ease;
  transform: scale(1);
}
.rs-services.style1 .service-item .content-part {
  position: absolute;
  content: '';
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  z-index: 1;
  text-align: center;
}
.rs-services.style1 .service-item .content-part i {
  font-size: 40px;
  color: #ffffff;
}
.rs-services.style1 .service-item .content-part i:before {
  font-size: 40px;
}
.rs-services.style1 .service-item .content-part .title {
  font-size: 22px;
  margin: 17px 0 0 0;
}
.rs-services.style1 .service-item .content-part .title a {
  color: #ffffff;
}
.rs-services.style1 .service-item .content-part .title a:hover {
  opacity: 0.8;
}
.rs-services.style1 .service-item:after {
  position: absolute;
  content: '';
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #273c66;
  opacity: 0.9;
}
.rs-services.style1 .service-item.overly1:after {
  background: #273c66;
}
.rs-services.style1 .service-item.overly2:after {
  background: #21a7d0;
}
.rs-services.style1 .service-item.overly3:after {
  background: #772bea;
}
.rs-services.style1 .service-item.overly4:after {
  background: #16aaca;
}
.rs-services.style1 .service-item:hover .scale-img {
  transform: scale(1.1);
}
.rs-services.style2 .service-item {
  text-align: center;
  background: #f3fafc;
}
.rs-services.style2 .service-item .content-part {
  position: unset;
  transform: unset;
  padding: 60px 35px 50px;
}
.rs-services.style2 .service-item .content-part .icon-part i {
  color: #21a7d0;
}
.rs-services.style2 .service-item .content-part .icon-part i:before {
  font-size: 75px;
}
.rs-services.style2 .service-item .content-part .title {
  font-size: 22px;
  margin: 20px 0 0 0;
}
.rs-services.style2 .service-item .content-part .title a {
  color: #111111;
}
.rs-services.style2 .service-item .content-part .title a:hover {
  color: #21a7d0;
}
.rs-services.style2 .service-item .content-part .desc {
  margin-bottom: 15px;
  margin-top: 5px;
}
.rs-services.style2 .service-item .content-part .service-btn {
  position: relative;
  padding-right: 20px;
  color: #111111;
}
.rs-services.style2 .service-item .content-part .service-btn i {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
}
.rs-services.style2 .service-item .content-part .service-btn:hover {
  color: #21a7d0;
}
.rs-services.style2 .service-item .content-part .service-btn:hover i {
  right: -5px;
}
.rs-services.style6 .services-wrap {
  text-align: center;
  padding: 50px 30px 50px;
  border-radius: 10px;
}
.rs-services.style6 .services-wrap .services-item {
  transition: all .8s ease;
  position: relative;
}
.rs-services.style6 .services-wrap .services-item .services-desc .title {
  font-size: 22px;
  line-height: 26px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.rs-services.style6 .services-wrap .services-item .services-desc .title a {
  color: #111111;
}
.rs-services.style6 .services-wrap .services-item .services-desc .title a:hover {
  color: #21a7d0;
  transition: all 0.3s ease;
}
.rs-services.style6 .services-wrap .services-item .services-desc p {
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: #505050;
  margin: 0;
}
.rs-services.style6 .services-wrap.bg1 {
  background: #fdfcdb;
}
.rs-services.style6 .services-wrap.bg2 {
  background: #fbe7e4;
}
.rs-services.style6 .services-wrap.bg3 {
  background: #e7f6fa;
}
.rs-services.style6 .services-wrap.bg4 {
  background: #f3e9fe;
}
.rs-services.style6 .services-wrap:hover .services-item {
  transform: translateY(-10px);
}
.rs-services.style6 .services-wrap:hover .services-item .services-desc .title a {
  color: #21a7d0;
}
.rs-services.style7 .services-item {
  position: relative;
}
.rs-services.style7 .services-item .img-part img {
  width: 100%;
}
.rs-services.style7 .services-item .content-part {
  position: absolute;
  top: 0;
  left: 0;
  padding: 50px 30px;
  border-radius: 4px;
  transition: all 0.3s ease;
}
.rs-services.style7 .services-item .content-part .title {
  font-size: 22px;
  line-height: 32px;
  margin-bottom: 20px;
}
.rs-services.style7 .services-item .content-part .title a {
  color: #ffffff;
}
.rs-services.style7 .services-item .content-part .title a:hover {
  color: #21a7d0;
  transition: all 0.3s ease;
}
.rs-services.style7 .services-item .content-part .desc {
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: #ffffff;
  margin: 0;
}
.rs-services.style7 .services-item:hover .content-part {
  top: -10px;
}
.rs-services.home12-style {
  background: url(assets/images/bg/home12/services-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  padding: 150px 0;
}
.rs-services.home12-style .services-item {
  padding: 45px 15px 40px 15px;
  text-align: center;
  background: #ffffff;
  box-shadow: 0 0 30px #eee;
}
.rs-services.home12-style .services-item .services-image .services-icons {
  padding-bottom: 20px;
}
.rs-services.home12-style .services-item .services-image .services-icons img {
  width: 100px;
}
.rs-services.home12-style .services-item .services-image .services-text .services-title .title {
  font-size: 24px;
  line-height: 36px;
  font-weight: 700;
  color: #171f32;
  padding-bottom: 15px;
  margin: 0;
}
.rs-services.home12-style .services-item .services-image .services-text .text {
  font-size: 16px;
  line-height: 27px;
  font-weight: 400;
  color: #363636;
  margin: 0;
  padding-bottom: 10px;
}
.rs-services.home12-style .services-item:hover .services-image .services-text .services-title .title {
  color: #0c8b51;
}
/* ------------------------------------
    09. Subject Section CSS
---------------------------------------*/
.rs-subject.style1 .subject-wrap {
  text-align: center;
  background: #d3fcf8;
  border-radius: 10px;
  padding: 50px 30px 45px;
}
.rs-subject.style1 .subject-wrap img {
  transition: all 0.3s ease;
  transform: translateY(0);
}
.rs-subject.style1 .subject-wrap .title {
  margin: 20px 0 5px 0;
}
.rs-subject.style1 .subject-wrap .title a {
  color: #111111;
}
.rs-subject.style1 .subject-wrap .title a:hover {
  opacity: .8;
}
.rs-subject.style1 .subject-wrap:hover img {
  transition: all 0.3s ease;
  transform: translateY(-7px);
}
.rs-subject.style1 .subject-wrap.bgc1 {
  background: #d3fcf8;
}
.rs-subject.style1 .subject-wrap.bgc2 {
  background: #16aaca;
}
.rs-subject.style1 .subject-wrap.bgc3 {
  background: #f9e8e8;
}
.rs-subject.style1 .subject-wrap.bgc4 {
  background: #f8f6d1;
}
.rs-subject.style1 .subject-wrap.bgc5 {
  background: #eaddf8;
}
.rs-subject.style1 .subject-wrap.bgc6 {
  background: #e9fbd5;
}
.rs-subject.style1 .subject-wrap.text-light {
  color: #ffffff;
}
.rs-subject.style1 .subject-wrap.text-light .title a {
  color: #ffffff;
}
/* ------------------------------------
    10. Categories Section CSS
---------------------------------------*/
.rs-categories.style1 .categories-item {
  border: 1px solid #dfe9eb;
  background: #ffffff;
  overflow: hidden;
  padding: 30px;
  display: block;
  color: #505050;
  border-radius: 5px;
  display: flex;
  align-items: center;
}
.rs-categories.style1 .categories-item .icon-part {
  float: left;
  margin-right: 25px;
  width: 70px;
  height: 70px;
  line-height: 70px;
  border-radius: 100%;
  background: rgba(22, 170, 202, 0.2);
  text-align: center;
  transition: all 0.3s ease;
}
.rs-categories.style1 .categories-item .icon-part img {
  -webkit-transition: all 0.4s ease;
  transform: scale(1);
}
.rs-categories.style1 .categories-item .content-part .title {
  color: #111111;
  margin-bottom: 5px;
  font-size: 22px;
  transition: all 0.3s ease;
}
.rs-categories.style1 .categories-item:hover {
  background: #21a7d0;
  color: #ffffff;
  border-color: #21a7d0;
}
.rs-categories.style1 .categories-item:hover .icon-part {
  background: #ffffff;
}
.rs-categories.style1 .categories-item:hover .icon-part img {
  transform: scale(0.9);
}
.rs-categories.style1 .categories-item:hover .content-part .title {
  color: #ffffff;
}
.rs-categories.main-home .categories-items {
  position: relative;
  transition: all 0.3s ease;
}
.rs-categories.main-home .categories-items .cate-images a img {
  box-shadow: 0 0 30px #eee;
  background: #fff;
  border-radius: 0 0 5px 5px;
  position: relative;
  transition: all 500ms ease;
  border-radius: 5px;
}
.rs-categories.main-home .categories-items .contents {
  position: absolute;
  left: 0px;
  z-index: 3;
  width: 100%;
  text-align: left;
  transition: all 500ms ease;
  padding: 30px 40px;
  bottom: 0;
  display: flex;
  align-items: center;
}
.rs-categories.main-home .categories-items .contents .img-part img {
  width: 42px;
  margin: 0 20px 8px 0;
}
.rs-categories.main-home .categories-items .contents .content-wrap .title {
  font-size: 22px;
  line-height: 32px;
  font-weight: 700;
  margin-bottom: 5px;
}
.rs-categories.main-home .categories-items .contents .content-wrap .title a {
  color: #ffffff;
}
.rs-categories.main-home .categories-items .contents .content-wrap .title a:hover {
  color: #ff5421;
}
.rs-categories.main-home .categories-items .contents .content-wrap .course-qnty {
  font-size: 16px;
  line-height: 1.4;
  font-weight: 400;
  color: #ffffff;
  transition: all 500ms ease;
  margin: 0px;
}
.rs-categories.main-home .categories-items:before {
  content: "";
  background: -moz-linear-gradient(bottom, #000000 0%, rgba(0, 0, 0, 0) 50%);
  background: linear-gradient(bottom, #000000 0%, rgba(0, 0, 0, 0) 50%);
  background: -webkit-linear-gradient(bottom, #000000 0%, rgba(0, 0, 0, 0) 50%);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  z-index: 0;
  border-radius: 5px;
  transition: all 500ms ease;
  z-index: 1;
}
.rs-categories.main-home .categories-items:hover {
  transform: translateY(-10px);
}
.rs-categories.home9-style .categories-items {
  position: relative;
  transition: all 0.3s ease;
}
.rs-categories.home9-style .categories-items .images-part a img {
  border-radius: 4px 4px 0px 0px;
}
.rs-categories.home9-style .categories-items .image-content {
  border-radius: 0px 0px 4px 4px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.18);
  padding: 70px 30px 25px 40px;
  background-color: #ffffff;
  position: relative;
}
.rs-categories.home9-style .categories-items .image-content .effect-icon {
  background: #1c335e;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  top: -44px;
  text-align: center;
  right: 0;
  margin: 0 auto;
  box-shadow: 0 0 50px #eee;
}
.rs-categories.home9-style .categories-items .image-content .effect-icon i {
  color: #f4bf00;
  font-size: 35px;
}
.rs-categories.home9-style .categories-items .image-content .title {
  font-size: 25px;
  line-height: 32px;
  font-weight: 700;
  color: #101010;
  margin-bottom: 17px;
}
.rs-categories.home9-style .categories-items .image-content .title a {
  color: #101010;
}
.rs-categories.home9-style .categories-items .image-content .title a:hover {
  color: #f4bf00;
}
.rs-categories.home9-style .categories-items .image-content .description p {
  font-weight: 400;
  color: #505050;
  font-size: 16px;
  padding-right: 50px;
}
.rs-categories.home9-style .categories-items .image-content .button-bottom .button-effect {
  position: absolute;
  left: 50%;
  bottom: -14px;
  transform: translateX(-50%);
  transition: all .8s ease;
  width: 100%;
  text-align: center;
  visibility: hidden;
  opacity: 0;
}
.rs-categories.home9-style .categories-items .image-content .button-bottom .button-effect a {
  text-transform: uppercase;
  color: #1c335f;
  padding: 13px 35px 13px 35px;
  background-color: #f4bf00;
  border-radius: 30px 30px 30px 30px;
}
.rs-categories.home9-style .categories-items .image-content .button-bottom .button-effect a:hover {
  color: #ffffff;
  background: #1c335f;
}
.rs-categories.home9-style .categories-items:hover {
  transform: translateY(-10px);
}
.rs-categories.home9-style .categories-items:hover .image-content .title a {
  color: #f4bf00;
}
.rs-categories.home9-style .categories-items:hover .image-content .button-bottom .button-effect {
  opacity: 1;
  visibility: visible;
}
.rs-categories.home11-style {
  background-position: center;
}
.rs-categories.home11-style .img-part {
  background: url(assets/images/categories/home11/1.jpg);
  background-size: cover;
  background-repeat: no-repeat;
}
.rs-categories.home11-style .main-part {
  background: #e7f8fb;
  padding: 100px 300px 100px 70px;
}
.rs-categories.home11-style .main-part .categories-item {
  display: flex;
}
.rs-categories.home11-style .main-part .categories-item .icon-part {
  margin-right: 15px;
}
.rs-categories.home11-style .main-part .categories-item .content-part .title {
  font-size: 22px;
  line-height: 32px;
  font-weight: 800;
  margin-bottom: 10px;
}
.rs-categories.home11-style .main-part .categories-item .content-part .title a {
  color: #4e49a1;
}
.rs-categories.home11-style .main-part .categories-item .content-part .title a:hover {
  color: #21a7d0;
}
.rs-categories.home11-style .main-part .categories-item .content-part p {
  font-size: 16px;
  line-height: 30px;
  font-weight: 400;
  color: #54647b;
  margin: 0;
}
.rs-categories.home-style14 .categories-items {
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
}
.rs-categories.home-style14 .categories-items .cate-images {
  position: relative;
  transition: all 500ms ease;
}
.rs-categories.home-style14 .categories-items .cate-images:before {
  content: "";
  left: 0;
  display: block;
  background: rgba(23, 31, 50, 0.5);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  z-index: 1;
  border-radius: 5px;
}
.rs-categories.home-style14 .categories-items .cate-images a img {
  box-shadow: 0 0 30px #eee;
  background: #fff;
  border-radius: 0 0 5px 5px;
  position: relative;
  transition: all 500ms ease;
  border-radius: 5px;
}
.rs-categories.home-style14 .categories-items .contents {
  position: absolute;
  left: 50%;
  bottom: -185px;
  z-index: 3;
  width: 100%;
  text-align: center;
  transform: translate(-50%, -50%);
  transition: all 500ms ease;
  padding: 26px 20px 20px 20px;
}
.rs-categories.home-style14 .categories-items .contents .img-part img {
  width: 42px;
  margin: 0 0px 15px 0;
}
.rs-categories.home-style14 .categories-items .contents .content-wrap .title {
  font-size: 22px;
  line-height: 32px;
  font-weight: 700;
  margin-bottom: 5px;
}
.rs-categories.home-style14 .categories-items .contents .content-wrap .title a {
  color: #ffffff;
}
.rs-categories.home-style14 .categories-items .contents .content-wrap .title a:hover {
  color: #ff5421;
}
.rs-categories.home-style14 .categories-items .contents .content-wrap .course-qnty {
  color: #fff;
  opacity: 0;
  visibility: hidden;
  transition: all 500ms ease;
  margin: 7px 0;
  display: inline-block;
}
.rs-categories.home-style14 .categories-items .contents .content-wrap .btn2 a {
  opacity: 0;
  visibility: hidden;
  transition: all 500ms ease;
  margin-top: 12px;
  background: #ff5421;
  color: #fff;
  display: inline-block;
  padding: 6px 22px;
  border-radius: 5px;
}
.rs-categories.home-style14 .categories-items:hover {
  transform: translateY(-10px);
}
.rs-categories.home-style14 .categories-items:hover .contents {
  bottom: -80px;
}
.rs-categories.home-style14 .categories-items:hover .contents .content-wrap .course-qnty {
  visibility: visible;
  opacity: 1;
}
.rs-categories.home-style14 .categories-items:hover .contents .content-wrap .btn2 a {
  visibility: visible;
  opacity: 1;
}
/* ------------------------------------
    11. Popular Courses Section CSS
---------------------------------------*/
.rs-popular-courses.style1 .courses-item {
  padding: 30px;
  border: 1px solid #dfe9eb;
  border-radius: 5px;
  transition: all 0.3s ease;
}
.rs-popular-courses.style1 .courses-item .img-part {
  margin-bottom: 30px;
}
.rs-popular-courses.style1 .courses-item .img-part img {
  width: 100%;
  border-radius: 5px;
  transition: all 0.3s ease;
}
.rs-popular-courses.style1 .courses-item .content-part .meta-part {
  margin-bottom: 17px;
}
.rs-popular-courses.style1 .courses-item .content-part .meta-part li {
  display: inline;
  margin-right: 10px;
}
.rs-popular-courses.style1 .courses-item .content-part .meta-part li span.price {
  display: inline-block;
  padding: 3px 20px;
  color: #ffffff;
  font-weight: 500;
  background: #21a7d0;
  border-radius: 3px;
  transition: all 0.3s ease;
}
.rs-popular-courses.style1 .courses-item .content-part .meta-part li a.categorie {
  color: #505050;
}
.rs-popular-courses.style1 .courses-item .content-part .meta-part li:last-child {
  margin: 0;
}
.rs-popular-courses.style1 .courses-item .content-part .title {
  font-size: 24px;
}
.rs-popular-courses.style1 .courses-item .content-part .title a {
  color: #111111;
}
.rs-popular-courses.style1 .courses-item .content-part .bottom-part {
  overflow: hidden;
}
.rs-popular-courses.style1 .courses-item .content-part .bottom-part .info-meta {
  float: left;
  padding-top: 10px;
}
.rs-popular-courses.style1 .courses-item .content-part .bottom-part .info-meta li {
  display: inline;
  margin-right: 15px;
  transition: all 0.3s ease;
}
.rs-popular-courses.style1 .courses-item .content-part .bottom-part .info-meta li.user i {
  color: #505050;
  margin-right: 8px;
  transition: all 0.3s ease;
}
.rs-popular-courses.style1 .courses-item .content-part .bottom-part .info-meta li.ratings i {
  color: #fcb903;
}
.rs-popular-courses.style1 .courses-item .content-part .bottom-part .info-meta li:last-child {
  margin: 0;
}
.rs-popular-courses.style1 .courses-item .content-part .bottom-part .btn-part {
  text-align: right;
}
.rs-popular-courses.style1 .courses-item .content-part .bottom-part .btn-part a {
  padding: 5px 13px;
  background: #f3f8f9;
  border: 1px solid #21a7d0;
  display: inline-block;
  border-radius: 3px;
  color: #111111;
}
.rs-popular-courses.style1 .courses-item .content-part .bottom-part .btn-part a i:before {
  font-size: 18px;
  font-weight: 700;
}
.rs-popular-courses.style1.modify1 .courses-item {
  overflow: hidden;
  transition: all 0.3s ease;
}
.rs-popular-courses.style1.modify1 .courses-item .img-part {
  overflow: hidden;
  margin-bottom: 35px;
  border-radius: 4px;
}
.rs-popular-courses.style1.modify1 .courses-item .img-part img {
  transition: all 500ms ease;
  transform: scale(1);
}
.rs-popular-courses.style1.modify1 .courses-item .content-part .title {
  font-size: 22px;
  line-height: 32px;
  font-weight: 700;
  margin-bottom: 12px;
}
.rs-popular-courses.style1.modify1 .courses-item .content-part .title a {
  color: #111111;
}
.rs-popular-courses.style1.modify1 .courses-item .content-part .title a:hover {
  color: #ff5421;
}
.rs-popular-courses.style1.modify1 .courses-item:hover .img-part img {
  transform: scale(1.1);
}
.rs-popular-courses.style2 .course-wrap {
  background: #c7e8f0;
  position: relative;
  border-radius: 10px;
  text-align: center;
  transition: all 0.3s ease;
  overflow: hidden;
  padding: 30px;
}
.rs-popular-courses.style2 .course-wrap .front-part {
  z-index: 1;
  position: relative;
  transition: all 0.3s ease;
  opacity: 1;
  visibility: visible;
}
.rs-popular-courses.style2 .course-wrap .front-part .img-part {
  margin-bottom: 21px;
}
.rs-popular-courses.style2 .course-wrap .front-part .content-part a.categorie {
  color: #505050;
}
.rs-popular-courses.style2 .course-wrap .front-part .content-part .title {
  margin: 7px 0 0 0;
}
.rs-popular-courses.style2 .course-wrap .front-part .content-part .title a {
  color: #111111;
}
.rs-popular-courses.style2 .course-wrap .inner-part {
  z-index: 1;
  position: absolute;
  top: 40%;
  left: 0;
  width: 100%;
  padding: 0 30px;
  transform: translateY(-50%);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
}
.rs-popular-courses.style2 .course-wrap .inner-part .content-part a.categorie {
  color: #ffffff;
}
.rs-popular-courses.style2 .course-wrap .inner-part .content-part a.categorie:hover {
  color: #111111;
}
.rs-popular-courses.style2 .course-wrap .inner-part .content-part .title {
  margin: 10px 0 25px;
}
.rs-popular-courses.style2 .course-wrap .inner-part .content-part .title a {
  color: #ffffff;
}
.rs-popular-courses.style2 .course-wrap .inner-part .content-part .title a:hover {
  color: #111111;
}
.rs-popular-courses.style2 .course-wrap .inner-part .content-part .course-meta li {
  color: #ffffff;
  display: inline;
  margin-right: 15px;
}
.rs-popular-courses.style2 .course-wrap .inner-part .content-part .course-meta li.course-user i {
  margin-right: 6px;
}
.rs-popular-courses.style2 .course-wrap .inner-part .content-part .course-meta li.ratings i {
  margin-right: 3px;
  color: #fcb903;
}
.rs-popular-courses.style2 .course-wrap .inner-part .content-part .course-meta li:last-child {
  margin: 0;
}
.rs-popular-courses.style2 .course-wrap .price-btn {
  position: absolute;
  bottom: 0;
  padding-bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
}
.rs-popular-courses.style2 .course-wrap .price-btn a {
  font-weight: 500;
  background: #ffffff;
  border-radius: 3px;
  color: #111111;
  display: inline-block;
  padding: 5px 5px 5px 10px;
}
.rs-popular-courses.style2 .course-wrap .price-btn a i {
  padding-left: 5px;
}
.rs-popular-courses.style2 .course-wrap .price-btn a i:before {
  font-size: 15px;
  color: #ffffff;
  width: 41px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: #21a7d0;
  display: inline-block;
  border-radius: 3px;
  transition: all 0.3s ease;
}
.rs-popular-courses.style2 .course-wrap .price-btn a:hover i:before {
  background: #111111;
}
.rs-popular-courses.style2 .course-wrap:after,
.rs-popular-courses.style2 .course-wrap:before {
  position: absolute;
  content: '';
  border-radius: 50%;
  transition: all 0.3s ease;
  z-index: 0;
  background: #b5e2ec;
}
.rs-popular-courses.style2 .course-wrap:after {
  width: 146px;
  height: 146px;
  left: -73px;
  bottom: 70px;
}
.rs-popular-courses.style2 .course-wrap:before {
  width: 412px;
  height: 412px;
  right: -133px;
  top: -80px;
}
.rs-popular-courses.style2 .course-wrap:hover {
  background: #16aaca;
}
.rs-popular-courses.style2 .course-wrap:hover .front-part {
  opacity: 0;
  visibility: hidden;
}
.rs-popular-courses.style2 .course-wrap:hover .inner-part,
.rs-popular-courses.style2 .course-wrap:hover .price-btn {
  opacity: 1;
  visibility: visible;
}
.rs-popular-courses.style2 .course-wrap:hover:after,
.rs-popular-courses.style2 .course-wrap:hover:before {
  background: #1ca6c7;
}
.rs-popular-courses.style3 .courses-item {
  /*border: 1px solid #dfe9eb;*/
  transition: all 0.3s ease;
}
.rs-popular-courses.style3 .courses-item .img-part img {
  width: 100%;
  transition: all 0.3s ease;
}
.rs-popular-courses.style3 .courses-item .content-part {
  position: relative;
  padding: 40px 25px;
  background-color: black;
  color: #fff;
}
.rs-popular-courses.style3 .courses-item .content-part a.categories {
  display: inline-block;
  padding: 0 15px;
  height: 40px;
  line-height: 40px;
  color: #ffffff;
  font-weight: 500;
  background: #21a7d0;
  border-radius: 3px;
  transition: all 0.3s ease;
  position: absolute;
  top: -20px;
}
.rs-popular-courses.style3 .courses-item .content-part .meta-part {
  margin-bottom: 17px;
}
.rs-popular-courses.style3 .courses-item .content-part .meta-part li {
  display: inline;
  margin-right: 10px;
  font-weight: 700;
}
.rs-popular-courses.style3 .courses-item .content-part .meta-part li:last-child {
  margin: 0;
}
.rs-popular-courses.style3 .courses-item .content-part .title a {
  color: #111111;
  line-height: 18pt;
}
.rs-popular-courses.style3 .courses-item .content-part .bottom-part {
  overflow: hidden;
}
.rs-popular-courses.style3 .courses-item .content-part .bottom-part .info-meta {
  float: left;
}
.rs-popular-courses.style3 .courses-item .content-part .bottom-part .info-meta li {
  display: inline;
  margin-right: 15px;
  transition: all 0.3s ease;
}
.rs-popular-courses.style3 .courses-item .content-part .bottom-part .info-meta li.ratings i {
  color: #fcb903;
}
.rs-popular-courses.style3 .courses-item .content-part .bottom-part .info-meta li:last-child {
  margin: 0;
}
.rs-popular-courses.style3 .courses-item .content-part .bottom-part .btn-part {
  text-align: right;
}
.rs-popular-courses.style3 .courses-item .content-part .bottom-part .btn-part a {
  color: #505050;
}
.rs-popular-courses.style3 .courses-item .content-part .bottom-part .btn-part a i:before {
  font-size: 18px;
  font-weight: 700;
  padding-left: 10px;
}
.rs-popular-courses.style4 .sec-title .title {
  font-weight: 800;
  line-height: 16pt;
}
.rs-popular-courses.style4 .sec-title .sub-title {
  text-transform: capitalize;
  font-size: 20px;
  font-weight: 500;
  color: #485973;
}
.rs-popular-courses.style4 .courses-item {
  border: 1px solid #dfe9eb;
  transition: all 0.3s ease;
}
.rs-popular-courses.style4 .courses-item .img-part img {
  width: 100%;
  transition: all 0.3s ease;
}
.rs-popular-courses.style4 .courses-item .content-part {
  position: relative;
  padding: 40px 25px;
}
.rs-popular-courses.style4 .courses-item .content-part .price {
  display: inline-block;
  padding: 0 15px;
  height: 40px;
  line-height: 40px;
  color: #ffffff;
  font-weight: 500;
  background: #21a7d0;
  border-radius: 3px;
  transition: all 0.3s ease;
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
}
.rs-popular-courses.style4 .courses-item .content-part a.categories {
  color: #485973;
  display: inline-block;
  margin-bottom: 10px;
}
.rs-popular-courses.style4 .courses-item .content-part .meta-part {
  margin-bottom: 17px;
}
.rs-popular-courses.style4 .courses-item .content-part .meta-part li {
  display: inline;
  margin-right: 10px;
  font-weight: 700;
}
.rs-popular-courses.style4 .courses-item .content-part .meta-part li:last-child {
  margin: 0;
}
.rs-popular-courses.style4 .courses-item .content-part .title {
  font-size: 24px;
}
.rs-popular-courses.style4 .courses-item .content-part .title a {
  color: #111111;
}
.rs-popular-courses.style4 .courses-item .content-part .bottom-part {
  overflow: hidden;
}
.rs-popular-courses.style4 .courses-item .content-part .bottom-part .user {
  float: left;
  margin-right: 20px;
}
.rs-popular-courses.style4 .courses-item .content-part .bottom-part .info-meta {
  float: left;
}
.rs-popular-courses.style4 .courses-item .content-part .bottom-part .info-meta li {
  display: inline;
  margin-right: 15px;
  transition: all 0.3s ease;
}
.rs-popular-courses.style4 .courses-item .content-part .bottom-part .info-meta li.ratings i {
  color: #fcb903;
}
.rs-popular-courses.style4 .courses-item .content-part .bottom-part .info-meta li:last-child {
  margin: 0;
}
.rs-popular-courses.style4 .courses-item .content-part .bottom-part .btn-part {
  text-align: right;
}
.rs-popular-courses.style4 .courses-item .content-part .bottom-part .btn-part a {
  color: #505050;
}
.rs-popular-courses.style4 .courses-item .content-part .bottom-part .btn-part a i:before {
  font-size: 18px;
  font-weight: 700;
  padding-left: 10px;
}
.rs-popular-courses.style5 .courses-item .courses-grid .img-part {
  overflow: hidden;
}
.rs-popular-courses.style5 .courses-item .courses-grid .img-part a img {
  border-radius: 5px;
  transition: all 500ms ease;
  transform: scale(1);
}
.rs-popular-courses.style5 .courses-item .courses-grid .content-part {
  padding: 50px 30px 50px;
  position: relative;
  text-align: center;
  background: #F9F8F8;
  border-color: #F9F8F8;
}
.rs-popular-courses.style5 .courses-item .courses-grid .content-part .course-price .price {
  padding: 9px 20px;
  color: #ffffff;
  font-weight: 500;
  background: #ff5421;
  border-radius: 5px;
  transition: all 0.3s ease;
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
}
.rs-popular-courses.style5 .courses-item .courses-grid .content-part .title {
  font-size: 22px;
  line-height: 32px;
  margin-bottom: 18px;
}
.rs-popular-courses.style5 .courses-item .courses-grid .content-part .title a {
  color: #031a3d;
}
.rs-popular-courses.style5 .courses-item .courses-grid .content-part .title a:hover {
  color: #21a7d0;
}
.rs-popular-courses.style5 .courses-item .courses-grid .content-part .meta-part {
  text-align: center;
}
.rs-popular-courses.style5 .courses-item .courses-grid .content-part .meta-part .user {
  display: inline-block;
  margin-right: 8px;
  color: #363636;
}
.rs-popular-courses.style5 .courses-item .courses-grid .content-part .meta-part .user i:before {
  color: #ff5421;
  padding-right: 6px;
}
.rs-popular-courses.style5 .courses-item .courses-grid .content-part .meta-part .user i:before:last-child {
  padding-left: 0;
}
.rs-popular-courses.style5 .courses-item .courses-grid .content-part .meta-part .user:last-child {
  margin-right: 0;
}
.rs-popular-courses.style5 .courses-item:hover .courses-grid .img-part a img {
  transform: scale(1.1);
}
.rs-popular-courses.course-view-style .course-part {
  margin: 0 -15px;
}
.rs-popular-courses.course-view-style .course-part .courses-item {
  float: left;
  width: 46%;
  margin: 0 15px;
  margin-bottom: 30px;
}
.rs-popular-courses.course-view-style .course-part .courses-item.right {
  float: right;
}
.rs-popular-courses.course-view-style .course-search-part {
  background-color: #fff;
  border: 1px solid #e0e0e08c;
  margin-bottom: 30px;
  padding: 15px 30px;
  display: inline-flex;
  width: 100%;
}
.rs-popular-courses.course-view-style .course-search-part .course-view-part {
  display: flex;
  align-items: center;
  float: left;
  width: 90%;
}
.rs-popular-courses.course-view-style .course-search-part .course-view-part .view-icons {
  float: left;
  margin-right: 20px;
  line-height: 1;
}
.rs-popular-courses.course-view-style .course-search-part .course-view-part .view-icons a {
  color: #505050;
  font-size: 20px;
  opacity: 0.7;
}
.rs-popular-courses.course-view-style .course-search-part .course-view-part .view-icons a.view-grid {
  color: #ff5421;
  opacity: 1;
}
.rs-popular-courses.course-view-style .course-search-part .course-view-part .view-icons a:hover {
  color: #ff5421;
  opacity: 1;
}
.rs-popular-courses.course-view-style .course-search-part .type-form {
  position: relative;
  float: right;
}
.rs-popular-courses.course-view-style .course-search-part .type-form select {
  display: block;
  width: 100%;
  min-width: 125px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  padding: 0 40px 0 20px;
  background: #ff5421;
  border: none;
  border-radius: 4px;
  -webkit-appearance: none;
  -moz-appearance: none;
  -webkit-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  transition: all 300ms ease;
  cursor: pointer;
}
.rs-popular-courses.course-view-style .course-search-part .type-form .custom-select-box {
  position: relative;
}
.rs-popular-courses.course-view-style .course-search-part .type-form .custom-select-box:before {
  font-family: 'FontAwesome';
  content: "\f107";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  display: block;
  color: #ffffff;
  font-size: 16px;
  line-height: 1;
}
.rs-popular-courses.course-view-style.list-view .course-part {
  margin: 0;
}
.rs-popular-courses.course-view-style.list-view .course-part .courses-item {
  width: 100%;
  margin: 0 0 30px;
  display: flex;
  align-items: center;
}
.rs-popular-courses.course-view-style.list-view .course-part .courses-item .img-part {
  margin: 0;
  margin-right: 30px;
}
.rs-popular-courses.course-view-style.list-view .course-search-part .course-view-part .view-icons a.view-grid {
  color: #505050;
  opacity: 0.7;
}
.rs-popular-courses.course-view-style.list-view .course-search-part .course-view-part .view-icons a.view-list {
  color: #ff5421;
  opacity: 1;
}
.rs-popular-courses.course-view-style .widget-archives .categories {
  margin-left: 25px;
}
.rs-popular-courses.course-view-style .filter-widget {
  position: relative;
}
.rs-popular-courses.course-view-style .filter-widget h5 {
  position: relative;
  padding: 12px 25px;
  padding-left: 0;
  font-size: 16px;
  font-weight: 600;
  color: #03382e;
  display: block;
  border-bottom: 1px solid #f0f5fb;
}
.rs-popular-courses.course-view-style .filter-form {
  position: relative;
}
.rs-popular-courses.course-view-style .filter-form span {
  position: relative;
  color: #626262;
  font-size: 16px;
  display: block;
  font-weight: 600;
  margin-bottom: 15px;
}
.rs-popular-courses.course-view-style .filter-form .radio-box {
  position: relative;
  margin-left: 25px;
  margin-bottom: 12px;
}
.rs-popular-courses.course-view-style .filter-form .radio-box label {
  position: relative;
  display: block;
  width: 100%;
  line-height: 1;
  padding-left: 25px;
  cursor: pointer;
  margin-bottom: 0;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}
.rs-popular-courses.course-view-style .filter-form .radio-box label:before {
  position: absolute;
  left: 0;
  top: 3px;
  height: 10px;
  width: 10px;
  background: #b6b6b6;
  content: "";
  border-radius: 50px;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
  border: 1px solid transparent;
}
.rs-popular-courses.course-view-style .filter-form .radio-box label:after {
  position: absolute;
  left: 0px;
  top: 0;
  height: 15px;
  line-height: 15px;
  max-width: 0;
  font-size: 14px;
  color: #ffffff;
  font-weight: 800;
  overflow: hidden;
  background: transparent;
  text-align: center;
  font-family: 'FontAwesome';
  -webkit-transition: max-width 500ms ease;
  -moz-transition: max-width 500ms ease;
  -ms-transition: max-width 500ms ease;
  -o-transition: max-width 500ms ease;
  transition: max-width 500ms ease;
}
.rs-popular-courses.course-view-style .filter-form .radio-box input[type="radio"]:checked + label {
  border-color: #25a9e0;
}
.rs-popular-courses.course-view-style .filter-form .radio-box input[type="radio"] {
  display: none;
}
.rs-popular-courses.course-view-style .filter-form .radio-box input[type="radio"]:checked + label:before {
  border: 5px solid #ff5421;
  background: #ffffff;
}
.rs-popular-courses.course-view-style .filter-form .radio-box input[type="radio"]:checked + label:after {
  max-width: 20px;
  opacity: 1;
}
.rs-popular-courses.course-view-style .filter-form .radio-box:last-child {
  margin-bottom: 0px;
}
.rs-popular-courses.course-view-style .filter-form .check-box {
  margin-left: 25px;
  margin-bottom: 12px;
}
.rs-popular-courses.course-view-style .filter-form .check-box label {
  padding-left: 10px;
  line-height: 1;
}
.rs-popular-courses.main-home .courses-item .courses-grid {
  padding: 0px;
  box-shadow: 0 0 30px #eee;
  border-radius: 0 0 4px 4px;
  background: #ffffff;
}
.rs-popular-courses.main-home .courses-item .courses-grid .img-part {
  margin-bottom: 0px;
  overflow: hidden;
}
.rs-popular-courses.main-home .courses-item .courses-grid .img-part a img {
  border-radius: 4px 4px 0 0;
  transition: all .8s ease;
  transform: scale(1);
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part {
  padding: 32px 35px 35px 35px;
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part .info-meta {
  padding-bottom: 10px;
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part .info-meta li {
  display: inline-block;
  margin-right: 10px;
  transition: all 0.3s ease;
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part .info-meta li i {
  color: #fcb903;
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part .course-price .price {
  padding: 3px 15px;
  color: #ffffff;
  font-weight: 500;
  background: #ff5421;
  border-radius: 5px;
  transition: all 0.3s ease;
  position: absolute;
  top: 20px;
  right: 38px;
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part .title {
  margin: 0 0 10px;
  line-height: 30px;
  font-size: 22px;
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part .title a {
  color: #101010;
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part .title a:hover {
  color: #ff5421;
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part .meta-part {
  display: flex;
  justify-content: space-between;
  list-style: none;
  margin: 25px 0 0;
  padding: 25px 0 0;
  font-size: 14px;
  border-top: 1px solid #f4f0f0;
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part .meta-part .user {
  display: inline-block;
  margin-right: 8px;
  color: #363636;
}
.rs-popular-courses.main-home .courses-item .courses-grid .content-part .meta-part .user i:before {
  color: #ff5421;
  padding-right: 6px;
}
.rs-popular-courses.main-home .courses-item:hover .courses-grid .img-part a img {
  transform: scale(1.1);
}
.rs-popular-courses.main-home.home12-style .courses-item .courses-grid .content-part {
  padding: 32px 35px 35px 35px;
}
.rs-popular-courses.main-home.home12-style .courses-item .courses-grid .content-part .info-meta {
  padding-bottom: 10px;
}
.rs-popular-courses.main-home.home12-style .courses-item .courses-grid .content-part .info-meta li i {
  color: #fcb903;
}
.rs-popular-courses.main-home.home12-style .courses-item .courses-grid .content-part .course-price .price {
  background: #0c8b51;
}
.rs-popular-courses.main-home.home12-style .courses-item .courses-grid .content-part .title {
  margin: 0 0 10px;
  line-height: 30px;
  font-size: 22px;
}
.rs-popular-courses.main-home.home12-style .courses-item .courses-grid .content-part .title a {
  color: #101010;
}
.rs-popular-courses.main-home.home12-style .courses-item .courses-grid .content-part .title a:hover {
  color: #0c8b51;
}
.rs-popular-courses.main-home.home12-style .courses-item .courses-grid .content-part .meta-part {
  border-top: 1px solid #f4f0f0;
}
.rs-popular-courses.main-home.home12-style .courses-item .courses-grid .content-part .meta-part .user {
  display: inline-block;
  margin-right: 8px;
  color: #363636;
}
.rs-popular-courses.main-home.home12-style .courses-item .courses-grid .content-part .meta-part .user i:before {
  color: #0c8b51;
  padding-right: 6px;
}
.rs-popular-courses.orange-color .courses-item .content-part .meta-part li span.price {
  background: #ff5421;
}
.rs-popular-courses.orange-color .courses-item .content-part .title a:hover {
  color: #ff5421;
}
.rs-popular-courses.orange-color .courses-item .content-part .bottom-part .btn-part a {
  border: 1px solid #f2f2f2;
  background: transparent;
}
.rs-popular-courses.orange-color .courses-item .content-part .bottom-part .btn-part a:hover {
  background: #ff5421;
  color: #ffffff;
}
.rs-popular-courses.orange-color.style2 .course-wrap {
  background: #ff5421;
}
.rs-popular-courses.orange-color.style2 .course-wrap .price-btn a i:before {
  background: #ff5421;
}
.rs-popular-courses.orange-color.style2 .course-wrap .price-btn a:hover i:before {
  background: #ff4007;
}
.rs-popular-courses.orange-color.style2 .course-wrap:after,
.rs-popular-courses.orange-color.style2 .course-wrap:before {
  background: #ff5421;
}
.rs-popular-courses.orange-color.style2 .course-wrap:hover {
  background: #ff5421;
}
.rs-popular-courses.orange-color.style2 .course-wrap:hover:after,
.rs-popular-courses.orange-color.style2 .course-wrap:hover:before {
  background: #ff5421;
}
.rs-popular-courses.orange-color.style3 .courses-item .content-part a.categories {
  background: #ff5421;
  color: #ffffff;
}
.rs-popular-courses.orange-color.style3 .courses-item .content-part .meta-part li span.price {
  background: unset;
  line-height: 10pt;
}
.rs-popular-courses.orange-color.style3 .courses-item .content-part .title a {
  color: #fff;
  font-family: 'DroidArabicKufiRegular';
  font-weight: normal;
  font-style: normal;
  font-size: 15px;

}
.rs-popular-courses.orange-color.style3 .courses-item .content-part .title a:hover {
  color: #ddd;
}
.rs-popular-courses.orange-color.style3 .courses-item .content-part .bottom-part .btn-part a {
  border: unset;
  color: #fff;
}
.rs-popular-courses.orange-color.style3 .courses-item .content-part .bottom-part .btn-part a:hover {
  background: transparent;
  color: #ddd;
}
.rs-popular-courses.orange-color.style4 .courses-item .content-part .price {
  background: #ff5421;
}
.rs-popular-courses.orange-color.style4 .courses-item .content-part .bottom-part .btn-part a {
  border: unset;
}
.rs-popular-courses.orange-color.style4 .courses-item .content-part .bottom-part .btn-part a {
  border: unset;
}
.rs-popular-courses.orange-color.style4 .courses-item .content-part .bottom-part .btn-part a:hover {
  background: transparent;
  color: #ff5421;
}
.rs-popular-courses.home11-style .courses-item {
  border: 1px solid #e5e4f1;
  padding: 30px;
  position: relative;
  transition: all .4s ease;
}
.rs-popular-courses.home11-style .courses-item .content-part {
  padding: 30px 0 0px;
}
.rs-popular-courses.home11-style .courses-item .content-part .course-body .title {
  font-size: 24px;
  line-height: 34px;
  font-weight: 800;
  margin-bottom: 12px;
}
.rs-popular-courses.home11-style .courses-item .content-part .course-body .title a {
  color: #4e49a1;
}
.rs-popular-courses.home11-style .courses-item .content-part .course-body .title a:hover {
  color: #21a7d0;
}
.rs-popular-courses.home11-style .courses-item .content-part .course-body p {
  font-size: 16px;
  line-height: 28px;
  color: #54647b;
  font-weight: 400;
  margin-bottom: 20px;
}
.rs-popular-courses.home11-style .courses-item .content-part .bottom-part {
  border-top: 1px solid #e5e4f1;
  padding: 20px 0 0;
}
.rs-popular-courses.home11-style .courses-item .content-part .bottom-part .course-footer {
  display: inline-block;
  border-right: 1px solid #e5e4f1;
  padding-right: 15px;
  padding-left: 15px;
}
.rs-popular-courses.home11-style .courses-item .content-part .bottom-part .course-footer .label {
  display: block;
  font-size: 16px;
  line-height: 28px;
  color: #54647b;
  font-weight: 500;
}
.rs-popular-courses.home11-style .courses-item .content-part .bottom-part .course-footer .desc {
  font-size: 14px;
  line-height: 23px;
  color: #21a7d0;
  font-weight: 400;
}
.rs-popular-courses.home11-style .courses-item .btn-part {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  clip-path: none;
  background: #4e49a1;
  position: absolute;
  text-align: center;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  visibility: hidden;
  transition: all .5s ease;
}
.rs-popular-courses.home11-style .courses-item .btn-part a {
  font-size: 16px;
  color: #ffffff;
  display: inline-block;
  font-weight: 500;
}
.rs-popular-courses.home11-style .courses-item .btn-part a:hover {
  color: #21a7d0;
}
.rs-popular-courses.home11-style .courses-item:hover .btn-part {
  bottom: -20px;
  opacity: 1;
  visibility: visible;
}
.rs-popular-courses.home11-style .owl-carousel .owl-stage-outer {
  padding-bottom: 20px;
  margin-bottom: -20px;
}
.rs-popular-courses.home13-style .courses-item .courses-grid {
  border-radius: 0 0 4px 4px;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .img-part {
  margin-bottom: 0px;
  overflow: hidden;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .img-part a img {
  border-radius: 4px 4px 0 0;
  transition: all .8s ease;
  transform: scale(1);
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part {
  padding: 25px 25px 25px 25px;
  border-style: solid;
  border-width: 0px 1px 1px 1px;
  border-color: #252525;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .course-price .price {
  padding: 3px 15px;
  color: #ffffff;
  font-weight: 500;
  background: #ff5421;
  border-radius: 5px;
  transition: all 0.3s ease;
  position: absolute;
  top: 20px;
  right: 38px;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .title {
  margin: 0 0 10px;
  line-height: 30px;
  font-size: 22px;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .title a {
  color: #ffffff;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .title a:hover {
  color: #ff5421;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .meta-part {
  display: flex;
  justify-content: space-between;
  list-style: none;
  font-size: 15px;
  margin: 0 0 20px;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .meta-part .user {
  display: inline-block;
  margin-right: 8px;
  color: #ffffff;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .meta-part .user i:before {
  color: #ff5421;
  padding-right: 6px;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .avatar-part {
  display: flex;
  align-items: center;
  margin-top: 20px;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .avatar-part .avatar-img {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .avatar-part .avatar-img img {
  border-radius: 50%;
}
.rs-popular-courses.home13-style .courses-item .courses-grid .content-part .avatar-part .title {
  font-size: 15px;
  color: #ffffff;
  line-height: 27px;
  margin-bottom: 0;
}
.rs-popular-courses.home13-style .courses-item:hover .courses-grid .img-part a img {
  transform: scale(1.1);
}
.free-course-contact {
  margin: 0px;
  padding: 40px 50px 50px;
  background: url(assets/images/bg/course-contact-bg.jpg);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.free-course-contact span {
  font-size: 30px;
  color: #111111;
  display: block;
}
.free-course-contact form input,
.free-course-contact form textarea,
.free-course-contact form select {
  width: 100%;
  border-radius: 3px;
  border: 1px solid #ffffff;
  color: #767676;
  background: #ffffff;
  padding: 10px 18px;
}
.free-course-contact form input,
.free-course-contact form select {
  margin-bottom: 30px;
  height: 45px;
}
.free-course-contact form textarea {
  height: 140px;
  display: inherit;
}
.free-course-contact form .select-option {
  position: relative;
}
.free-course-contact form .select-option select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  z-index: 11;
  position: relative;
}
.free-course-contact form .select-option:after {
  content: '\f107';
  font-family: FontAwesome;
  color: #767676;
  right: 15px;
  position: absolute;
  top: 16px;
  font-size: 20px;
  pointer-events: none;
}
.free-course-contact form ::-webkit-input-placeholder {
  color: #fff;
}
.free-course-contact form :-ms-input-placeholder {
  color: #fff;
}
.free-course-contact form ::placeholder {
  color: #fff;
}
.rs-latest-couses .course-item {
  align-items: flex-start;
  border-radius: 5px 300px 300px 5px;
  display: flex;
  /*border: 1px solid #ceeaf0;*/
  background: #18191A !important;
}
.rs-latest-couses .course-item .course-image a {
  width: 200px;
  height: 200px;
  overflow: hidden;
  border-radius: 100%;
  display: block;
}
.rs-latest-couses .course-item .course-image a img {
  object-fit: cover;
  object-position: left;
  height: 100%;
  width: 100%;
}
.rs-latest-couses .course-item .course-info {
  align-self: center;
  padding: 0px 30px;
}
.rs-latest-couses .course-item .course-info .meta-part {
  padding-bottom: 5px;
}
.rs-latest-couses .course-item .course-info .meta-part li {
  display: inline;
  margin-right: -10px;
  font-weight: 700;
  margin-right: 10px; 
}
.rs-latest-couses .course-item .course-info .course-title {
  font-size: 22px;
  line-height: 36px;
  font-weight: 700;
  margin-bottom: 5px;
}
.rs-latest-couses .course-item .course-info .course-title a {
  color: #111111;
}
.rs-latest-couses .course-item .course-info .course-title a:hover {
  color: #21a7d0;
  transition: all 0.3s ease;
}
.rs-latest-couses .course-item .course-info .bottom-part .info-meta {
  float: right;
}
.rs-latest-couses .course-item .course-info .bottom-part .info-meta ul .ratings {
  display: inline-block;
  margin-right: 10px;
  transition: all 0.3s ease;
}
.rs-latest-couses .course-item .course-info .bottom-part .info-meta ul .ratings i {
  color: #fcb903;
}
.rs-latest-couses .course-item .course-info .bottom-part .btn-part {
  text-align: right;
}
.rs-latest-couses .course-item .course-info .bottom-part .btn-part a {
  color: #505050;
}
.rs-latest-couses .course-item .course-info .bottom-part .btn-part a i:before {
  font-size: 18px;
  font-weight: 700;
  padding-left: 10px;
}
.rs-latest-couses .course-item .course-info .bottom-part .btn-part a:hover {
  color: #21a7d0;
  transition: all 0.3s ease;
}
.rs-latest-couses .course-item:hover {
  background: #FFFFFF;
  border-color: #FFFFFF;
}
.rs-latest-couses.orange-color .course-item {
  border: 1px solid #FF542129;
}
.rs-latest-couses.orange-color .course-item .course-info .course-title a:hover,
.rs-latest-couses.orange-color .course-item .bottom-part .btn-part a:hover {
  color: #ff5421;
}
.rs-latest-couses.orange-color .course-item:hover {
  background: #FFFFFF;
  border-color: #FFFFFF;
}
.rs-featured-courses {
  background: #e7f8fb;
}
.rs-featured-courses .rs-carousel .owl-dots {
  text-align: center;
}
.rs-featured-courses .rs-carousel .owl-dots .owl-dot {
  width: 30px;
  height: 10px;
  display: inline-block;
  margin: 0 6px;
  padding: 3px 0;
  border-radius: 30px;
  background: #111111;
}
.rs-featured-courses .rs-carousel .owl-dots .owl-dot:hover {
  background: #21a7d0;
}
.rs-featured-courses .rs-carousel .owl-dots .active {
  background: #21a7d0;
}
.rs-featured-courses .owl-stage-outer {
  padding-bottom: 10px;
}
.rs-featured-courses .courses-item {
  padding: 0 30px;
  position: relative;
  transition: all 0.3s ease;
}
.rs-featured-courses .courses-item .img-part {
  position: relative;
  z-index: 111;
}
.rs-featured-courses .courses-item .content-part {
  padding: 30px 0;
  position: relative;
  z-index: 111;
}
.rs-featured-courses .courses-item .content-part span .categories {
  display: inline-block;
  padding: 0 15px;
  height: 40px;
  line-height: 40px;
  color: #ffffff;
  font-weight: 500;
  border-radius: 3px;
  transition: all 0.3s ease;
  position: absolute;
  top: -20px;
  left: 32px;
}
.rs-featured-courses .courses-item .content-part span .categories.bg1 {
  background: #c701f1;
}
.rs-featured-courses .courses-item .content-part span .categories.bg2 {
  background: #065ce8;
}
.rs-featured-courses .courses-item .content-part span .categories.bg3 {
  background: #f6075b;
}
.rs-featured-courses .courses-item .content-part .meta-part {
  display: flex;
  align-items: center;
  padding-top: 16px;
  padding-bottom: 12px;
}
.rs-featured-courses .courses-item .content-part .meta-part .user {
  display: inline;
  margin-right: 10px;
  font-weight: 700;
}
.rs-featured-courses .courses-item .content-part .meta-part .ratings {
  padding-left: 25px;
}
.rs-featured-courses .courses-item .content-part .meta-part .ratings i {
  color: #fcb903;
}
.rs-featured-courses .courses-item .content-part .title {
  font-size: 24px;
  line-height: 35px;
  font-weight: 700;
}
.rs-featured-courses .courses-item .content-part .title a {
  color: #031a3d;
}
.rs-featured-courses .courses-item .content-part .title a:hover {
  color: #21a7d0;
}
.rs-featured-courses .courses-item .content-part .images {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e5e8eb;
  padding-bottom: 35px;
}
.rs-featured-courses .courses-item .content-part .images .img-part img {
  border-radius: 50%;
  width: 30px;
  height: 30px;
}
.rs-featured-courses .courses-item .content-part .images span {
  color: #54647b;
  font-size: 15px;
  line-height: 25px;
  font-weight: 400;
  padding-left: 15px;
}
.rs-featured-courses .courses-item .content-part .bottom-part .info-meta {
  float: left;
}
.rs-featured-courses .courses-item .content-part .bottom-part .info-meta li {
  color: #031a3d;
  font-size: 15px;
  line-height: 25px;
  font-weight: 700;
}
.rs-featured-courses .courses-item .content-part .bottom-part .btn-part {
  text-align: right;
}
.rs-featured-courses .courses-item .content-part .bottom-part .btn-part a {
  color: #031a3d;
  font-size: 15px;
  line-height: 25px;
}
.rs-featured-courses .courses-item .content-part .bottom-part .btn-part a i:before {
  font-size: 18px;
  font-weight: 700;
  padding-left: 10px;
}
.rs-featured-courses .courses-item .content-part .bottom-part .btn-part a:hover {
  color: #21a7d0;
}
.rs-featured-courses .courses-item:after {
  position: absolute;
  content: '';
  left: 0;
  bottom: 0;
  width: 100%;
  height: 78%;
  background: #ffff;
  border-radius: 3px;
}
.rs-featured-courses .courses-item:hover {
  transform: translateY(-10px);
}
.rs-features {
  position: relative;
  margin-top: -245px;
  z-index: 111;
}
.rs-features .features-wrap {
  border-radius: 5px 5px 5px 5px;
  background: #171f32;
  padding: 25px 40px 25px;
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;
}
.rs-features .features-wrap .icon-part img {
  width: 50px;
  margin: 0 35px 8px 0;
}
.rs-features .features-wrap .content-part .title {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 5px;
}
.rs-features .features-wrap .content-part .title .watermark {
  color: #ffffff;
}
.rs-features .features-wrap .content-part .dese {
  font-size: 16px;
  font-weight: 400;
  color: #ffffff;
  margin: 0;
}
.rs-features .features-wrap:hover {
  transform: translateY(-10px);
}
.rs-features.style2 {
  margin-top: -110px;
}
.rs-features.style2 .rs-iconbox-area {
  position: relative;
  transition: all 0.3s ease;
  padding: 50px;
  background-color: #1C335F;
  border-radius: 5px 5px 5px 5px;
  text-align: center;
}
.rs-features.style2 .rs-iconbox-area .icon-area {
  margin-bottom: 32px !important;
}
.rs-features.style2 .rs-iconbox-area .icon-area img {
  width: 80px;
  height: 80px;
}
.rs-features.style2 .rs-iconbox-area .text-area .icon-title {
  font-size: 22px;
  line-height: 700;
  line-height: 1;
  color: #ffffff;
  margin-bottom: 0;
}
.rs-features.style2 .rs-iconbox-area:hover {
  transform: translateY(-10px);
}
.rs-features.main-home {
  width: 100%;
  position: absolute;
  bottom: 100px;
  margin: 0;
}
.rs-features.style3 {
  margin: unset;
}
.rs-features.style3 .features-item {
  position: relative;
  transition: all 0.3s ease;
}
.rs-features.style3 .features-item .content-part {
  position: absolute;
  width: 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  padding: 50px 35px 0;
  text-align: center;
}
.rs-features.style3 .features-item .content-part .icon-part {
  padding-bottom: 20px;
}
.rs-features.style3 .features-item .content-part .icon-part img {
  -webkit-transition: all 0.4s ease;
  transform: scale(1);
}
.rs-features.style3 .features-item .content-part .title {
  font-size: 22px;
  line-height: 32px;
  font-weight: 700;
  margin-bottom: 12px;
}
.rs-features.style3 .features-item .content-part .title a {
  color: #ffffff;
}
.rs-features.style3 .features-item .content-part p {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: #ffffff;
  margin-bottom: 4px;
}
.rs-features.style3 .features-item:hover {
  transform: translateY(-10px);
}
.rs-features.style3 .features-item:hover .content-part .icon-part img {
  transform: scale(0.9);
}
.rs-features.style4 {
  position: unset;
  margin-top: unset;
  z-index: unset;
}
.rs-features.style4 .features-wrap {
  background: unset;
  display: unset;
  align-items: unset;
  position: unset;
}
.rs-features.style4 .features-wrap .icon-part {
  margin: 0 0px 17px 0;
}
.rs-features.style4 .features-wrap .content-part .title {
  margin-bottom: 10px;
}
.rs-features.style4 .features-wrap .content-part .title .watermark {
  color: #333333;
}
.rs-features.style4 .features-wrap .content-part .dese {
  color: #505050;
}
.rs-degree.style1 .degree-wrap {
  position: relative;
  overflow: hidden;
  border-radius: 3px;
}
.rs-degree.style1 .degree-wrap img {
  width: 100%;
}
.rs-degree.style1 .degree-wrap .title-part {
  position: absolute;
  left: 0;
  bottom: 0;
  padding: 0 30px 21px;
  opacity: 1;
  transition: all 0.3s ease;
}
.rs-degree.style1 .degree-wrap .title-part .title {
  margin: 0;
  color: #ffffff;
}
.rs-degree.style1 .degree-wrap .content-part {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) rotateY(90deg);
  text-align: center;
  background: rgba(17, 41, 88, 0.9);
  border-radius: 3px;
  padding: 78px 35px 0;
  color: #ffffff;
  opacity: 0;
  transition: all 0.3s ease;
}
.rs-degree.style1 .degree-wrap .content-part .title {
  margin-bottom: 13px;
}
.rs-degree.style1 .degree-wrap .content-part .title a {
  color: #ffffff;
}
.rs-degree.style1 .degree-wrap .content-part .title a:hover {
  color: #cccccc;
}
.rs-degree.style1 .degree-wrap .content-part .desc {
  margin-bottom: 15px;
}
.rs-degree.style1 .degree-wrap .content-part .btn-part a {
  text-transform: uppercase;
  font-weight: 500;
  color: #ffffff;
  padding-bottom: 0;
  border-bottom: 1px solid #ffffff;
}
.rs-degree.style1 .degree-wrap .content-part .btn-part a:hover {
  color: #cccccc;
}
.rs-degree.style1 .degree-wrap:before {
  position: absolute;
  content: '';
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.9;
  background: linear-gradient(transparent, #273c66);
}
.rs-degree.style1 .degree-wrap:hover .content-part {
  transform: translate(-50%, -50%) rotateY(0);
  opacity: 1;
}
.rs-degree.style1 .degree-wrap:hover .title-part {
  bottom: -60px;
  opacity: 0;
}
/* -----------------------------------
    12. Course Single Section CSS
-------------------------------------*/
/*Intro Section*/
.intro-section {
  position: relative;
  /*Video Column Style*/

  /*Intro Tabs Style*/

}
.intro-section .video-column {
  position: relative;
  z-index: 1;
}
.intro-section .video-column .intro-video {
  position: relative;
  text-align: center;
  width: 100%;
  display: block;
}
.intro-section .video-column .intro-video h4 {
  position: absolute;
  width: 100%;
  text-align: center;
  color: #ffffff;
  font-weight: 500;
  font-size: 16px;
  text-transform: capitalize;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, -50%);
}
.intro-section .video-column .intro-video .intro-video-box {
  position: relative;
  width: 60px;
  height: 60px;
  z-index: 99;
  color: #00aa15;
  font-weight: 400;
  font-size: 24px;
  text-align: center;
  border-radius: 50%;
  padding-left: 7px;
  line-height: 60px;
  display: inline-block;
  background-color: #ffffff;
  transition: all 900ms ease;
  -moz-transition: all 900ms ease;
  -webkit-transition: all 900ms ease;
  -ms-transition: all 900ms ease;
  -o-transition: all 900ms ease;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
}
.intro-section .video-column .intro-video:before {
  position: absolute;
  content: '';
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
  background-color: rgba(0, 0, 0, 0.1);
}
.intro-section .video-column .course-features-info {
  margin: 50px 0;
  border-radius: 3px;
  padding: 30px 40px 30px;
  background: black;
  /*box-shadow: 0 0 30px #eee;*/
}
.intro-section .video-column .course-features-info ul li {
  display: block;
  padding: 10px 0;
}
.intro-section .video-column .course-features-info ul li i {
  color: #ff5421;
}
.intro-section .video-column .course-features-info ul li .label {
  padding-left: 10px;
}
.intro-section .video-column .course-features-info ul li .value {
  float: right;
  padding-right: 5px;
}
.intro-section .video-column .btn-part {
  background: black;
  box-shadow: 0 0 35px #eee;
  padding: 50px 50px 35px;
  /*border-radius: 5px;*/
}
.intro-section .video-column .btn-part .btn {
  margin: 0 0 15px;
  width: 100%;
}
.intro-section .intro-tabs {
  position: relative;
  border: none;
}
.intro-section .intro-tabs .tab-btns {
  position: relative;
  text-align: center;
  width: 20%;
}
.intro-section .intro-tabs .tab-btns .tab-btn {
  position: relative;
  display: block;
  width: 100%;
  font-size: 14px;
  background: none;
  background: #ffffff;
  color: #626262;
  border: 1px solid #eee;
  font-weight: 500;
  line-height: 24px;
  cursor: pointer;
  float: left;
  padding: 12px 40px 10px;
  text-transform: capitalize;
  transition: all 500ms ease;
}
.intro-section .intro-tabs .tab-btns .tab-btn:hover,
.intro-section .intro-tabs .tab-btns .tab-btn.active {
  color: #ffffff;
  background: #ff5421;
  border-color: #ff5421;
}
.intro-section .intro-tabs .tabs-content {
  position: relative;
  overflow: hidden;
  background-color: #ffffff;
}
.intro-section .intro-tabs .tabs-content .minutes {
  position: relative;
  color: #21a7d0;
  font-size: 14px;
  font-weight: 500;
  margin-top: 12px;
}
/* Course Overview */
.course-overview {
  position: relative;
}
.course-overview .instructor-title {
  position: relative;
  color: #111111;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.3em;
  margin-bottom: 18px;
}
.course-overview .inner-box {
  position: relative;
  padding: 0px 40px 40px;
}
.course-overview .inner-box h4 {
  position: relative;
  color: #111111;
  font-weight: 600;
  line-height: 1.3em;
  margin-bottom: 15px;
}
.course-overview .inner-box h3 {
  position: relative;
  color: #111111;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.3em;
  margin-bottom: 18px;
}
.course-overview .inner-box p {
  position: relative;
  color: #626262;
  font-size: 16px;
  line-height: 1.8em;
  margin-bottom: 15px;
}
.course-overview .inner-box .student-list {
  position: relative;
  margin-top: 25px;
  margin-bottom: 30px;
}
.course-overview .inner-box .student-list li {
  position: relative;
  color: #393939;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.3em;
  margin-right: 80px;
  display: inline-block;
  list-style: disc;
  margin-bottom: 10px;
}
.course-overview .inner-box .student-list li .fa {
  color: #ff5421;
  margin: 0px 2px;
}
.course-overview .inner-box .student-list li:last-child {
  margin-right: 0px;
}
.course-overview .inner-box .review-list {
  position: relative;
  margin-bottom: 20px;
}
.course-overview .inner-box .review-list li {
  position: relative;
  color: #626262;
  font-size: 16px;
  margin-bottom: 14px;
  padding-left: 30px;
}
.course-overview .inner-box .review-list li:before {
  position: absolute;
  content: "\f101";
  left: 0px;
  top: 5px;
  color: #ff5421;
  font-size: 14px;
  line-height: 1em;
  font-family: "FontAwesome";
}
.course-overview .inner-box .review-list li:last-child {
  margin-bottom: 0px;
}
/*Course Review*/
.cource-review-box {
  position: relative;
  padding: 0px 40px;
}
.cource-review-box h4 {
  position: relative;
  color: #111111;
  font-weight: 700;
  line-height: 1.3em;
  margin-bottom: 15px;
}
.cource-review-box .rating {
  position: relative;
  color: #626262;
  font-size: 16px;
}
.cource-review-box .rating .fa {
  position: relative;
  color: #ff5421;
  margin: 0px 2px;
  font-size: 16px;
}
.cource-review-box .total-rating {
  position: relative;
  color: #ff5421;
  font-size: 16px;
  font-weight: 600;
  margin-right: 5px;
}
.cource-review-box .text {
  position: relative;
  color: #626262;
  font-size: 16px;
  line-height: 1.8em;
  margin-top: 15px;
}
.cource-review-box .helpful {
  position: relative;
  color: #393939;
  font-size: 16px;
  line-height: 1.8em;
  margin-top: 12px;
  font-weight: 500;
  margin-bottom: 15px;
}
.cource-review-box .like-option {
  position: relative;
}
.cource-review-box .like-option li {
  position: relative;
  margin-right: 10px;
  display: inline-block;
}
.cource-review-box .like-option li i {
  position: relative;
  width: 36px;
  height: 36px;
  color: #626262;
  text-align: center;
  line-height: 36px;
  border-radius: 50px;
  display: inline-block;
  background-color: #eaeff5;
}
.cource-review-box .like-option li i:hover {
  color: #21a7d0;
}
.cource-review-box .like-option .report {
  position: relative;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}
.cource-review-box .like-option .report:hover {
  color: #21a7d0;
}
.cource-review-box .more {
  position: relative;
  color: #ff5421;
  font-size: 14px;
  font-weight: 500;
  margin-top: 30px;
  display: inline-block;
}
/* -----------------------------------
    13. Accordion Section CSS
-------------------------------------*/
.accordion-box {
  position: relative;
}
.accordion-box .block {
  position: relative;
  background-color: #ffffff;
  border: none;
  border-bottom: 4px solid #f6f6f6;
  box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
}
.accordion-box .block .acc-btn {
  position: relative;
  width: 100%;
  text-align: left;
  font-size: 20px;
  margin-bottom: 0px;
  cursor: pointer;
  line-height: 24px;
  font-weight: 600;
  color: #111111;
  padding: 0 25px;
  height: 65px;
  line-height: 65px;
  text-decoration: none !important;
  text-transform: capitalize;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}
.accordion-box .block .acc-btn:after {
  position: absolute;
  font-family: FontAwesome;
  content: "\f077";
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  font-weight: 400;
  color: #111111;
  padding: unset;
  transition: all 0.3s ease;
}
.accordion-box .block .acc-btn.collapsed:after {
  content: "\f078";
}
.accordion-box .block .card-header {
  position: relative;
  padding: 0;
  border: none;
  border-radius: unset;
}
.accordion-box .block .content {
  position: relative;
  padding: 0px 40px 25px;
}
.accordion-box .block .play-icon {
  position: relative;
  color: #626262;
  font-size: 18px;
  text-align: center;
  line-height: 40px;
  display: inline-block;
  transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  background-color: #ffffff;
}
.accordion-box .block .play-icon:before {
  content: "";
  position: absolute;
  z-index: 0;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  animation: pulse-border 1500ms ease-out infinite;
  transition: all 0.3s ease;
}
.accordion-box .block .play-icon .fa {
  position: relative;
  width: 40px;
  height: 40px;
  line-height: 40px;
  left: 0px;
  z-index: 99;
  color: #ff5421;
  font-weight: 400;
  font-size: 16px;
  text-align: center;
  border-radius: 50%;
  padding-left: 4px;
  margin-right: 30px;
  display: inline-block;
  margin-top: -20px;
  margin-left: -20px;
  background-color: #ffffff;
  transition: all 900ms ease;
  -moz-transition: all 900ms ease;
  -webkit-transition: all 900ms ease;
  -ms-transition: all 900ms ease;
  -o-transition: all 900ms ease;
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
}
.accordion-box .block .play-icon:hover:before {
  background: #ff5421;
}
/* ------------------------------------
    14. Why Choose US Section CSS
---------------------------------------*/
.why-choose-us .choose-us-part .facilities-part .single-facility .icon-part {
  position: relative;
  float: left;
  margin-right: 5px;
}
.why-choose-us .choose-us-part .facilities-part .single-facility .icon-part .shape-img {
  position: relative;
}
.why-choose-us .choose-us-part .facilities-part .single-facility .icon-part i {
  position: absolute;
  top: 55%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #21a7d0;
}
.why-choose-us .choose-us-part .facilities-part .single-facility .icon-part i:before {
  font-size: 28px;
}
.why-choose-us .choose-us-part .facilities-part .single-facility .text-part .title {
  font-size: 22px;
  color: #111111;
  font-weight: bold;
  margin-bottom: 10px;
}
.why-choose-us.style2 .facilities-two .content-part {
  display: flex;
  align-items: center;
}
.why-choose-us.style2 .facilities-two .content-part .icon-part {
  width: 80px;
  line-height: 80px;
  text-align: center;
  border-radius: 50%;
  margin-right: 20px;
  position: relative;
  transition: all 0.3s ease;
}
.why-choose-us.style2 .facilities-two .content-part .icon-part img {
  -webkit-transition: all 0.4s ease;
  transform: scale(1);
}
.why-choose-us.style2 .facilities-two .content-part .icon-part.purple-bg {
  background: #4e49a1;
}
.why-choose-us.style2 .facilities-two .content-part .icon-part.pink-bg {
  background: #ff6666;
}
.why-choose-us.style2 .facilities-two .content-part .icon-part.orange-bg {
  background: #ffaf40;
}
.why-choose-us.style2 .facilities-two .content-part .icon-part.green-bg {
  background: #95cc47;
}
.why-choose-us.style2 .facilities-two .content-part .icon-part:hover {
  transform: scale(0.9);
}
.why-choose-us.style2 .facilities-two .content-part .text-part .title {
  font-size: 22px;
  line-height: 32px;
  font-weight: 800;
  color: #4e49a1;
  margin-bottom: 0;
}
.why-choose-us.style2 .video-wrap {
  position: relative;
  display: inline-block;
}
.why-choose-us.style2 .video-wrap .popup-videos {
  color: #4e49a1;
  display: block;
  overflow: hidden;
  max-width: 270px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.why-choose-us.style2 .video-wrap .popup-videos i {
  width: 70px;
  height: 70px;
  line-height: 61px;
  border: 5px solid #4e49a1;
  border-radius: 50%;
  text-align: center;
  font-size: 25px;
  float: left;
  margin-right: 20px;
}
.why-choose-us.style2 .video-wrap .popup-videos i:before {
  padding-left: 5px;
}
.why-choose-us.style2 .video-wrap .popup-videos:hover {
  opacity: 0.7;
}
.why-choose-us.style3 {
  background: url(assets/images/bg/home12/why-learn-bg.jpg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: 200px 0;
}
.why-choose-us.style3 .services-part {
  display: flex;
  box-shadow: 0 0 30px #eee;
  background: #ffffff;
  padding: 15px 30px 20px 30px;
}
.why-choose-us.style3 .services-part .services-icon {
  margin-right: 29px;
}
.why-choose-us.style3 .services-part .services-icon img {
  width: 90px;
}
.why-choose-us.style3 .services-part .services-text .title {
  font-size: 20px;
  line-height: 36px;
  font-weight: 700;
  padding-bottom: 5px;
  margin: 0;
  color: #171f32;
}
.why-choose-us.style3 .services-part .services-text .services-txt {
  font-size: 16px;
  line-height: 1.2;
  font-weight: 400;
  margin: 0;
  color: #333333;
}
/* ------------------------------------
    15. Team Section CSS
---------------------------------------*/
.rs-team.style1 .team-item {
  position: relative;
  overflow: hidden;
}
.rs-team.style1 .team-item img {
  width: 100%;
}
.rs-team.style1 .team-item .content-part {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 50%);
  text-align: center;
  z-index: 1;
  transition: all 0.3s ease;
  opacity: 0;
}
.rs-team.style1 .team-item .content-part .name {
  margin-bottom: 8px;
  font-size: 22px;
}
.rs-team.style1 .team-item .content-part .name a {
  color: #ffffff;
}
.rs-team.style1 .team-item .content-part .name a:hover {
  color: #111111;
}
.rs-team.style1 .team-item .content-part .designation {
  color: #ffffff;
  margin-bottom: 16px;
  display: block;
}
.rs-team.style1 .team-item .content-part .social-links li {
  display: inline;
  margin-right: 35px;
}
.rs-team.style1 .team-item .content-part .social-links li a {
  display: inline-block;
  color: #ffffff;
}
.rs-team.style1 .team-item .content-part .social-links li a:hover {
  color: #111111;
}
.rs-team.style1 .team-item .content-part .social-links li:last-child {
  margin: 0;
}
.rs-team.style1 .team-item:after {
  position: absolute;
  content: '';
  left: 50%;
  top: 50%;
  transform: translate(-50%, 100%) scale(0.1);
  background: #21a7d0;
  opacity: 0;
  border-radius: 3px;
  transition: all 0.3s ease;
}
.rs-team.style1 .team-item:hover .content-part {
  transform: translate(-50%, -50%);
  opacity: 1;
}
.rs-team.style1 .team-item:hover:after {
  transform: translate(-50%, -50%) scale(1);
  opacity: 0.9;
}
.rs-team.style1 .owl-item.center .team-item .content-part {
  transform: translate(-50%, -50%);
  opacity: 1;
}
.rs-team.style1 .owl-item.center .team-item:after {
  transform: translate(-50%, -50%) scale(1);
  opacity: 0.9;
}
.rs-team.style1 .nav-style {
  position: relative;
}
.rs-team.style1 .nav-style .owl-nav {
  display: block;
  position: absolute;
  top: -80px;
  right: 0;
}
.rs-team.style1 .nav-style .owl-nav .owl-prev,
.rs-team.style1 .nav-style .owl-nav .owl-next {
  display: inline-block;
}
.rs-team.style1 .nav-style .owl-nav .owl-prev i,
.rs-team.style1 .nav-style .owl-nav .owl-next i {
  transition: all 0.3s ease;
}
.rs-team.style1 .nav-style .owl-nav .owl-prev i:before,
.rs-team.style1 .nav-style .owl-nav .owl-next i:before {
  font-family: Flaticon;
  font-size: 22px;
}
.rs-team.style1 .nav-style .owl-nav .owl-prev:hover i,
.rs-team.style1 .nav-style .owl-nav .owl-next:hover i {
  color: #21a7d0;
}
.rs-team.style1 .nav-style .owl-nav .owl-prev i:before {
  content: "\f134";
}
.rs-team.style1 .nav-style .owl-nav .owl-prev:after {
  content: "/";
  padding: 0 5px 0 5px;
  position: relative;
  top: -3px;
}
.rs-team.style1 .nav-style .owl-nav .owl-next i:before {
  content: "\f133";
}
.rs-team.style1.orange-color .team-item:after {
  background: black;
}
.rs-team.style7 .team-item {
  position: relative;
  overflow: hidden;
}
.rs-team.style7 .team-item:after {
  position: absolute;
  content: '';
  left: 50%;
  top: 50%;
  transform: translate(-50%, 100%) scale(0.1);
  background: #c701f1 !important;
  opacity: 0;
  border-radius: 3px;
  transition: all 0.3s ease;
}
.rs-team.style7 .team-item:hover .content-part {
  transform: translate(-50%, -50%);
  opacity: 1;
}
.rs-team.style7 .team-item:hover:after {
  transform: translate(-50%, -50%) scale(1);
  opacity: 0.9;
}
.rs-team.inner-style .team-item {
  overflow: hidden;
}
.rs-team.inner-style .team-item .content-part {
  top: unset !important;
  bottom: -205px;
  transform: translateX(-50%) !important;
  width: 100% !important;
  height: 320px !important;
  background: #18191A;
  border-radius: 50%;
  padding-top: 30px;
  opacity: 1 !important;
  transition: all 0.3s ease;
}
.rs-team.inner-style .team-item .content-part .name a {
  color: #fff !important;
}
.rs-team.inner-style .team-item .content-part .name a:hover {
  color: #fff !important;
}
.rs-team.inner-style .team-item .content-part .designation {
  color: #fff !important;
}
.rs-team.inner-style .team-item .content-part .social-links {
  display: block;
}
.rs-team.inner-style .team-item:hover .content-part {
  bottom: unset;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: unset !important;
  height: unset !important;
  background: #18191A;
  background: transparent;
  border-radius: unset;
  padding-top: 0;
  opacity: 1 !important;
}
.rs-team.inner-style .team-item:hover .content-part .name a {
  color: #ffffff !important;
}
.rs-team.inner-style .team-item:hover .content-part .name a:hover {
  color: #111111 !important;
}
.rs-team.inner-style .team-item:hover .content-part .designation {
  color: #ffffff !important;
}
.rs-team.inner-style .team-item:hover .content-part .social-links {
  display: block;
}
.rs-team.home11-style .team-item .team-thumbnail {
  position: relative;
  overflow: hidden;
}
.rs-team.home11-style .team-item .team-thumbnail .team-img {
  position: relative;
  overflow: hidden;
  border-radius: 6px;
}
.rs-team.home11-style .team-item .team-thumbnail .team-img .team-social-link {
  display: grid;
  border: 30px solid #4e49a1;
  border-radius: 50%;
  width: 196px;
  height: 193px;
  grid-template-columns: auto auto;
  position: absolute;
  right: -196px;
  bottom: -193px;
  padding: 30px 30px 30px 30px;
  opacity: 0;
  z-index: 9;
  transition: all 0.3s ease;
}
.rs-team.home11-style .team-item .team-thumbnail .team-img .team-social-link li {
  display: inline-block;
}
.rs-team.home11-style .team-item .team-thumbnail .team-img .team-social-link li a {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ffffff;
  cursor: pointer;
  font-size: 16px;
}
.rs-team.home11-style .team-item .team-thumbnail .team-img .team-social-link li a:hover {
  color: #21a7d0;
}
.rs-team.home11-style .team-item .team-thumbnail .team-img:before {
  content: '';
  transition: all ease-in-out 0.4s;
  position: absolute;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 2;
  opacity: 0;
}
.rs-team.home11-style .team-item .team-thumbnail .team-header {
  text-align: center;
  padding-top: 35px;
}
.rs-team.home11-style .team-item .team-thumbnail .team-header .name {
  font-size: 22px;
  line-height: 32px;
  font-weight: 800;
  margin-bottom: 5px;
}
.rs-team.home11-style .team-item .team-thumbnail .team-header .name a {
  color: #4e49a1;
}
.rs-team.home11-style .team-item .team-thumbnail .team-header .name a:hover {
  color: #21a7d0;
}
.rs-team.home11-style .team-item .team-thumbnail .team-header .subject {
  font-size: 15px;
  line-height: 26px;
  font-weight: 400;
  color: #54647b;
  margin-bottom: 10px;
}
.rs-team.home11-style .team-item .team-thumbnail:hover .team-img .team-social-link {
  right: -50px;
  bottom: -45px;
  opacity: 1;
}
.rs-team.home11-style .team-item .team-thumbnail:hover .team-img .team-social-link li a:hover {
  color: #21a7d0;
}
.rs-team.home11-style .team-item .team-thumbnail:hover .team-img:before {
  opacity: 0.4;
}
/* -----------------------------------
    16. Team Single Section CSS
-------------------------------------*/
.profile-section {
  position: relative;
}
.profile-section .content-part .title {
  color: #111111;
  font-weight: 600;
  line-height: 1.3em;
  font-size: 26px;
}
.profile-section .content-part p {
  font-size: 16px;
  margin: 0;
}
.profile-section .content-column {
  position: relative;
}
.profile-section .content-column .inner-column {
  position: relative;
}
.profile-section .content-column .inner-column h2 {
  position: relative;
  color: #111111;
  font-weight: 700;
  margin: 0;
}
.profile-section .content-column .inner-column h4 {
  position: relative;
  color: #111111;
  font-weight: 600;
  line-height: 1.3em;
  margin-top: 15px;
  margin-bottom: 20px;
}
.profile-section .content-column .inner-column h5 {
  position: relative;
  color: #111111;
  font-size: 25px;
  font-weight: 600;
  line-height: 1.3em;
  margin-top: 12px;
  margin-bottom: 0px;
}
.profile-section .content-column .inner-column p {
  position: relative;
  color: #626262;
  font-weight: 400;
  line-height: 1.9em;
  font-size: 16px;
  margin-top: 20px;
  margin-bottom: 35px;
}
.profile-section .content-column .inner-column .student-list {
  position: relative;
  margin-top: 25px;
  margin-bottom: 10px;
}
.profile-section .content-column .inner-column .student-list li {
  position: relative;
  color: #393939;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.3em;
  margin-right: 80px;
  display: inline-block;
  list-style: disc;
  margin-bottom: 20px;
}
.profile-section .content-column .inner-column .student-list li .fa {
  color: #ff5421;
  margin: 0px 2px;
}
.profile-section .content-column .inner-column .student-list li:last-child {
  margin-right: 0px;
}
.profile-section .image-column {
  position: relative;
}
.profile-section .image-column .inner-column {
  position: relative;
  padding: 30px;
  border-radius: 8px;
  /*background-color: #ffffff;*/
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.12);
}
.profile-section .image-column .inner-column .image {
  position: relative;
}
.profile-section .image-column .inner-column .image img {
  position: relative;
  display: inline-block;
  border-radius: 5px;
  width: 100%;
}
.profile-section .image-column .team-content h3 {
  position: relative;
  font-weight: 700;
  color: #111111;
  margin-top: 30px;
  margin-bottom: 10px;
}
.profile-section .image-column .team-content .text {
  position: relative;
  /*color: #626262;*/
  font-size: 16px;
  line-height: 1.7em;
  margin-bottom: 8px;
}
.profile-section .image-column .team-content .personal-info {
  margin-bottom: 20px;
}
.profile-section .image-column .team-content .personal-info li {
  margin: 5px 0;
  display: inline-block;
  padding-right: 15px;
}
.profile-section .image-column .team-content .personal-info li:last-child {
  margin-right: none;
}
.profile-section .image-column .team-content .personal-info li a {
  color: #fff;
}
.profile-section .image-column .team-content .personal-info li a:hover {
  color: #ff5421;
}
.profile-section .image-column .team-content .personal-info li span i:before {
  font-size: 16px;
  margin-right: 10px;
}
.profile-section .image-column .social-box {
  position: relative;
  text-align: center;
}
.profile-section .image-column .social-box a {
  position: relative;
  color: #626262;
  font-size: 28px;
  margin: 0px 5px;
  display: inline-block;
  transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}
.profile-section .image-column .social-box a:hover {
  color: #ff5421;
}
.profile-section .browse-course-section {
  position: relative;
  margin-top: 50px;
}
.profile-section .browse-course-section .styled-pagination {
  margin-top: 20px;
}
.profile-section .skill-title {
  font-size: 25px;
  margin-top: 15px;
  margin-bottom: 30px;
}
.profile-section .rs-progress {
  height: 8px;
  border: 1px solid #ff5421;
  border-radius: 0;
  overflow: visible;
  padding: 1px;
  background: #fff;
  margin-top: 25px;
}
.profile-section .rs-progress + .rs-progress {
  margin-top: 50px;
}
.profile-section .rs-progress .progress-bar {
  position: relative;
  text-align: left;
  line-height: 4px;
  border-radius: 0;
  box-shadow: none;
  background-color: #ff5421;
  overflow: visible;
}
.profile-section .rs-progress .progress-bar .pb-label {
  position: absolute;
  left: 0px;
  top: -24px;
  color: #666;
  font-size: 15px;
  font-weight: 600;
}
.profile-section .rs-progress .progress-bar .pb-percent {
  position: absolute;
  right: -13px;
  font-weight: 500;
  color: #fff;
  font-size: 10px;
  top: -30px;
  background: #ff5421;
  padding: 8px 5px;
}
.profile-section .rs-progress .progress-bar .pb-percent:after {
  content: '';
  position: absolute;
  border: 5px solid transparent;
  left: 50%;
  border-top-color: #ff5421;
  top: 20px;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}
/* ------------------------------------
    17. Testimonial Section CSS
---------------------------------------*/
.rs-testimonial.style1 .testi-item {
  background: #f3f8f9;
  max-width: 1000px;
  margin: 0 0 0 auto;
  position: relative;
}
.rs-testimonial.style1 .testi-item .img-part {
  position: absolute;
  left: -240px;
  top: 50px;
}
.rs-testimonial.style1 .testi-item .img-part img {
  border-radius: 5px;
  max-width: 450px;
}
.rs-testimonial.style1 .testi-item .content-part {
  padding: 98px 0 98px 320px;
}
.rs-testimonial.style1 .testi-item .content-part .desc {
  font-family: 'Nunito', sans-serif;
  font-weight: 600;
  color: #111111;
  font-size: 36px;
  line-height: 46px;
  position: relative;
}
.rs-testimonial.style1 .testi-item .content-part .desc:before,
.rs-testimonial.style1 .testi-item .content-part .desc:after {
  content: "\f151";
  font-family: Flaticon;
  font-size: 20px;
  color: #111111;
}
.rs-testimonial.style1 .testi-item .content-part .desc:before {
  transform: rotateY(180deg);
  position: absolute;
  left: -30px;
  top: -13px;
}
.rs-testimonial.style1 .testi-item .content-part .desc:after {
  position: relative;
  bottom: -7px;
  right: -8px;
}
.rs-testimonial.style1 .testi-item .content-part .desc.title-color {
  color: #111111;
}
.rs-testimonial.style1 .testi-item .content-part .name {
  margin: 0;
  color: #111111;
  font-weight: 500;
}
.rs-testimonial.style1 .rs-carousel.nav-style2 .owl-nav {
  bottom: 35px;
  right: 50px;
  top: unset;
}
.rs-testimonial.style2 .donation-part img {
  border-radius: 5px;
  margin-bottom: 22px;
}
.rs-testimonial.style2 .testi-wrap {
  overflow: hidden;
  background: #f3f8f9;
  border-radius: 5px;
  padding: 30px;
}
.rs-testimonial.style2 .testi-wrap .img-part {
  max-width: 209px;
  float: left;
  margin-right: 58px;
}
.rs-testimonial.style2 .testi-wrap .img-part img {
  border-radius: 5px;
}
.rs-testimonial.style2 .testi-wrap .content-part .desc {
  font-size: 22px;
  font-weight: 600;
  line-height: 30px;
  font-family: 'Nunito', sans-serif;
  color: #112958;
  display: inline;
  position: relative;
}
.rs-testimonial.style2 .testi-wrap .content-part .desc:before,
.rs-testimonial.style2 .testi-wrap .content-part .desc:after {
  content: "\f151";
  font-family: Flaticon;
  font-size: 20px;
  color: #112958;
}
.rs-testimonial.style2 .testi-wrap .content-part .desc:after {
  position: relative;
  bottom: -7px;
  right: -8px;
}
.rs-testimonial.style2 .testi-wrap .content-part .desc:before {
  transform: rotateY(180deg);
  position: absolute;
  left: -30px;
  top: -13px;
}
.rs-testimonial.style2 .testi-wrap .content-part .desc.title-color {
  color: #111111;
}
.rs-testimonial.style2 .testi-wrap .content-part .info {
  margin-top: 20px;
  color: #505050;
}
.rs-testimonial.style2 .testi-wrap .content-part .info .name {
  margin: 0;
  font-weight: 600;
}
.rs-testimonial.style2 .testi-wrap .content-part.new-content .desc {
  font-size: 22px;
  font-weight: 600;
  line-height: 30px;
  font-family: 'Nunito', sans-serif;
  color: #111111;
  display: inline;
  position: relative;
}
.rs-testimonial.style2 .testi-wrap .content-part.new-content .desc:before,
.rs-testimonial.style2 .testi-wrap .content-part.new-content .desc:after {
  content: "\f151";
  font-family: Flaticon;
  font-size: 20px;
  color: #111111;
}
.rs-testimonial.style2 .testi-wrap .content-part.new-content .desc:after {
  position: relative;
  bottom: -7px;
  right: -8px;
}
.rs-testimonial.style2 .testi-wrap .content-part.new-content .desc:before {
  transform: rotateY(180deg);
  position: absolute;
  left: -30px;
  top: -13px;
}
.rs-testimonial.style3 .testi-item {
  background-color: #f3f8f9;
  background-image: url(assets/images/testimonial/cloud-pattern.png);
  background-repeat: no-repeat;
  background-size: 130%;
  background-position: 0 70px;
  border-radius: 10px;
  padding: 40px 45px;
}
.rs-testimonial.style3 .testi-item .user-info img {
  max-width: 84px;
  height: auto;
  margin-bottom: 22px;
}
.rs-testimonial.style3 .testi-item .user-info .name {
  font-size: 20px;
  margin-bottom: 5px;
}
.rs-testimonial.style3 .testi-item .user-info .designation {
  font-size: 14px;
  color: #555555;
  font-weight: 500;
}
.rs-testimonial.style3 .testi-item .user-info .ratings {
  margin-top: 10px;
}
.rs-testimonial.style3 .testi-item .user-info .ratings li {
  display: inline;
}
.rs-testimonial.style3 .testi-item .user-info .ratings li i {
  color: #fcb903;
}
.rs-testimonial.style3 .testi-item .desc {
  font-size: 17px;
  font-style: italic;
}
.rs-testimonial.style3 .owl-dots {
  text-align: center;
  margin-top: 47px;
}
.rs-testimonial.style3 .owl-dots .owl-dot {
  display: inline-block;
  width: 15px;
  height: 5px;
  border-radius: 2.5px;
  background: #21a7d0;
  margin: 0 5px;
  opacity: 0.5;
}
.rs-testimonial.style3 .owl-dots .owl-dot.active {
  opacity: 1;
}
.rs-testimonial.style4 .testi-item {
  border-radius: 10px;
  padding: 30px;
  padding-right: 20px;
  background: #ffffff;
  border: 1px solid rgba(33, 167, 208, 0.1);
  transition: all 0.3s ease;
}
.rs-testimonial.style4 .testi-item .user-img img {
  max-width: 100px;
  height: auto;
}
.rs-testimonial.style4 .testi-item .user-info {
  padding-left: 20px;
}
.rs-testimonial.style4 .testi-item .user-info .name {
  font-size: 18px;
  line-height: 1.2;
  font-weight: 600;
  margin: 0;
  color: #111111;
}
.rs-testimonial.style4 .testi-item .user-info .name:hover {
  color: #21a7d0;
}
.rs-testimonial.style4 .testi-item .user-info .designation {
  display: block;
}
.rs-testimonial.style4 .testi-item .user-info .desc {
  margin-bottom: 20px;
}
.rs-testimonial.style4 .testi-item .user-info .quote {
  color: #111111;
  font-weight: bold;
}
.rs-testimonial.style4 .center .testi-item {
  border: none;
  box-shadow: 0px 0px 32px 0px rgba(0, 0, 0, 0.1);
}
.rs-testimonial.style4 .owl-dots {
  text-align: center;
  margin-top: 47px;
}
.rs-testimonial.style4 .owl-dots .owl-dot {
  display: inline-block;
  width: 15px;
  height: 5px;
  border-radius: 2.5px;
  background: #21a7d0;
  margin: 0 5px;
  opacity: 0.5;
}
.rs-testimonial.style4 .owl-dots .owl-dot.active {
  opacity: 1;
}
.rs-testimonial.style4 .owl-stage-outer {
  padding: 10px;
  margin: -10px;
}
.rs-testimonial.style5 {
  background: url(assets/images/bg/testi-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
}
.rs-testimonial.style5 .testi-item .author-desc {
  position: relative;
  text-align: center;
  padding: 35px 15px;
}
.rs-testimonial.style5 .testi-item .author-desc .desc {
  position: relative;
  background: #ffffff;
  padding: 25px 25px 55px;
}
.rs-testimonial.style5 .testi-item .author-desc .desc .quote {
  width: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 10px;
}
.rs-testimonial.style5 .testi-item .author-desc .desc:before {
  content: '';
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: #dbf2f7;
  z-index: -1;
  transform: rotate(10deg);
  border-radius: 10px;
}
.rs-testimonial.style5 .testi-item .author-desc .author-img {
  position: absolute;
  bottom: -55px;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
}
.rs-testimonial.style5 .testi-item .author-desc .author-img img {
  width: auto;
  border-radius: 50%;
}
.rs-testimonial.style5 .testi-item .author-part {
  text-align: center;
}
.rs-testimonial.style5 .testi-item .author-part .name {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #111111;
  margin-top: 15px;
}
.rs-testimonial.style6 .testimonial-item {
  display: flex;
}
.rs-testimonial.style6 .testimonial-item .content-part {
  padding: 0 30px;
}
.rs-testimonial.style6 .testimonial-item .content-part .content-wrap .text p {
  font-size: 24px;
  line-height: 40px;
  color: #222222;
  font-style: italic;
}
.rs-testimonial.style6 .testimonial-item .content-part .content-wrap .info .name {
  font-size: 22px;
  line-height: 38px;
  color: #031a3d;
  margin-bottom: 10px;
}
.rs-testimonial.style6 .testimonial-item .content-part .content-wrap .info .position {
  font-size: 16px;
  line-height: 28px;
  color: #54647b;
}
.rs-testimonial.style7 {
  background: #e7f8fb;
}
.rs-testimonial.style7 .testi-item {
  background: #ffffff !important;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05) !important;
}
.rs-testimonial.style7 .testi-item .user-info img {
  width: 80px !important;
  height: 80px !important;
  border-radius: 50% !important;
  display: block !important;
}
.rs-testimonial.style8 .testi-item .author-desc {
  position: relative;
  text-align: center;
  padding: 35px 15px;
}
.rs-testimonial.style8 .testi-item .author-desc .desc {
  position: relative;
  background-color: #f9f8f8;
  padding: 25px 25px 55px;
}
.rs-testimonial.style8 .testi-item .author-desc .desc .quote {
  width: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 10px;
}
.rs-testimonial.style8 .testi-item .author-desc .desc:before {
  content: '';
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: #f9f8f8;
  z-index: -1;
  transform: rotate(10deg);
  border-radius: 10px;
}
.rs-testimonial.style8 .testi-item .author-desc .author-img {
  position: absolute;
  bottom: -55px;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
}
.rs-testimonial.style8 .testi-item .author-desc .author-img img {
  margin: auto;
  max-width: 70px;
  border-radius: 50%;
}
.rs-testimonial.style8 .testi-item .author-part {
  text-align: center;
}
.rs-testimonial.style8 .testi-item .author-part .name {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #111111;
  margin-top: 15px;
}
.rs-testimonial.style8 .owl-dots {
  text-align: center;
  margin-top: 47px;
}
.rs-testimonial.style8 .owl-dots .owl-dot {
  width: 25px;
  height: 10px;
  display: inline-block;
  margin: 0 6px;
  padding: 3px 0;
  border-radius: 30px;
  background: #ed3600;
  border: none;
  transition: all 0.3s ease;
}
.rs-testimonial.style8 .owl-dots .owl-dot:hover,
.rs-testimonial.style8 .owl-dots .owl-dot.active {
  width: 40px;
  opacity: 1;
  background: #ff5421;
}
.rs-testimonial.style8 .owl-stage-outer {
  padding: 10px;
  margin: -10px;
}
.rs-testimonial.main-home {
  background: url(assets/images/bg/main-home.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.rs-testimonial.main-home .testi-item .author-desc {
  padding: 70px 15px;
  background-color: #171F3296;
  border-radius: 5px 5px 5px 5px;
}
.rs-testimonial.main-home .testi-item .author-desc .desc {
  text-align: center;
  position: relative;
  color: #ffffff;
  padding: 80px 40px 30px;
  font-size: 23px;
  line-height: 33px;
  font-weight: 500;
  font-style: italic;
}
.rs-testimonial.main-home .testi-item .author-desc .desc .quote {
  width: 66px;
  top: -13px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
.rs-testimonial.main-home .testi-item .author-desc .desc:before {
  content: none;
}
.rs-testimonial.main-home .testi-item .author-desc .author-img {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 80px;
}
.rs-testimonial.main-home .testi-item .author-desc .author-img img {
  width: auto;
  border-radius: 50%;
}
.rs-testimonial.main-home .testi-item .author-part {
  text-align: center;
  padding-top: 45px;
}
.rs-testimonial.main-home .testi-item .author-part .name {
  font-size: 20px;
  font-weight: 700;
  line-height: 45px;
  color: #ffffff;
  display: block;
}
.rs-testimonial.main-home .testi-item .author-part .designation {
  font-size: 16px;
  color: #ffffff;
}
.rs-testimonial.main-home .owl-dots {
  text-align: center;
  margin-top: 47px;
}
.rs-testimonial.main-home .owl-dots .owl-dot {
  width: 25px;
  height: 10px;
  display: inline-block;
  margin: 0 6px;
  padding: 3px 0;
  border-radius: 30px;
  background: #ed3600;
  border: none;
  transition: all 0.3s ease;
}
.rs-testimonial.main-home .owl-dots .owl-dot:hover,
.rs-testimonial.main-home .owl-dots .owl-dot.active {
  width: 40px;
  background: #ff5421;
  opacity: 1;
}
.rs-testimonial.main-home .owl-stage-outer {
  padding: 10px;
  margin: -10px;
}
.rs-testimonial.home9-style .testi-item p {
  font-size: 25px;
  font-style: italic;
  color: #111111;
  padding: 15px 0px 15px 0px;
  margin-bottom: 30px;
}
.rs-testimonial.home9-style .testi-item .testi-information {
  color: #505050;
}
.rs-testimonial.home9-style .testi-item .testi-information .name {
  font-size: 24px;
  font-weight: 700;
  text-transform: uppercase;
  color: #111111;
  margin-bottom: 15px;
}
.rs-testimonial.home9-style .testi-item .testi-information .designation {
  font-size: 22px;
  font-weight: 500;
}
.rs-testimonial.home-style1 .testi-item .author-desc {
  padding: 70px 15px;
  background-color: #f3f8f9;
  border-radius: 5px 5px 5px 5px;
}
.rs-testimonial.home-style1 .testi-item .author-desc .desc {
  text-align: center;
  position: relative;
  color: #505050;
  padding: 80px 40px 30px;
  font-size: 23px;
  line-height: 33px;
  font-weight: 500;
  font-style: italic;
}
.rs-testimonial.home-style1 .testi-item .author-desc .desc .quote {
  width: 66px;
  top: -13px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
.rs-testimonial.home-style1 .testi-item .author-desc .desc:before {
  content: none;
}
.rs-testimonial.home-style1 .testi-item .author-desc .author-img {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 80px;
}
.rs-testimonial.home-style1 .testi-item .author-desc .author-img img {
  width: auto;
  border-radius: 50%;
}
.rs-testimonial.home-style1 .testi-item .author-part {
  text-align: center;
  padding-top: 45px;
}
.rs-testimonial.home-style1 .testi-item .author-part .name {
  font-size: 20px;
  font-weight: 700;
  line-height: 45px;
  color: #ffffff;
  display: block;
}
.rs-testimonial.home-style1 .testi-item .author-part .designation {
  font-size: 16px;
  color: #ffffff;
}
.rs-testimonial.home-style1 .owl-dots {
  text-align: center;
  margin-top: 47px;
}
.rs-testimonial.home-style1 .owl-dots .owl-dot {
  width: 40px;
  height: 10px;
  display: inline-block;
  margin: 0 6px;
  padding: 3px 0;
  border-radius: 30px;
  background: #21a7d0;
  border: none;
}
.rs-testimonial.home-style1 .owl-dots .owl-dot:hover,
.rs-testimonial.home-style1 .owl-dots .owl-dot.active {
  background: #21a7d0;
  opacity: 1;
}
.rs-testimonial.home-style1 .owl-stage-outer {
  padding: 10px;
  margin: -10px;
}
.rs-testimonial.home11-style .rs-carousel .owl-dots {
  text-align: center;
}
.rs-testimonial.home11-style .rs-carousel .owl-dots .owl-dot {
  position: relative;
  border: none;
  border-radius: 50%;
  background: #21a7d0;
  width: 10px;
  height: 10px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  margin-right: 11px;
}
.rs-testimonial.home11-style .rs-carousel .owl-dots .owl-dot:before {
  content: "";
  position: absolute;
  width: 18px;
  height: 18px;
  left: -4px;
  top: -4px;
  border-radius: 50%;
  border-width: 1px;
  border-style: solid;
  border-color: #4e49a1;
  border-image: initial;
}
.rs-testimonial.home11-style .rs-carousel .owl-dots .active {
  background: #4e49a1;
}
.rs-testimonial.home11-style .testi-item .user-info {
  position: relative;
  padding: 25px 30px;
  background: #e7f9fb;
  border: 1px solid #e7f9fb;
}
.rs-testimonial.home11-style .testi-item .user-info .desc {
  font-size: 18px;
  line-height: 30px;
  font-weight: 400;
  color: #54647b;
  margin-bottom: 15px;
}
.rs-testimonial.home11-style .testi-item .user-info .name {
  font-size: 20px;
  line-height: 30px;
  font-weight: 600;
  color: #4e49a1;
  margin-bottom: 0;
}
.rs-testimonial.home11-style .testi-item .user-info .name:hover {
  color: #21a7d0;
}
.rs-testimonial.home11-style .testi-item .user-info .position {
  font-size: 15px;
  margin-bottom: 0;
}
.rs-testimonial.home11-style .testi-item .user-info .position span {
  font-weight: 500;
}
.rs-testimonial.home11-style .testi-item .user-info::before {
  content: "";
  height: 0;
  width: 0;
  top: 30px;
  right: 100%;
  border: solid transparent;
  position: absolute;
  pointer-events: none;
}
.rs-testimonial.home11-style .testi-item .user-info:before {
  border-right-color: #e7f9fb;
  border-width: 15px;
}
.rs-testimonial.orange-color .testi-item .user-info .ratings li i {
  color: #ff5421;
}
.rs-testimonial.orange-color .owl-dots .owl-dot {
  border-color: #ff5421 !important;
  background: #ff5421 !important;
}
.rs-testimonial.home12-style {
  background: url(assets/images/bg/home12/testi-bg.jpg);
  background-repeat: no-repeat;
  background-position: center top;
  background-size: cover;
  padding: 150px 0;
}
.rs-testimonial.home12-style .testi-item {
  padding: 100px 40px 40px 40px;
  background-color: #ffffff;
  margin-right: 14px;
  margin-bottom: 0px;
}
.rs-testimonial.home12-style .testi-item .item-content-basic {
  position: relative;
  text-align: center;
}
.rs-testimonial.home12-style .testi-item .item-content-basic .desc {
  font-style: italic;
  margin-bottom: 28px;
}
.rs-testimonial.home12-style .testi-item .item-content-basic .desc .quote {
  width: 46px;
  top: -70px;
  position: absolute;
  z-index: 1;
  left: 50%;
  transform: translateX(-50%);
}
.rs-testimonial.home12-style .testi-item .item-content-basic .testi-content .img-wrap {
  margin-bottom: 20px;
}
.rs-testimonial.home12-style .testi-item .item-content-basic .testi-content .img-wrap img {
  height: 65px;
  width: 65px;
  border-radius: 50%;
  position: relative;
  margin: 0 auto;
}
.rs-testimonial.home12-style .testi-item .item-content-basic .testi-content .name {
  font-size: 20px;
  color: #000000;
  font-weight: 700;
  font-family: 'Nunito', sans-serif;
}
.rs-testimonial.home12-style .testi-item .item-content-basic .testi-content .designation {
  font-size: 16px;
  line-height: 27px;
  font-weight: 400;
  color: #555555;
}
.rs-testimonial.home13-style .content {
  margin-bottom: -170px;
}
.rs-testimonial.home13-style .content .sub-title {
  font-size: 16px;
  line-height: 27px;
  font-weight: 400;
  color: #ff5421;
  text-transform: uppercase;
}
.rs-testimonial.home13-style .content .title {
  font-size: 42px;
  line-height: 52px;
  font-weight: 600;
  color: #ffffff;
}
.rs-testimonial.home13-style .slick-part .slider-nav .slick-list {
  padding: 0 7px !important;
  margin-bottom: 32px;
}
.rs-testimonial.home13-style .slick-part .slider .images-single {
  padding: 0 0 0 6.5px;
}
.rs-testimonial.home13-style .slick-part .slider .images-single img {
  border-radius: 3px;
}
.rs-testimonial.home13-style .slick-part .slider .images-single .polite {
  background: #f00;
}
.rs-testimonial.home13-style .slick-part .slider .images-slide-single .single-testimonial {
  display: flex;
  align-items: center;
}
.rs-testimonial.home13-style .slick-part .slider .images-slide-single .single-testimonial .images-part {
  width: 50%;
}
.rs-testimonial.home13-style .slick-part .slider .images-slide-single .single-testimonial .content-part {
  width: 50%;
}
.rs-testimonial.home13-style .slick-part .slider .images-slide-single .content-part {
  width: 50%;
  padding: 0px 0px 0px 34px;
}
.rs-testimonial.home13-style .slick-part .slider .images-slide-single .content-part .quote {
  width: 40px;
  margin-bottom: 20px;
}
.rs-testimonial.home13-style .slick-part .slider .images-slide-single .content-part p {
  font-size: 22px;
  font-weight: 400;
  font-style: italic;
  color: #ffffff;
  padding: 0px 0px 30px 0px;
}
.rs-testimonial.home13-style .slick-part .slider .images-slide-single .content-part .name {
  font-size: 22px;
  font-weight: 600;
  line-height: 32px;
  color: #ffffff;
}
.rs-testimonial.home13-style .slick-part .slider button {
  border: none;
  font-size: 0px;
  background: none;
}
.rs-testimonial.home13-style .slick-part .slider button.slick-prev:before {
  content: "\f137";
  font-family: Flaticon;
  color: #fff;
  font-size: 18px;
  background: #ff5421;
  width: 45px;
  display: block;
  height: 45px;
  line-height: 45px;
  border-radius: 35px;
  right: 40%;
  bottom: 50px;
  position: absolute;
  z-index: 10;
}
.rs-testimonial.home14-style .owl-stage-outer {
  padding: 10px 10px 10px;
  margin: 0 10px;
}
.rs-testimonial.home14-style .testi-item {
  padding: 38px 37px 30px 37px;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.03);
}
.rs-testimonial.home14-style .testi-item .image-wrap {
  position: relative;
  margin-bottom: 25px;
}
.rs-testimonial.home14-style .testi-item .image-wrap img {
  height: 65px;
  width: 65px;
  border-radius: 50%;
  position: relative;
}
.rs-testimonial.home14-style .testi-item .item-content {
  position: relative;
}
.rs-testimonial.home14-style .testi-item .item-content .quote-positions {
  width: 18px;
  top: 136px;
  position: absolute;
  z-index: 1;
  left: 154px;
}
.rs-testimonial.home14-style .testi-item .item-content p {
  font-size: 18px;
  line-height: 31px;
  font-weight: 400;
  color: #505050;
  margin-bottom: 18px;
}
.rs-testimonial.home14-style .testi-item .testi-content .testi-name {
  font-size: 20px;
  line-height: 34px;
  font-weight: 500;
  color: #101010;
}
.rs-testimonial.home14-style .testi-item .testi-content .testi-title {
  font-size: 18px;
  line-height: 31px;
  font-weight: 400;
  color: #505050;
  display: block;
}
/* ------------------------------------
    18. Blog Section CSS
---------------------------------------*/
.rs-blog.style1 .blog-item {
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid #dfe9eb;
}
.rs-blog.style1 .blog-item .image-part {
  overflow: hidden;
  height: 100%;
}
.rs-blog.style1 .blog-item .image-part a img {
  transition: all 0.5s;
  transform: scale(1);
  height: 100%;
  width: 100%;
}
.rs-blog.style1 .blog-item .blog-content {
  padding: 37px 19px 30px;
}
.rs-blog.style1 .blog-item .blog-content .title {
  margin-bottom: 14px;
  font-size: 24px;
  line-height: 34px;
}
.rs-blog.style1 .blog-item .blog-content .title a {
  color: #111111;
}
.rs-blog.style1 .blog-item .blog-content .title a:hover {
  color: #21a7d0;
}
.rs-blog.style1 .blog-item .blog-content .blog-meta {
  margin-bottom: 11px;
}
.rs-blog.style1 .blog-item .blog-content .blog-meta li {
  position: relative;
  margin-right: 5px;
  padding-right: 15px;
  display: inline-block;
}
.rs-blog.style1 .blog-item .blog-content .blog-meta li i {
  padding-right: 3px;
  font-size: 14px;
}
.rs-blog.style1 .blog-item .blog-content .blog-meta li i.fa-calendar {
  padding-right: 7px;
}
.rs-blog.style1 .blog-item .blog-content .blog-meta li a {
  color: #505050;
}
.rs-blog.style1 .blog-item .blog-content .blog-meta li:last-child {
  margin: 0;
  padding: 0;
}
.rs-blog.style1 .blog-item .blog-content .readon-arrow {
  text-transform: uppercase;
}
.rs-blog.style1 .blog-item:hover .image-part a img {
  transform: scale(1.07);
}
.rs-blog.style1 .events-short {
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: #ffffff;
  padding: 30px;
  border: 1px solid #dfe9eb;
  border-radius: 3px;
}
.rs-blog.style1 .events-short .date-part {
  position: absolute;
  background: #f3f8f9;
  width: 100px;
  text-align: center;
  padding: 11px 0 22px;
  border-radius: 3px;
}
.rs-blog.style1 .events-short .date-part .month {
  margin-bottom: 10px;
  display: block;
}
.rs-blog.style1 .events-short .date-part .date {
  font-size: 50px;
  font-family: 'Nunito', sans-serif;
  color: #21a7d0;
  font-weight: 700;
}
.rs-blog.style1 .events-short .content-part {
  padding-left: 120px;
}
.rs-blog.style1 .events-short .content-part .categorie a {
  color: #505050;
}
.rs-blog.style1 .events-short .content-part .categorie a:hover {
  color: #21a7d0;
}
.rs-blog.style1 .events-short .content-part .title {
  line-height: 1.5;
}
.rs-blog.style1 .events-short .content-part .title a {
  color: #111111;
}
.rs-blog.style1 .events-short .content-part .title a:hover {
  color: #21a7d0;
}
.rs-blog.style1 .events-short.new-style {
  padding: unset;
  border: none;
}
.rs-blog.style1 .events-short.new-style .date-part {
  position: absolute;
  width: 100px;
  text-align: center;
  padding: 11px 0 22px;
  border-radius: 3px;
}
.rs-blog.style1 .events-short.new-style .date-part .month {
  color: #ffffff;
}
.rs-blog.style1 .events-short.new-style .date-part .date {
  color: #ffffff;
}
.rs-blog.style1 .events-short.new-style .date-part.bg1 {
  background: #4e49a1;
}
.rs-blog.style1 .events-short.new-style .date-part.bg2 {
  background: #ff6666;
}
.rs-blog.style1 .events-short.new-style .date-part.bg3 {
  background: #ffaf40;
}
.rs-blog.style1 .events-short.new-style .date-part.bg4 {
  background: #95cc47;
}
.rs-blog.style1 .events-short.new-style .content-part .categorie .timesec {
  color: #54647b;
}
.rs-blog.style1 .events-short.new-style .content-part .categorie a {
  color: #4e49a1;
}
.rs-blog.style1 .events-short.new-style .content-part .title {
  padding-right: 62px;
  font-size: 22px;
}
.rs-blog.style1 .events-short.new-style .content-part .title a {
  color: #4e49a1;
}
.rs-blog.style1 .events-short.new-style .content-part .title a:hover {
  color: #ff5421;
}
.rs-blog.style1.modify1 .blog-item {
  border: 1px solid #c7e8f0;
}
.rs-blog.style1.modify1 .blog-item .blog-content {
  padding: 33px 30px 34px;
}
.rs-blog.style1.modify1 .events-short {
  overflow: visible;
  background: #ffffff;
  padding: 6px 0;
  border: none;
  border-radius: 0;
}
.rs-blog.style1.modify1 .events-short .date-part {
  background: #f3f8f9;
}
.rs-blog.style1.modify1 .events-short .date-part.bgc1 {
  background: #dcf1fd;
}
.rs-blog.style1.modify1 .events-short .date-part.bgc2 {
  background: #eaddf8;
}
.rs-blog.style1.modify1 .events-short .date-part.bgc3 {
  background: #f8f6d1;
}
.rs-blog.style1.modify1 .events-short .date-part.bgc4 {
  background: #f9e8e8;
}
.rs-blog.style1.modify1 .events-short .content-part .title {
  font-size: 22px;
}
.rs-blog.style1.home11-style .blog-item {
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid #dfe9eb;
}
.rs-blog.style1.home11-style .blog-item .blog-content {
  padding: 37px 25px 30px;
}
.rs-blog.style1.home11-style .blog-item .blog-content .title a {
  color: #4e49a1;
}
.rs-blog.style1.home11-style .blog-item .blog-content .title a:hover {
  color: #21a7d0;
}
.rs-blog.style1.home11-style .blog-item .blog-content .blog-meta {
  margin-bottom: 11px;
}
.rs-blog.style1.home11-style .blog-item .blog-content .blog-meta li {
  color: #54647b;
}
.rs-blog.style1.home11-style .blog-item .blog-content .btn-part .readon-arrow {
  color: #4e49a1;
}
.rs-blog.style1.home11-style .blog-item .blog-content .btn-part .readon-arrow:after {
  color: #4e49a1;
}
.rs-blog.style1.home11-style .blog-item .blog-content .btn-part .readon-arrow:hover {
  color: #21a7d0;
}
.rs-blog.style1.home11-style .blog-item .blog-content .btn-part .readon-arrow:hover:after {
  color: #21a7d0;
}
.rs-blog.style1.home11-style .blog-item:hover .image-part a img {
  transform: scale(1.07);
}
.rs-blog.style2 .blog-item {
  border-radius: 3px;
  /*background: #ffffff;*/
  overflow: hidden;
}
.rs-blog.style2 .blog-item .image-part {
  overflow: hidden;
  height: 100%;
}
.rs-blog.style2 .blog-item .image-part img {
  transition: all 0.5s;
  transform: scale(1);
}
.rs-blog.style2 .blog-item .blog-content {
  padding: 21px 30px 30px;
  color: #273c66;
  background: #fcfcfc;
}
.rs-blog.style2 .blog-item .blog-content .title {
  margin-bottom: 14px;
  font-size: 24px;
  line-height: 34px;
}
.rs-blog.style2 .blog-item .blog-content .title a {
  color: #fff;
}
.rs-blog.style2 .blog-item .blog-content .title a:hover {
  color: #ddd;
}
.rs-blog.style2 .blog-item .blog-content .categories {
  color: #505050;
}
.rs-blog.style2 .blog-item .blog-content .desc {
  color: #fff;
}
.rs-blog.style2 .blog-item .blog-content .blog-meta {
  margin-bottom: 11px;
  color: #fff;
}
.rs-blog.style2 .blog-item .blog-content .blog-meta li {
  position: relative;
  margin-right: 5px;
  padding-right: 15px;
  display: inline-block;
}
.rs-blog.style2 .blog-item .blog-content .blog-meta li i {
  padding-right: 3px;
  font-size: 14px;
}
.rs-blog.style2 .blog-item .blog-content .blog-meta li i.fa-calendar {
  padding-right: 7px;
}
.rs-blog.style2 .blog-item .blog-content .blog-meta li a {
  color: #273c66;
}
.rs-blog.style2 .blog-item .blog-content .blog-meta li:last-child {
  margin: 0;
  padding: 0;
}
.rs-blog.style2 .blog-item .blog-content .blog-bottom {
  overflow: hidden;
  padding-top: 21px;
  margin-top: 21px;
  border-top: 1px solid #dcdfe6;
}
.rs-blog.style2 .blog-item .blog-content .blog-bottom li {
  display: inline;
}
.rs-blog.style2 .blog-item .blog-content .blog-bottom li.cmnt-part a {
  color: #273c66;
}
.rs-blog.style2 .blog-item .blog-content .blog-bottom li.cmnt-part a:hover {
  color: #21a7d0;
}
.rs-blog.style2 .blog-item .blog-content .blog-bottom li.btn-part {
  float: right;
}
.rs-blog.style2 .blog-item .blog-content .blog-bottom li.btn-part .readon-arrow {
  text-transform: uppercase;
  color: #273c66;
}
.rs-blog.style2 .blog-item .blog-content .blog-bottom li.btn-part .readon-arrow:hover {
  color: #21a7d0;
}
.rs-blog.style2 .blog-item .blog-content.primary-border {
  border: 1px solid rgba(33, 167, 208, 0.1);
  border-top: none;
}
.rs-blog.style2 .blog-item .blog-content.new-style .title a {
  color: #112958;
}
.rs-blog.style2 .blog-item .blog-content.new-style .title a:hover {
  color: #21a7d0;
}
.rs-blog.style2 .blog-item .blog-content.new-style .blog-bottom li {
  display: inline;
}
.rs-blog.style2 .blog-item .blog-content.new-style .blog-bottom li.cmnt-part a {
  color: #505050;
}
.rs-blog.style2 .blog-item .blog-content.new-style .blog-bottom li.cmnt-part a:hover {
  color: #21a7d0;
}
.rs-blog.style2 .blog-item .blog-content.new-style .blog-bottom li.btn-part {
  float: right;
}
.rs-blog.style2 .blog-item .blog-content.new-style .blog-bottom li.btn-part .readon-arrow {
  text-transform: uppercase;
  color: #112958;
}
.rs-blog.style2 .blog-item .blog-content.new-style .blog-bottom li.btn-part .readon-arrow:hover {
  color: #21a7d0;
}
.rs-blog.style2 .blog-item .blog-content.new-style2 .blog-bottom li.cmnt-part a {
  color: #505050;
}
.rs-blog.style2 .blog-item .blog-content.new-style2 .blog-bottom li.cmnt-part a:hover {
  color: #21a7d0;
}
.rs-blog.style2 .blog-item .blog-content.new-style2 .blog-bottom li.btn-part {
  float: right;
}
.rs-blog.style2 .blog-item .blog-content.new-style2 .blog-bottom li.btn-part .readon-arrow {
  text-transform: uppercase;
  color: #111111;
}
.rs-blog.style2 .blog-item .blog-content.new-style2 .blog-bottom li.btn-part .readon-arrow:hover {
  color: #21a7d0;
}
.rs-blog.style2 .blog-item:hover .image-part img {
  transform: scale(1.07);
}
.rs-blog.style2.modify .blog-item .blog-content {
  border: none;
  background: #ffffff;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
}
.rs-blog.style2.modify .blog-item .blog-content .blog-bottom .btn-part {
  font-weight: 600;
  color: #111111;
}
.rs-blog.style2.modify .blog-item .blog-content .blog-bottom .btn-part .readon-arrow {
  font-weight: 600;
  color: #111111;
}
.rs-blog.style2.modify2 .owl-stage-outer {
  padding-bottom: 10px;
  margin: -10px;
  padding-left: 10px;
}
.rs-blog.style2.modify2 .blog-item {
  overflow: visible !important;
}
.rs-blog.style2.modify2 .blog-item .blog-content {
  background: black !important;
  box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.2) !important;
  transition: all 500ms ease;
  position: relative;
}
.rs-blog.style2.modify2 .blog-item .blog-content .categories.color-bg1 {
  color: #fff;
}
.rs-blog.style2.modify2 .blog-item .blog-content .categories.color-bg2 {
  color: #fff;
}
.rs-blog.style2.modify2 .blog-item .blog-content .categories.color-bg3 {
  color: #fff;
}
.rs-blog.style2.modify2 .blog-item:hover .blog-content {
  transform: translateY(-10px);
}
.rs-blog.main-home .owl-stage-outer {
  padding: 0 10px 10px;
  margin: 0 -10px;
}
.rs-blog.main-home .blog-item {
  background: #fff;
  transition: all 500ms ease;
  border-radius: 5px;
}
.rs-blog.main-home .blog-item .blog-content {
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.05);
  padding: 40px 35px 16px;
}
.rs-blog.main-home .blog-item .blog-content .blog-meta {
  margin-bottom: 11px;
}
.rs-blog.main-home .blog-item .blog-content .blog-meta li {
  position: relative;
  margin-right: 5px;
  padding-right: 15px;
  display: inline-block;
}
.rs-blog.main-home .blog-item .blog-content .blog-meta li i {
  padding-right: 3px;
  font-size: 14px;
}
.rs-blog.main-home .blog-item .blog-content .blog-meta li i:before {
  padding-right: 7px;
  color: #ff5421;
}
.rs-blog.main-home .blog-item .blog-content .blog-meta li a {
  color: #505050;
}
.rs-blog.main-home .blog-item .blog-content .blog-meta li:last-child {
  margin: 0;
  padding: 0;
}
.rs-blog.main-home .blog-item .blog-content .title {
  margin-bottom: 14px;
  font-size: 24px;
  line-height: 34px;
}
.rs-blog.main-home .blog-item .blog-content .title a {
  color: #101010;
}
.rs-blog.main-home .blog-item .blog-content .title a:hover {
  color: #ff5421;
}
.rs-blog.main-home .blog-item .blog-content .desc {
  color: #505050;
  padding-bottom: 20px;
}
.rs-blog.main-home .blog-item .blog-content .btn-btm {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f4f0f0;
  padding-top: 20px;
}
.rs-blog.main-home .blog-item .blog-content .btn-btm .cat-list .post-categories {
  display: flex;
  position: relative;
  padding-left: 22px;
}
.rs-blog.main-home .blog-item .blog-content .btn-btm .cat-list .post-categories:before {
  content: "\f02d";
  font-family: fontawesome;
  font-size: 15px;
  color: #ff5421;
  position: absolute;
  left: 0px;
  top: 1px;
}
.rs-blog.main-home .blog-item .blog-content .btn-btm .cat-list .post-categories li {
  font-size: 14px;
  text-transform: capitalize;
}
.rs-blog.main-home .blog-item .blog-content .btn-btm .cat-list .post-categories li a {
  color: #505050;
}
.rs-blog.main-home .blog-item .blog-content .btn-btm .cat-list .post-categories li a:hover {
  color: #ff5421;
}
.rs-blog.main-home .blog-item .blog-content .btn-btm .rs-view-btn a {
  padding: 0px 0px 0px 0px;
  font-family: "Rubik", Sans-serif;
  font-weight: 400;
  color: #505050;
  font-size: 15px;
  position: relative;
}
.rs-blog.main-home .blog-item .blog-content .btn-btm .rs-view-btn a:after {
  content: "\f054";
  font-family: fontawesome;
  font-size: 12px;
  position: relative;
  right: 0px;
  top: 0px;
  display: inline-block;
  margin-left: 3px;
}
.rs-blog.main-home .blog-item .blog-content .btn-btm .rs-view-btn a:hover {
  color: #ff5421;
}
.rs-blog.main-home .blog-item:hover {
  transform: translateY(-10px);
}
.rs-blog.main-home.modify1 .blog-item .blog-content {
  background-color: #F3FCF8;
  padding: 30px 25px 30px 25px;
  border-radius: 0 0 5px 5px;
  box-shadow: unset;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .blog-meta {
  font-size: 14px;
  text-transform: capitalize;
  display: flex;
  justify-content: space-between;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .blog-meta .date {
  color: #333333;
  font-size: 14px;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .blog-meta .date i:before {
  color: #0c8b51;
  padding-right: 5px;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .blog-meta .admin {
  color: #333333;
  font-size: 14px;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .blog-meta .admin i:before {
  color: #0c8b51;
  padding-right: 5px;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .title {
  font-size: 22px;
  line-height: 32px;
  font-weight: 700;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .title a {
  color: #101010;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .title a:hover {
  color: #0c8b51;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .btn-btm .cat-list .post-categories:before {
  color: #0c8b51;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .btn-btm .cat-list .post-categories li {
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .btn-btm .cat-list .post-categories li a {
  color: #333333;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .btn-btm .cat-list .post-categories li a:hover {
  color: #0c8b51;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .btn-btm .rs-view-btn a {
  font-weight: 500;
  color: #333333;
  font-size: 15px;
}
.rs-blog.main-home.modify1 .blog-item .blog-content .btn-btm .rs-view-btn a:hover {
  color: #0c8b51;
}
.rs-blog.home9-style .blog-item {
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid #dfe9eb;
}
.rs-blog.home9-style .blog-item .image-part {
  overflow: hidden;
  height: 100%;
}
.rs-blog.home9-style .blog-item .image-part a img {
  transition: all 0.5s;
  transform: scale(1);
  height: 100%;
  width: 100%;
}
.rs-blog.home9-style .blog-item .blog-content {
  padding: 10px 20px 0px 30px;
}
.rs-blog.home9-style .blog-item .blog-content .title {
  margin-bottom: 14px;
  font-size: 24px;
  line-height: 34px;
}
.rs-blog.home9-style .blog-item .blog-content .title a {
  color: #101010;
}
.rs-blog.home9-style .blog-item .blog-content .title a:hover {
  color: #f4bf00;
}
.rs-blog.home9-style .blog-item .blog-content .txt {
  font-size: 16px;
  color: #505050;
  font-weight: 400;
  margin: 0;
}
.rs-blog.home9-style .blog-item .blog-content .blog-meta {
  margin-bottom: 11px;
}
.rs-blog.home9-style .blog-item .blog-content .blog-meta li {
  position: relative;
  margin-right: 5px;
  padding-right: 15px;
  display: inline-block;
  font-size: 16px;
  color: #505050;
  font-weight: 400;
}
.rs-blog.home9-style .blog-item .blog-content .blog-meta li i {
  padding-right: 3px;
  font-size: 14px;
}
.rs-blog.home9-style .blog-item .blog-content .blog-meta li i:before {
  color: #f4bf00;
  padding-right: 5px;
}
.rs-blog.home9-style .blog-item .blog-content .blog-meta li i:before:last-child {
  padding-right: 0;
}
.rs-blog.home9-style .blog-item .blog-content .blog-meta li a {
  color: #505050;
}
.rs-blog.home9-style .blog-item .blog-content .blog-meta li:last-child {
  margin: 0;
  padding: 0;
}
.rs-blog.home9-style .blog-item:hover .image-part a img {
  transform: scale(1.07);
}
.rs-blog.home9-style .events-short-top {
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
  border: 1px solid #dfe9eb;
  padding: 25px 39px 38px 25px;
  align-items: flex-start;
  background: #ffffff;
}
.rs-blog.home9-style .events-short-top .date-part {
  background: #1C335F;
  min-width: 100px;
  text-align: center;
  padding: 11px 0 22px;
  border-radius: 3px;
  margin-right: 25px;
  transition: all 500ms ease;
}
.rs-blog.home9-style .events-short-top .date-part .month {
  margin-bottom: 10px;
  display: block;
  color: #ffffff;
}
.rs-blog.home9-style .events-short-top .date-part .date {
  font-size: 50px;
  color: #f4bf00;
  font-weight: 700;
  line-height: 25px;
}
.rs-blog.home9-style .events-short-top .date-part:hover {
  transform: translateY(-10px);
}
.rs-blog.home9-style .events-short-top .content-part .categorie a {
  color: #505050;
}
.rs-blog.home9-style .events-short-top .content-part .categorie a:hover {
  color: #ff5421;
}
.rs-blog.home9-style .events-short-top .content-part .title {
  line-height: 1.5;
  font-weight: 700;
  font-size: 24px;
  margin-bottom: 6px;
}
.rs-blog.home9-style .events-short-top .content-part .title a {
  color: #101010;
}
.rs-blog.home9-style .events-short-top .content-part .title a:hover {
  color: #f4bf00;
}
.rs-blog.home9-style .events-short-top .content-part .txt {
  font-size: 16px;
  color: #505050;
  margin: 0;
}
.rs-blog.home9-style .short {
  position: relative;
  display: flex;
  overflow: hidden;
  background: #ffffff;
  padding: 30px;
  border: 1px solid #dfe9eb;
  border-radius: 3px;
}
.rs-blog.home9-style .short .date-part {
  position: absolute;
  background: #1c335f;
  width: 100px;
  text-align: center;
  padding: 11px 0 22px;
  border-radius: 3px;
}
.rs-blog.home9-style .short .date-part .month {
  margin-bottom: 10px;
  display: block;
}
.rs-blog.home9-style .short .date-part .date {
  font-size: 50px;
  font-family: 'Nunito', sans-serif;
  color: #21a7d0;
  font-weight: 700;
}
.rs-blog.home9-style .short .content-part {
  padding-left: 120px;
}
.rs-blog.home9-style .short .content-part .categorie a {
  color: #505050;
}
.rs-blog.home9-style .short .content-part .categorie a:hover {
  color: #21a7d0;
}
.rs-blog.home9-style .short .content-part .title {
  line-height: 1.5;
}
.rs-blog.home9-style .short .content-part .title a {
  color: #111111;
}
.rs-blog.home9-style .short .content-part .title a:hover {
  color: #21a7d0;
}
.rs-blog.orange-color .blog-item .blog-content .title a:hover {
  color: #ff5421;
}
.rs-blog.orange-color .blog-item .blog-content .readon-arrow:hover {
  color: #ff5421;
}
.rs-blog.orange-color .blog-item .blog-content .readon-arrow:hover:after {
  color: #ff5421;
}
.rs-blog.orange-color .events-short .content-part .title a:hover {
  color: #ff5421;
}
.rs-blog.gym-home .blog-item {
  transition: all 500ms ease;
  border-radius: 5px;
}
.rs-blog.gym-home .blog-item .blog-content {
  border-style: solid;
  border-width: 1px 1px 1px 1px;
  border-color: #252525;
  background-color: #1D0E15;
  padding: 26px 26px 30px 26px;
}
.rs-blog.gym-home .blog-item .blog-content .post-categories li {
  margin-bottom: 10px;
}
.rs-blog.gym-home .blog-item .blog-content .post-categories li a {
  font-size: 15px;
  color: #ff5421;
  text-transform: uppercase;
  margin-bottom: 8px;
}
.rs-blog.gym-home .blog-item .blog-content .title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 18px;
}
.rs-blog.gym-home .blog-item .blog-content .title a {
  color: #ffffff;
}
.rs-blog.gym-home .blog-item .blog-content .title a:hover {
  color: #ff5421;
}
.rs-blog.gym-home .blog-item .blog-content .blog-meta {
  font-size: 14px;
  color: #ffffff;
  display: flex;
  justify-content: space-between;
}
.rs-blog.gym-home .blog-item .blog-content .blog-meta li i {
  padding-right: 3px;
  font-size: 14px;
  color: #ff5421;
}
.rs-blog.gym-home .blog-item .blog-content .blog-meta li i.fa-calendar {
  padding-right: 7px;
}
.rs-blog.gym-home .blog-item:hover {
  transform: translateY(-10px);
}
.rs-inner-blog .blog-item {
  background: #fff;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.07);
}
.rs-inner-blog .blog-item .blog-content {
  padding: 40px 46px 45px;
  overflow: hidden;
}
.rs-inner-blog .blog-item .blog-content .blog-title {
  margin: 4px 0 10px;
  font-size: 26px;
  line-height: 35px;
}
.rs-inner-blog .blog-item .blog-content .blog-title a {
  color: #111111;
}
.rs-inner-blog .blog-item .blog-content .blog-title a:hover {
  color: #21a7d0;
}
.rs-inner-blog .blog-item .blog-content .blog-meta .btm-cate {
  overflow: hidden;
  margin: 0 0 5px;
  padding: 0 0 12px;
  font-size: 13px;
  display: flex;
  align-items: center;
}
.rs-inner-blog .blog-item .blog-content .blog-meta .btm-cate li {
  margin-right: 15px;
}
.rs-inner-blog .blog-item .blog-content .blog-meta .btm-cate li .blog-date {
  display: inline-block;
  font-weight: 400;
  font-size: 13px;
  color: #505050;
}
.rs-inner-blog .blog-item .blog-content .blog-meta .btm-cate li .blog-date i {
  color: #21a7d0;
  margin-right: 3px;
}
.rs-inner-blog .blog-item .blog-content .blog-meta .btm-cate li .author {
  display: inline-block;
  padding: 0;
  margin-right: 0;
  line-height: normal;
  color: #555;
}
.rs-inner-blog .blog-item .blog-content .blog-meta .btm-cate li .author i {
  color: #21a7d0;
  margin-right: 3px;
}
.rs-inner-blog .blog-item .blog-content .blog-meta .btm-cate li .tag-line {
  position: relative;
  padding-left: 2px;
  color: #555;
}
.rs-inner-blog .blog-item .blog-content .blog-meta .btm-cate li .tag-line i {
  margin-right: 3px;
  color: #21a7d0;
}
.rs-inner-blog .blog-item .blog-content .blog-meta .btm-cate li .tag-line i:before {
  font-size: 14px;
  font-weight: 600;
}
.rs-inner-blog .blog-item .blog-content .blog-meta .btm-cate li .tag-line a {
  color: #555;
}
.rs-inner-blog .blog-item .blog-content .blog-desc {
  font-size: 16px;
  line-height: 27px;
  font-weight: 400;
  color: #505050;
  margin-bottom: 35px;
}
.rs-inner-blog .widget-area .widget-title {
  color: #111111;
  font-size: 20px;
  line-height: 26px;
  font-weight: 600;
  position: relative;
  z-index: 1;
  padding-bottom: 12px;
}
.rs-inner-blog .widget-area .widget-title:before {
  content: "";
  position: absolute;
  border: 0;
  width: 50px;
  height: 2px;
  background: #ff5421;
  z-index: 1;
  margin-left: 0;
  bottom: 0;
  left: 0;
}
.rs-inner-blog .widget-area .search-widget {
  background: #fff;
  padding: 40px 30px 40px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.07);
}
.rs-inner-blog .widget-area .search-widget .search-wrap {
  position: relative;
}
.rs-inner-blog .widget-area .search-widget .search-wrap [type=search] {
  border: 1px solid #ddd;
  color: #444444;
  padding: 12px 17px;
  width: 100%;
  border-radius: 5px;
  position: relative;
}
.rs-inner-blog .widget-area .search-widget .search-wrap button {
  background: transparent;
  border: medium none;
  color: #505050;
  padding: 11px 15px 12px;
  position: absolute;
  display: block;
  right: 0px;
  top: 0;
  z-index: 10;
  font-size: 20px;
  border-radius: 0 5px 5px;
}
.rs-inner-blog .widget-area .search-widget .search-wrap button i:before {
  font-weight: 600;
  font-size: 18px;
}
.rs-inner-blog .widget-area .search-widget .search-wrap button:hover {
  color: #21a7d0;
}
.rs-inner-blog .widget-area .recent-posts {
  background: #fff;
  padding: 40px 30px 40px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.07);
}
.rs-inner-blog .widget-area .recent-posts ul li {
  margin-top: 13px;
  padding-top: 13px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  font-weight: 400;
  font-size: 16px;
}
.rs-inner-blog .widget-area .recent-posts ul li a {
  color: #505050;
}
.rs-inner-blog .widget-area .recent-posts ul li a:hover {
  color: #21a7d0;
}
.rs-inner-blog .widget-area .recent-posts-widget {
  background: #fff;
  padding: 40px 30px 40px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.07);
}
.rs-inner-blog .widget-area .recent-posts-widget .show-featured {
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding-bottom: 15px;
  margin-bottom: 15px;
}
.rs-inner-blog .widget-area .recent-posts-widget .show-featured .post-img {
  max-width: 150px;
  padding-right: 15px;
  border-radius: 3px;
  overflow: hidden;
}
.rs-inner-blog .widget-area .recent-posts-widget .show-featured .post-desc a {
  font-size: 15px;
  line-height: 22px;
  color: #111111;
  font-weight: 400;
}
.rs-inner-blog .widget-area .recent-posts-widget .show-featured .post-desc a:hover {
  color: #21a7d0;
}
.rs-inner-blog .widget-area .recent-posts-widget .show-featured .post-desc .date {
  font-size: 12px;
  line-height: 20px;
  color: #21a7d0;
  font-weight: 400;
  display: block;
}
.rs-inner-blog .widget-area .recent-posts-widget .show-featured .post-desc .date i {
  font-size: 12px;
  line-height: 12px;
  color: #21a7d0;
  font-weight: 400;
}
.rs-inner-blog .widget-area .recent-posts-widget .show-featured:last-child {
  margin: 0;
  padding: 0;
  border: none;
}
.rs-inner-blog .widget-area .widget-archives {
  background: #fff;
  padding: 40px 30px 40px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.07);
}
.rs-inner-blog .widget-area .widget-archives ul li {
  border: none;
  position: relative;
  padding: 6px 0px 6px 14px;
  margin: 0;
  font-size: 16px;
}
.rs-inner-blog .widget-area .widget-archives ul li a {
  color: #505050;
}
.rs-inner-blog .widget-area .widget-archives ul li a:after {
  content: "\f105";
  font-family: FontAwesome;
  font-style: normal;
  font-weight: normal;
  text-decoration: inherit;
  position: absolute;
  left: 0;
  top: 6px;
  color: #273c66;
}
.rs-inner-blog .widget-area .widget-archives ul li a:hover {
  color: #ff5421;
}
.rs-inner-blog .blog-deatails {
  background: #fff;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
}
.rs-inner-blog .blog-deatails .blog-full {
  padding: 40px 45px 40px;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta {
  display: flex;
  align-items: center;
  padding: 0 0 30px;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta li .p-date {
  color: #555;
  font-size: 14px;
  margin-right: 8px;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta li .p-date i {
  font-size: 13px;
  margin-right: 3px;
  color: #21a7d0;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta li .p-date i:before {
  margin-right: 5px;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta .Post-cate {
  list-style: none;
  display: inline-block;
  padding-right: 10px;
  color: #555;
  font-size: 14px;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta .Post-cate .tag-line i {
  color: #21a7d0;
  margin-right: 3px;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta .Post-cate .tag-line i:before {
  margin-right: 4px;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta .Post-cate .tag-line a {
  font-weight: 400;
  color: #555;
  font-size: 14px;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta .Post-cate .tag-line a:hover {
  color: #21a7d0;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta .post-comment {
  color: #555;
  font-size: 14px;
}
.rs-inner-blog .blog-deatails .blog-full .single-post-meta .post-comment i:before {
  color: #21a7d0;
}
.rs-inner-blog .blog-deatails .blog-full .blog-desc p {
  font-size: 16px;
  font-weight: 400;
  color: #505050;
}
.rs-inner-blog .blog-deatails .blog-full .title {
  color: #111111;
  font-size: 36px;
  font-weight: 700;
  line-height: 40px;
}
.rs-inner-blog .blog-deatails .blog-full .sm-title {
  font-size: 20px;
  font-weight: 700;
  line-height: 28px;
  color: #111111;
}
.rs-inner-blog .blog-deatails .blog-full .unorder-list li {
  display: block;
  position: relative;
  padding-left: 25px;
  padding-bottom: 15px;
  font-weight: 500;
  line-height: 25px;
}
.rs-inner-blog .blog-deatails .blog-full .unorder-list li:before {
  position: absolute;
  top: 0;
  left: 0;
  content: "\f105";
  font-family: FontAwesome;
  color: #21a7d0;
  font-size: 20px;
}
.rs-inner-blog .ps-navigation {
  border-top: 1px solid rgba(170, 170, 170, 0.25);
  border-bottom: 1px solid rgba(170, 170, 170, 0.25);
  padding: 30px 0;
  margin: 60px 0 0;
}
.rs-inner-blog .ps-navigation ul li {
  text-align: right;
}
.rs-inner-blog .ps-navigation ul li a {
  display: inline-block;
}
.rs-inner-blog .ps-navigation ul li a .next-link {
  text-transform: uppercase;
  color: #909090;
  margin-top: 0;
  display: block;
}
.rs-inner-blog .ps-navigation ul li a .next-link:hover {
  color: #21a7d0;
}
.rs-inner-blog .ps-navigation ul li a .next-link i:before {
  font-size: 16px;
  font-style: normal;
  color: #505050;
}
.rs-inner-blog .ps-navigation ul li a .link-text {
  font-size: 16px;
  font-weight: 500;
  color: #1c1b1b;
}
.rs-inner-blog .ps-navigation ul li a .link-text:hover {
  color: #21a7d0;
}
.rs-inner-blog .comment-area .comment-full .reply-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 30px;
  margin-top: 45px;
  position: relative;
  padding: 0 0 12px;
  color: #111111;
}
.rs-inner-blog .comment-area .comment-full p span {
  font-size: 16px;
  color: #505050;
}
.rs-inner-blog .comment-area .comment-full .from-group {
  width: 100%;
  height: 50px;
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
}
.rs-inner-blog .comment-area .comment-full .submit-btn .submit {
  border: 2px solid;
  border-color: #21a7d0;
  background: #21a7d0;
  position: relative;
  display: inline-block;
  padding: 12px 22px;
  border-radius: 3px;
  line-height: normal;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  transition: all .3s ease 0s;
  text-transform: uppercase;
}
.rs-inner-blog .comment-area .comment-full .submit-btn .submit:hover {
  opacity: 0.9;
}
.rs-inner-blog.orange-color .blog-btn:hover,
.rs-inner-blog.orange-color .blog-btn:hover::after,
.rs-inner-blog.orange-color .widget-area .recent-posts ul li a:hover,
.rs-inner-blog.orange-color .blog-item .blog-content .blog-title a:hover,
.rs-inner-blog.orange-color .widget-area .search-widget .search-wrap button:hover,
.rs-inner-blog.orange-color .blog-item .blog-content .blog-meta .btm-cate li .author i,
.rs-inner-blog.orange-color .blog-item .blog-content .blog-meta .btm-cate li .tag-line i,
.rs-inner-blog.orange-color .blog-item .blog-content .blog-meta .btm-cate li .blog-date i,
.rs-inner-blog.orange-color .widget-area .recent-posts-widget .show-featured .post-desc .date,
.rs-inner-blog.orange-color .widget-area .recent-posts-widget .show-featured .post-desc .date i,
.rs-inner-blog.orange-color .widget-area .recent-posts-widget .show-featured .post-desc a:hover,
.rs-inner-blog.orange-color .blog-deatails .blog-full .single-post-meta .Post-cate .tag-line i,
.rs-inner-blog.orange-color .blog-deatails .blog-full .single-post-meta .post-comment i::before,
.rs-inner-blog.orange-color .blog-deatails .blog-full .single-post-meta .Post-cate .tag-line a:hover,
.rs-inner-blog.orange-color .blog-deatails .blog-full .single-post-meta li .p-date i,
.rs-inner-blog.orange-color .blog-deatails .blog-full .unorder-list li::before,
.rs-inner-blog.orange-color .ps-navigation ul li a .link-text:hover,
.rs-inner-blog.orange-color .ps-navigation ul li a .next-link:hover,
.rs-inner-blog.orange-color .ps-navigation ul li a .next-link:hover i::before {
  color: #ff5421;
}
.rs-inner-blog.orange-color blockquote::before {
  background: #ff5421;
}
.rs-inner-blog.orange-color .comment-area .comment-full .submit-btn .submit {
  border-color: #ff5421;
  background: #ff5421;
}
.rs-inner-blog.orange-color .comment-area .comment-full .submit-btn .submit:hover {
  border-color: #ed3600;
  background: #ed3600;
}
/* ------------------------------------
    19. Latest Events Section CSS
---------------------------------------*/
.rs-event .event-item {
  transition: all 0.3s ease;
  position: relative;
}
.rs-event .event-item .event-short {
  text-align: left;
  box-shadow: 0 0 20px #eee;
}
.rs-event .event-item .event-short .featured-img {
  position: relative;
}
.rs-event .event-item .event-short .featured-img img {
  border-radius: 5px 5px 0 0;
}
.rs-event .event-item .event-short .categorie {
  position: absolute;
  top: 30px;
  left: 30px;
  z-index: 1;
}
.rs-event .event-item .event-short .categorie a {
  background: #ff5421;
  padding: 5px 20px;
  display: inline-block;
  color: #ffffff;
  border-radius: 4px;
}
.rs-event .event-item .event-short .categorie a:hover {
  opacity: .9;
}
.rs-event .event-item .event-short .content-part {
  padding: 25px 30px 15px 30px;
  border-radius: 0 0 5px 5px;
}
.rs-event .event-item .event-short .content-part .address {
  padding-bottom: 15px;
  color: #363636;
  font-size: 14px;
}
.rs-event .event-item .event-short .content-part .address i {
  font-size: 13px;
  margin-right: 4px;
}
.rs-event .event-item .event-short .content-part .title {
  font-size: 26px;
  line-height: 32px;
  transition: all .8s ease;
}
.rs-event .event-item .event-short .content-part .title a {
  color: #000000;
}
.rs-event .event-item .event-short .content-part .title a:hover {
  color: #ff5421;
}
.rs-event .event-item .event-short .content-part .text {
  color: #363636;
  margin: 0;
  font-size: 14px;
}
.rs-event .event-item .event-short .content-part .event-btm {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #efefef;
  margin-top: 34px;
  padding-top: 26px;
}
.rs-event .event-item .event-short .content-part .event-btm .date-part {
  font-size: 13px;
}
.rs-event .event-item .event-short .content-part .event-btm .date-part .date i {
  font-size: 13px;
  margin-right: 7px;
}
.rs-event .event-item .event-short .content-part .event-btm .btn-part a {
  background: #ff5421;
  text-transform: capitalize;
  padding: 7px 15px;
  color: #ffffff;
  border-radius: 2px;
  font-size: 14px;
  display: inline-block;
}
.rs-event .event-item .event-short .content-part .event-btm .btn-part a:hover {
  background: #ed3600;
}
.rs-event .event-item:hover {
  transform: translateY(-10px);
}
.rs-event .event-item.home12-style .event-short {
  text-align: left;
  background: #ffffff;
}
.rs-event .event-item.home12-style .event-short .featured-img {
  position: relative;
}
.rs-event .event-item.home12-style .event-short .featured-img img {
  border-radius: 5px 5px 0 0;
}
.rs-event .event-item.home12-style .event-short .content-part {
  padding: 25px 30px 30px 30px;
  border-radius: 0 0 5px 5px;
}
.rs-event .event-item.home12-style .event-short .content-part .all-dates-time {
  display: flex;
}
.rs-event .event-item.home12-style .event-short .content-part .all-dates-time .address {
  color: #363636;
  padding-right: 20px;
}
.rs-event .event-item.home12-style .event-short .content-part .all-dates-time .address i:before {
  color: #0c8b51;
}
.rs-event .event-item.home12-style .event-short .content-part .all-dates-time .time {
  color: #363636;
  font-size: 14px;
}
.rs-event .event-item.home12-style .event-short .content-part .all-dates-time .time i:before {
  color: #0c8b51;
  padding-right: 5px;
}
.rs-event .event-item.home12-style .event-short .content-part .title {
  font-size: 22px;
  line-height: 32px;
  transition: all .8s ease;
}
.rs-event .event-item.home12-style .event-short .content-part .title a {
  color: #000000;
}
.rs-event .event-item.home12-style .event-short .content-part .title a:hover {
  color: #0c8b51;
}
.rs-event .event-item.home12-style .event-short .content-part .event-btm {
  border-top: 1px solid #efefef;
}
.rs-event .event-item.home12-style .event-short .content-part .event-btm .date-part {
  font-size: 13px;
  color: #363636;
}
.rs-event .event-item.home12-style .event-short .content-part .event-btm .date-part .date i {
  font-size: 13px;
  margin-right: 7px;
  color: #0c8b51;
}
.rs-event .event-item.home12-style .event-short .content-part .event-btm .btn-part a {
  background: #0c8b51;
}
.rs-event .event-item.home12-style .event-short .content-part .event-btm .btn-part a:hover {
  background: #085c36;
}
.rs-event .event-item.home12-style .rs-carousel .owl-dots {
  text-align: center;
}
.rs-event .event-item.home12-style .rs-carousel .owl-dots .owl-dot {
  width: 40px;
  height: 10px;
  display: inline-block;
  margin: 0 6px;
  padding: 3px 0;
  border-radius: 30px;
  background: red;
  border: none;
}
.rs-event .event-item.home12-style .rs-carousel .owl-dots .owl-dot:hover {
  width: 40px;
  opacity: 1;
}
.rs-event .event-item.home12-style .rs-carousel .owl-dots .active {
  background: red;
}
.rs-event.home12style {
  background: url(assets/images/bg/home12/event-bg.jpg);
  background-repeat: no-repeat;
  background-position: center top;
  background-size: cover;
  padding: 150px 0;
}
.rs-event.home12style .owl-dots {
  text-align: center;
  margin-top: 47px;
}
.rs-event.home12style .owl-dots .owl-dot {
  width: 25px;
  height: 8px;
  display: inline-block;
  margin: 0 6px;
  padding: 3px 0;
  border-radius: 30px;
  background: #085c36;
  border: none;
  transition: all 0.3s ease;
}
.rs-event.home12style .owl-dots .owl-dot:hover,
.rs-event.home12style .owl-dots .owl-dot.active {
  width: 40px;
  background: #0c8b51;
  opacity: 0.5;
}
.rs-event.modify1 .event-item .event-short .featured-img .dates {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  background: #21a7d0;
  padding: 7px 20px;
  display: inline-block;
  color: #fff;
  border-radius: 15px 15px 0 0;
  right: 0;
  margin: 0 auto;
  max-width: 220px;
  text-align: center;
}
.rs-event.modify1 .event-item .event-short .content-part {
  padding: 30px 30px 35px 30px;
  text-align: center;
}
.rs-event.modify1 .event-item .event-short .content-part .title {
  font-size: 24px;
}
.rs-event.modify1 .event-item .event-short .content-part .time-sec {
  display: inline-flex;
}
.rs-event.modify1 .event-item .event-short .content-part .time-sec .timesec {
  margin-right: 15px;
  font-size: 14px;
}
.rs-event.modify1 .event-item .event-short .content-part .time-sec .timesec i:before {
  font-size: 16px;
}
.rs-event.modify1 .event-item .event-short .content-part .time-sec .address {
  color: #363636;
  font-size: 14px;
}
.rs-event.modify1 .event-item .event-short .content-part .time-sec .address i {
  font-size: 13px;
  margin-right: 4px;
}
.rs-event.modify2 .event-item .event-short {
  transition: all 500ms ease;
  box-shadow: 0 0 20px #eee;
  position: relative;
}
.rs-event.modify2 .event-item .event-short .featured-img {
  position: relative;
  padding: 0px 0px 0px 0px;
}
.rs-event.modify2 .event-item .event-short .featured-img:before {
  opacity: .85;
  background: 0 0;
  background: linear-gradient(transparent, #333333), linear-gradient(transparent, #333333);
  background: -moz-linear-gradient(transparent, #333333), -moz-linear-gradient(transparent, #333333);
  background: -o-linear-gradient(transparent, #333333), -o-linear-gradient(transparent, #333333);
  content: "";
  height: 100%;
  bottom: 0;
  left: 0px;
  right: 0px;
  position: absolute;
  z-index: 1;
  transition: all .7s ease 0s;
}
.rs-event.modify2 .event-item .event-short .content-part {
  padding: 40px 40px 40px 40px;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  border-radius: 0 0 5px 5px;
}
.rs-event.modify2 .event-item .event-short .content-part .title a {
  color: #ffffff;
}
.rs-event.modify2 .event-item .event-short .content-part .address {
  color: #ffffff;
  font-size: 14px;
}
.rs-event.modify2 .event-item .event-short .content-part .address i {
  font-size: 13px;
  margin-right: 4px;
}
.rs-event.modify2 .event-item .event-short .content-part .date-part {
  padding: 0px 0px 10px 0px;
}
.rs-event.modify2 .event-item .event-short .content-part .date-part .date {
  color: #ffffff;
  font-size: 13px;
}
.rs-event.modify2 .event-item .event-short .content-part .date-part .date i {
  font-size: 13px;
  margin-right: 7px;
}
.rs-event.modify2 .event-item .event-short .content-part .time {
  color: #ffffff;
  font-size: 14px;
}
.rs-event.modify2 .event-item .event-short .content-part .time i:before {
  margin: 0;
  font-size: 14px;
  color: #fff;
  margin-right: 4px;
}
.rs-event.home8-style1 .rs-carousel .owl-dots {
  text-align: center;
}
.rs-event.home8-style1 .rs-carousel .owl-dots .owl-dot {
  width: 40px;
  height: 10px;
  display: inline-block;
  margin: 0 6px;
  padding: 3px 0;
  border-radius: 30px;
  background: #ff5421;
  border: none;
}
.rs-event.home8-style1 .rs-carousel .owl-dots .owl-dot:hover {
  width: 40px;
  opacity: 1;
}
.rs-event.home8-style1 .rs-carousel .owl-dots .active {
  background: #ff5421;
}
.rs-event.home8-style1 .owl-stage-outer {
  padding-bottom: 10px;
}
.rs-event.home8-style1 .event-item .event-short .featured-img .dates {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  background: #ff5421;
  padding: 7px 20px;
  display: inline-block;
  color: #fff;
  border-radius: 15px 15px 0 0;
  right: 0;
  margin: 0 auto;
  max-width: 220px;
  text-align: center;
}
.rs-event.home8-style1 .event-item .event-short .content-part {
  padding: 30px 30px 35px 30px;
  text-align: center;
}
.rs-event.home8-style1 .event-item .event-short .content-part .title {
  font-size: 24px;
}
.rs-event.home8-style1 .event-item .event-short .content-part .time-sec {
  display: inline-flex;
}
.rs-event.home8-style1 .event-item .event-short .content-part .time-sec .timesec {
  padding-right: 4px;
}
.rs-event.home8-style1 .event-item .event-short .content-part .time-sec .timesec i:before {
  color: #ff5421;
  font-size: 14px;
}
.rs-event.home8-style1 .event-item .event-short .content-part .time-sec .address {
  font-size: 14px;
}
.rs-event.home8-style1 .event-item .event-short .content-part .time-sec .address i {
  font-size: 13px;
  margin-right: 5px;
}
.rs-event.home8-style1 .event-item .event-short .content-part .time-sec .address i:before {
  color: #ff5421;
}
.rs-event.orange-color.modify1 .event-item .event-short .featured-img .dates {
  background: #ff5421;
}
.rs-latest-events.style1 .single-img {
  z-index: 1;
  position: relative;
}
.rs-latest-events.style1 .single-img img {
  border-radius: 3px;
}
.rs-latest-events.style1 .event-wrap {
  z-index: 1;
  position: relative;
}
.rs-latest-events.style1 .event-wrap .events-short {
  overflow: hidden;
  background: #ffffff;
  padding: 31px 30px;
  border-radius: 3px;
}
.rs-latest-events.style1 .event-wrap .events-short .date-part {
  width: 100px;
  float: left;
  text-align: center;
  padding: 11px 0 22px;
  border-radius: 3px;
  margin-right: 25px;
}
.rs-latest-events.style1 .event-wrap .events-short .date-part .month {
  margin-bottom: 10px;
  display: block;
}
.rs-latest-events.style1 .event-wrap .events-short .date-part .date {
  font-size: 50px;
  font-family: 'Nunito', sans-serif;
  color: #112958;
  font-weight: 700;
}
.rs-latest-events.style1 .event-wrap .events-short .date-part.bgc1 {
  background: #dcf1fd;
}
.rs-latest-events.style1 .event-wrap .events-short .date-part.bgc2 {
  background: #e9fbd5;
}
.rs-latest-events.style1 .event-wrap .events-short .date-part.bgc3 {
  background: #fee2c0;
}
.rs-latest-events.style1 .event-wrap .events-short .content-part .categorie a {
  color: #505050;
}
.rs-latest-events.style1 .event-wrap .events-short .content-part .categorie a:hover {
  color: #21a7d0;
}
.rs-latest-events.style1 .event-wrap .events-short .content-part .title {
  line-height: 1.5;
}
.rs-latest-events.style1 .event-wrap .events-short .content-part .title a {
  color: #112958;
}
.rs-latest-events.style1 .event-wrap .events-short .content-part .title a:hover {
  color: #21a7d0;
}
.rs-latest-events.style1 .event-wrap .btn-part a {
  text-transform: uppercase;
  font-weight: 500;
  font-size: 16px;
  color: #112958;
  position: relative;
  display: inline-block;
}
.rs-latest-events.style1 .event-wrap .btn-part a:after {
  position: absolute;
  content: '';
  right: 0;
  bottom: 3px;
  width: 0;
  height: 1px;
  background: #112958;
  transition: all 0.3s ease;
}
.rs-latest-events.style1 .event-wrap .btn-part a:hover:after {
  left: 0;
  right: auto;
  width: 100%;
}
.rs-latest-events.bg-wrap {
  position: relative;
}
.rs-latest-events.bg-wrap:after {
  position: absolute;
  content: '';
  top: 0;
  right: 0;
  width: 63%;
  height: 100%;
  background: #f3f8f9;
}
/* ------------------------------------
    19. Download Section CSS
---------------------------------------*/
.rs-download-app .mobile-img {
  display: flex;
}
.rs-download-app .mobile-img .apps-image img {
  max-width: 195px;
}
/* ------------------------------------
    20. Partner Section CSS
---------------------------------------*/
.rs-partner .partner-item a img {
  max-width: 163px;
  margin: 0 auto;
}
.rs-partner.style2 .partner-item a img {
  max-width: 100px;
  margin: 0 auto;
}
/* ------------------------------------
    21. Gallery Section CSS
---------------------------------------*/
.rs-gallery .gallery-item {
  padding: 0 30px 30px 0;
}
.rs-gallery .gallery-item .gallery-img a img {
  border-radius: 5px;
}
.rs-gallery .gallery-item .title {
  padding-top: 25px;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  line-height: 30px;
  color: #111111;
}
.rs-gallery.style2 .gallery-part {
  position: relative;
  overflow: hidden;
}
.rs-gallery.style2 .gallery-part .gallery-img {
  position: relative;
  overflow: hidden;
}
.rs-gallery.style2 .gallery-part .gallery-img a img {
  transform: scale(1);
  transition: all .8s ease;
  height: 220px;
}
.rs-gallery.style2 .gallery-part .gallery-img a:before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: #171f32;
  opacity: 0;
  display: block;
  transition: all .8s ease;
  z-index: 1;
}
.rs-gallery.style2 .gallery-part:hover .gallery-img a img {
  transform: scale(1.1);
}
.rs-gallery.style2 .gallery-part:hover .gallery-img a:before {
  opacity: .4;
}
.rs-gallery.home11-style .gallery-part .gallery-img {
  position: relative;
}
.rs-gallery.home11-style .gallery-part .gallery-img img {
  border-radius: 30px;
}
.rs-gallery.home11-style .gallery-part .gallery-img .content-part {
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  background: #4e49a1;
  padding: 5px 20px;
  display: inline-block;
  color: #fff;
  border-radius: 30px;
  right: 0;
  transition: all .4s ease;
}
.rs-gallery.home11-style .gallery-part .gallery-img .content-part .title {
  font-size: 22px;
  line-height: 34px;
  font-weight: 700;
  color: #ffffff;
  padding: 15px 0 15px;
  margin: 0;
}
.rs-gallery.home11-style .gallery-part .gallery-img .gallery-info {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all .4s ease;
  z-index: 1;
  text-align: center;
  width: 100%;
}
.rs-gallery.home11-style .gallery-part .gallery-img .gallery-info .title-part {
  font-size: 22px;
  line-height: 34px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 12px;
}
.rs-gallery.home11-style .gallery-part .gallery-img .gallery-info p {
  font-size: 16px;
  line-height: 28px;
  font-weight: 400;
  color: #ffffff;
  padding-right: 60px;
  padding-left: 60px;
  margin-bottom: 15px;
}
.rs-gallery.home11-style .gallery-part .gallery-img .gallery-info .btn-part a {
  font-size: 16px;
  line-height: 28px;
  font-weight: 400;
  color: #ffffff;
}
.rs-gallery.home11-style .gallery-part .gallery-img .gallery-info .btn-part a i {
  padding-left: 6px;
}
.rs-gallery.home11-style .gallery-part .gallery-img .gallery-info .btn-part a i:before {
  font-size: 16px;
}
.rs-gallery.home11-style .gallery-part .gallery-img:before {
  content: '';
  background: #4e49a1;
  position: absolute;
  visibility: hidden;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  border-radius: 30px;
  opacity: 0;
  display: block;
  transition: all .4s ease;
  z-index: 1;
}
.rs-gallery.home11-style .gallery-part:hover .content-part {
  visibility: hidden;
  opacity: 0;
}
.rs-gallery.home11-style .gallery-part:hover .gallery-img .gallery-info {
  opacity: 1;
  visibility: visible;
}
.rs-gallery.home11-style .gallery-part:hover .gallery-img .gallery-info .title-part {
  color: #ffffff;
}
.rs-gallery.home11-style .gallery-part:hover .gallery-img .gallery-info p {
  color: #ffffff;
}
.rs-gallery.home11-style .gallery-part:hover .gallery-img .gallery-info .btn-part a {
  color: #ffffff;
}
.rs-gallery.home11-style .gallery-part:hover .gallery-img:before {
  visibility: visible;
  opacity: 0.9;
}
/* ------------------------------------
    22. CTA Section CSS
---------------------------------------*/
.rs-cta {
  position: relative;
}
.rs-cta .cta-img img {
  min-height: 425px;
  width: 100%;
}
.rs-cta .cta-content {
  position: absolute;
  width: 100%;
  max-width: 600px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.rs-cta.style1 {
  background: url(assets/images/bg/pattern1.png);
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: bottom;
}
.rs-cta.style1 .partition-bg-wrap {
  position: relative;
}
.rs-cta.style1 .partition-bg-wrap:after,
.rs-cta.style1 .partition-bg-wrap:before {
  position: absolute;
  content: '';
  width: 50%;
  height: 100%;
  bottom: 0;
  z-index: -1;
}
.rs-cta.style1 .partition-bg-wrap:after {
  background: #21a7d0;
  left: 0;
}
.rs-cta.style1 .partition-bg-wrap:before {
  background: #273c66;
  right: 0;
}
.rs-cta.style2 .video-wrap .popup-videos {
  color: #ffffff;
  display: block;
  overflow: hidden;
  max-width: 270px;
}
.rs-cta.style2 .video-wrap .popup-videos i {
  width: 70px;
  height: 70px;
  line-height: 61px;
  border: 5px solid #ffffff;
  border-radius: 50%;
  text-align: center;
  font-size: 25px;
  float: left;
  margin-right: 20px;
}
.rs-cta.style2 .video-wrap .popup-videos i:before {
  padding-left: 5px;
}
.rs-cta.style2 .video-wrap .popup-videos .title {
  color: #ffffff;
}
.rs-cta.style2 .video-wrap .popup-videos:hover {
  opacity: 0.7;
}
.rs-cta.style2 .partition-bg-wrap {
  position: relative;
}
.rs-cta.style2 .partition-bg-wrap:after,
.rs-cta.style2 .partition-bg-wrap:before {
  position: absolute;
  content: '';
  width: 50%;
  height: 100%;
  top: 0;
  bottom: 0;
  z-index: -1;
}
.rs-cta.style2 .partition-bg-wrap:before {
  background: url(assets/images/cta/style2/left-bg.jpg);
  left: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.rs-cta.style2 .partition-bg-wrap:after {
  background: url(assets/images/cta/style2/right-bg.jpg);
  right: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: left;
}
.rs-cta.style2 .partition-bg-wrap.inner-page:before {
  background: url(assets/images/cta/style2/left-bg2.png);
  background-size: cover;
}
.rs-cta.style2 .partition-bg-wrap.inner-page:after {
  background: url(assets/images/cta/style2/right-bg2.png);
  background-size: cover;
}
.rs-cta.style3 {
  background: url(assets/images/bg/pattern1.png);
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: bottom;
}
.rs-cta.style3 .partition-bg-wrap {
  position: relative;
}
.rs-cta.style3 .partition-bg-wrap:after,
.rs-cta.style3 .partition-bg-wrap:before {
  position: absolute;
  content: '';
  width: 50%;
  height: 100%;
  bottom: 0;
  z-index: -1;
}
.rs-cta.style3 .partition-bg-wrap:after {
  background: #21a7d0;
  left: 0;
}
.rs-cta.style3 .partition-bg-wrap:before {
  background: #273c66;
  right: 0;
}
.rs-cta.style7 .partition-bg-wrap {
  background: none !important;
  position: relative;
}
.rs-cta.style7 .partition-bg-wrap:after,
.rs-cta.style7 .partition-bg-wrap:before {
  position: absolute;
  content: '';
  width: 50%;
  height: 100%;
  bottom: 0;
  z-index: -1;
}
.rs-cta.style7 .partition-bg-wrap:after {
  background: url(assets/images/cta/home7/1.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.rs-cta.style7 .partition-bg-wrap:before {
  background: url(assets/images/cta/home7/2.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  right: 0;
}
.rs-cta.main-home .partition-bg-wrap {
  background: none !important;
  position: relative;
  padding: 130px 0;
}
.rs-cta.main-home .partition-bg-wrap:after,
.rs-cta.main-home .partition-bg-wrap:before {
  position: absolute;
  content: '';
  width: 50%;
  height: 100%;
  bottom: 0;
  z-index: -1;
}
.rs-cta.main-home .partition-bg-wrap:after {
  background: url(assets/images/cta/home1.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.rs-cta.main-home .partition-bg-wrap:before {
  background: url(assets/images/cta/main-home.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  right: 0;
}
.rs-cta.home9-style .partition-bg-wrap {
  background: none !important;
  position: relative;
  padding: 100px 0;
}
.rs-cta.home9-style .partition-bg-wrap:after,
.rs-cta.home9-style .partition-bg-wrap:before {
  position: absolute;
  content: '';
  width: 50%;
  height: 100%;
  bottom: 0;
  z-index: -1;
}
.rs-cta.home9-style .partition-bg-wrap:after {
  background: url(assets/images/cta/covid-19.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.rs-cta.home9-style .partition-bg-wrap:before {
  background: #1c335f;
  background-size: cover;
  background-repeat: no-repeat;
  right: 0;
}
.rs-cta.section-wrap .content-wrap {
  padding: 100px 100px 100px 105px;
  background: #273c66;
}
.rs-cta.section-wrap .content-wrap .text-part {
  padding-right: 400px;
}
.rs-cta.section-wrap .content-wrap .text-part .sub-title {
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;
  color: #ffffff;
  text-transform: uppercase;
  margin-bottom: 15px;
}
.rs-cta.section-wrap .content-wrap .text-part .title {
  font-size: 36px;
  line-height: 46px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 25px;
}
.rs-cta.section-wrap .content-wrap .text-part .desc {
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: #ffffff;
  margin-bottom: 40px;
}
.rs-cta.section-wrap .content {
  max-width: 700px;
  padding-left: 90px;
  padding-right: 15px;
}
.rs-cta.home11-style {
  background: url(assets/images/cta/home11.jpg);
  background-repeat: no-repeat;
  background-position: bottom;
}
.rs-cta.home-style14 {
  max-width: 700px;
  margin: 0 auto;
  background: url(assets/images/bg/home14/cta.jpg);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  padding: 44px 30px 44px 30px;
  border-radius: 5px 5px 5px 5px;
}
.rs-cta.home-style14 .content-part .sub-text {
  font-family: "Nunito", Sans-serif;
  font-size: 15px;
  font-weight: 700;
  text-transform: uppercase;
  color: #FF5421;
  display: block;
  margin-bottom: 10px;
}
.rs-cta.home-style14 .content-part .title {
  font-family: "Nunito", Sans-serif;
  font-size: 22px;
  font-weight: 800;
  letter-spacing: 1px;
  color: #101010;
  margin: 0px 0px 0px 0px;
}
.rs-cta.effects-layer .effects-bg .content-part {
  padding: 56px 244px 60px 50px;
}
.rs-cta.effects-layer .effects-bg .content-part .title {
  font-size: 24px;
  line-height: 40px;
  font-weight: 800;
  color: #101010;
  margin-bottom: 13px;
}
.rs-cta.effects-layer .effects-bg.apply-bg {
  background-color: #F9F7F8;
  background-image: url(assets/images/cta/style3/1-2.png);
  background-position: bottom right;
  background-repeat: no-repeat;
  border-radius: 4px 4px 4px 4px;
  margin-top: 20px;
}
.rs-cta.effects-layer .effects-bg.enroll-bg {
  background-color: #F9F7F8;
  background-image: url(assets/images/cta/style3/2.png);
  background-position: bottom right;
  background-repeat: no-repeat;
  border-radius: 4px 4px 4px 4px;
  margin-top: 20px;
}
/* ------------------------------------
    23. Counter Section CSS
---------------------------------------*/
.rs-counter .counter-item .rs-count {
  position: relative;
  display: inline-block;
  font-size: 42px;
  line-height: 1;
  color: #112958;
  padding-right: 25px;
  margin-bottom: 15px;
}
.rs-counter .counter-item .rs-count:before {
  content: '';
  position: absolute;
  right: 0;
}
.rs-counter .counter-item .rs-count.plus:before {
  content: '+';
}
.rs-counter .counter-item .rs-count.kplus:before {
  content: 'k+';
  right: -25px;
}
.rs-counter .counter-item .rs-count.percent:before {
  content: '%';
  right: -15px;
}
.rs-counter .counter-item .title {
  font-size: 22px;
  color: #505050;
}
.rs-counter .counter-item .counter-text {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  text-align: center;
}
.rs-counter.style2-about .counter-item .rs-count {
  color: #111111;
}
.rs-counter.style2-about .counter-item .title {
  font-size: 22px;
  color: #505050;
  font-family: 'DroidArabicKufiRegular';
  font-weight: normal;
  font-style: normal;
}
.rs-counter.style7 .counter-item .rs-count.purple-color {
  color: #c701f1;
}
.rs-counter.style7 .counter-item .rs-count.blue-color {
  color: #065ce8;
}
.rs-counter.style7 .counter-item .rs-count.pink-color {
  color: #f6075b;
}
.rs-counter.style7 .counter-item .rs-count.orange-color {
  color: #ffbc00;
}
.rs-counter.style7 .counter-item .title {
  color: #54647b;
}
.rs-counter.style-home8 .counter-item {
  padding: 40px 10px 32px 10px;
  background-color: #F9F8F8;
  border-radius: 4px 4px 4px 4px;
}
.rs-counter.style-home8 .counter-item .rs-count {
  color: #ff5421;
}
.rs-counter.style-home8 .counter-item .title {
  color: #031a3d;
}
.rs-counter.home12-style .counter-item .rs-count {
  color: #0c8b51;
  font-size: 42px;
  font-weight: 900;
  line-height: 51px;
  font-family: "Nunito", Sans-serif;
}
.rs-counter.home12-style .counter-item .prefix {
  color: #0c8b51;
  font-size: 42px;
  font-weight: 900;
  line-height: 51px;
  font-family: "Nunito", Sans-serif;
}
.rs-counter.home12-style .counter-item .title {
  color: #505050;
}
.rs-counter.home12-style .counter-item .title:hover {
  color: #0c8b51;
}
.rs-counter.home13-style .counter-item {
  transition: all 500ms ease;
}
.rs-counter.home13-style .counter-item .rs-count {
  color: #ff5421;
  font-size: 42px;
  font-weight: 700;
  line-height: 42px;
  margin-bottom: 5px;
}
.rs-counter.home13-style .counter-item .title {
  font-size: 22px;
  font-weight: 500;
  line-height: 37px;
  color: #ffffff;
}
.rs-counter.home13-style .counter-item:hover {
  transform: translateY(-5px);
}
/* ------------------------------------
    24. Newsletter Section CSS
---------------------------------------*/
.rs-newsletter.style1 .newsletter-wrap {
  background: url(assets/images/bg/newsletter-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  border-radius: 3px;
  padding: 60px 70px;
  position: relative;
}
.rs-newsletter.style1 .newsletter-wrap .content-part .sec-title {
  display: flex;
  align-items: center;
}
.rs-newsletter.style1 .newsletter-wrap .content-part .sec-title .title-icon img {
  max-width: 50px;
  margin-right: 20px;
}
.rs-newsletter.style1 .newsletter-wrap .newsletter-form {
  position: relative;
  border-radius: 3px;
  overflow: hidden;
}
.rs-newsletter.style1 .newsletter-wrap .newsletter-form input,
.rs-newsletter.style1 .newsletter-wrap .newsletter-form button {
  outline: none;
  border: none;
  background: transparent; 
}
.rs-newsletter.style1 .newsletter-wrap .newsletter-form input {
  background: #ffffff;
  width: 100%;
  color: #505050;
  padding: 17px 30px;
  padding-right: 150px;
}
.rs-newsletter.style1 .newsletter-wrap .newsletter-form button {
  position: absolute;
  right: 0;
  top: 0;
  background: black;
  color: #ffffff;
  padding: 17px 40px;
  transition: all 0.3s ease;
}
.rs-newsletter.style1 .newsletter-wrap .newsletter-form button:hover {
  background: #2e4778;
}
.rs-newsletter.style2 {
  background: url(assets/images/bg/newsletter-bg2.jpg);
  background-size: cover;
  background-repeat: no-repeat;
}
.rs-newsletter.style2 .sec-title .title {
  margin-bottom: 10px;
}
.rs-newsletter.style2 .sec-title .sub-title {
  margin: 0;
  text-transform: capitalize;
}
.rs-newsletter.style2 .newsletter-wrap {
  border-radius: 3px;
  padding: 60px 70px;
  padding-left: 0;
  position: relative;
}
.rs-newsletter.style2 .newsletter-wrap .newsletter-form {
  position: relative;
  border-radius: 3px;
  overflow: hidden;
  max-width: 500px;
}
.rs-newsletter.style2 .newsletter-wrap .newsletter-form input,
.rs-newsletter.style2 .newsletter-wrap .newsletter-form button {
  outline: none;
  border: none;
}
.rs-newsletter.style2 .newsletter-wrap .newsletter-form input {
  background: #ffffff;
  width: 100%;
  color: #505050;
  padding: 17px 30px;
  padding-right: 150px;
}
.rs-newsletter.style2 .newsletter-wrap .newsletter-form button {
  position: absolute;
  right: 0;
  top: 0;
  background: #21a7d0;
  color: #ffffff;
  padding: 17px 40px;
  transition: all 0.3s ease;
}
.rs-newsletter.style2 .newsletter-wrap .newsletter-form button:hover {
  background: #2db4de;
}
.rs-newsletter.style2.home11-style {
  background: url(assets/images/bg/home11-news.jpg);
  background-size: cover;
  background-repeat: no-repeat;
}
.rs-newsletter.style2.home11-style .sec-title2 .title {
  margin-bottom: 10px;
}
.rs-newsletter.style2.home11-style .sec-title2 .sub-title {
  margin: 0;
  text-transform: capitalize;
}
.rs-newsletter.style2.home11-style .newsletter-wrap {
  padding: unset;
}
.rs-newsletter.style2.home11-style .newsletter-wrap .newsletter-form input,
.rs-newsletter.style2.home11-style .newsletter-wrap .newsletter-form button {
  outline: none;
  border: none;
}
.rs-newsletter.style2.home11-style .newsletter-wrap .newsletter-form button {
  position: absolute;
  right: 5px;
  top: 5px;
  background: #4e49a1;
  color: #ffffff;
  padding: 12px 40px 12px;
  border-radius: 5px;
}
.rs-newsletter.style2.home11-style .newsletter-wrap .newsletter-form button:hover {
  background: #625eaa;
}
.rs-newsletter.style6 .newsletter-wrap .content-part .title {
  font-size: 42px;
  line-height: 46px;
  font-weight: 600;
  color: #031a3d;
}
.rs-newsletter.style6 .newsletter-wrap .content-part .sub-title {
  font-weight: 400;
  line-height: 30px;
  font-size: 20px;
  color: #54647b;
}
.rs-newsletter.style6 .newsletter-wrap .newsletter-form {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  max-width: 600px;
  border-radius: 4px;
  overflow: hidden;
}
.rs-newsletter.style6 .newsletter-wrap .newsletter-form input,
.rs-newsletter.style6 .newsletter-wrap .newsletter-form button {
  outline: none;
  border: none;
}
.rs-newsletter.style6 .newsletter-wrap .newsletter-form input {
  background: #ffffff;
  color: #505050;
  padding: 18px 40px;
  padding-right: 150px;
}
.rs-newsletter.style6 .newsletter-wrap .newsletter-form button {
  position: absolute;
  right: 0;
  top: 0;
  background: #21a7d0;
  color: #ffffff;
  padding: 17px 40px;
  transition: all 0.3s ease;
  border-radius: 0 0 5px 5px;
}
.rs-newsletter.style6 .newsletter-wrap .newsletter-form button:hover {
  background: #2e4778;
}
.rs-newsletter.style6 .newsletter-wrap .newsletter-form #email {
  width: 100%;
  background: transparent;
  border: solid #fff 3px; 
}
.rs-newsletter.style6 .newsletter-wrap .newsletter-form span {
  margin: 0;
}
.rs-newsletter.modify .newsletter-wrap .newsletter-form button {
  position: absolute;
  right: 4px;
  top: 4px;
  background: black;
  color: #ffffff;
  padding: 14px 38px;
  transition: all 0.3s ease;
  border-radius: 3px;
}
.rs-newsletter.modify.home13 .newsletter-wrap .newsletter-form button {
  background: #ff5421;
}
.rs-newsletter.modify.home13 .newsletter-wrap .newsletter-form button:hover {
  background: #2e4778;
}
.rs-newsletter.home8-style1 .content-wrap .newsletter-form {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  max-width: 600px;
  border-radius: 4px;
  overflow: hidden;
}
.rs-newsletter.home8-style1 .content-wrap .newsletter-form input,
.rs-newsletter.home8-style1 .content-wrap .newsletter-form button {
  outline: none;
  border: none;
}
.rs-newsletter.home8-style1 .content-wrap .newsletter-form input {
  background: #ffffff;
  color: #505050;
  padding: 18px 40px;
  padding-right: 150px;
}
.rs-newsletter.home8-style1 .content-wrap .newsletter-form button {
  position: absolute;
  right: 0;
  top: 0;
  background: #ff5421;
  color: #ffffff;
  padding: 17px 40px;
  transition: all 0.3s ease;
  border-radius: 0 3px 3px 0;
}
.rs-newsletter.home8-style1 .content-wrap .newsletter-form button:hover {
  background: #2e4778;
}
.rs-newsletter.home8-style1 .content-wrap .newsletter-form #email {
  width: 100%;
}
.rs-newsletter.main-home .newsletter-wrap {
  background: #171f32;
  padding: 60px 70px;
  position: relative;
  border-radius: 5px;
}
.rs-newsletter.main-home .newsletter-wrap .newsletter-form {
  position: relative;
  border-radius: 3px;
  overflow: hidden;
}
.rs-newsletter.main-home .newsletter-wrap .newsletter-form input,
.rs-newsletter.main-home .newsletter-wrap .newsletter-form button {
  outline: none;
  border: none;
}
.rs-newsletter.main-home .newsletter-wrap .newsletter-form input {
  background: #ffffff;
  width: 100%;
  color: #505050;
  padding: 17px 30px;
  padding-right: 150px;
}
.rs-newsletter.main-home .newsletter-wrap .newsletter-form button {
  position: absolute;
  right: 0;
  top: 0;
  background: #ff5421;
  color: #ffffff;
  padding: 17px 40px;
  transition: all 0.3s ease;
  text-transform: uppercase;
}
.rs-newsletter.main-home .newsletter-wrap .newsletter-form button:hover {
  background: #FF6030;
}
.rs-newsletter.orange-color .newsletter-wrap {
  background: #171F32;
}
.rs-newsletter.orange-color .newsletter-wrap .newsletter-form button {
  background: #ff5421;
}
.rs-newsletter.orange-color .newsletter-wrap .newsletter-form button:hover {
  background: #ff683b;
}
.rs-newsletter.yellow-color .newsletter-wrap {
  background: #172e58;
}
.rs-newsletter.yellow-color .newsletter-wrap .newsletter-form button {
  background: #f4bf00;
  color: #1c335f;
}
.rs-newsletter.yellow-color .newsletter-wrap .newsletter-form button:hover {
  background: #ffcb0f;
}
.rs-newsletter.green-color .newsletter-wrap {
  background: #0c8b51;
}
.rs-newsletter.green-color .newsletter-wrap .newsletter-form {
  position: relative;
  border-radius: 3px;
  overflow: hidden;
}
.rs-newsletter.green-color .newsletter-wrap .newsletter-form input,
.rs-newsletter.green-color .newsletter-wrap .newsletter-form button {
  outline: none;
  border: none;
}
.rs-newsletter.green-color .newsletter-wrap .newsletter-form input {
  background: #127c4b;
}
.rs-newsletter.green-color .newsletter-wrap .newsletter-form button {
  background: #0fcb75;
  text-transform: uppercase;
}
.rs-newsletter.green-color .newsletter-wrap .newsletter-form button:hover {
  background: #22cc7f;
}
.rs-newsletter.green-color .newsletter-wrap ::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */

  color: #fff;
  opacity: 1;
}
.rs-newsletter.green-color .newsletter-wrap ::-moz-placeholder {
  /* Firefox 19+ */

  color: #fff;
  opacity: 1;
}
.rs-newsletter.green-color .newsletter-wrap :-ms-input-placeholder {
  /* IE 10+ */

  color: #fff;
  opacity: 1;
}
.rs-newsletter.green-color .newsletter-wrap :-moz-placeholder {
  /* Firefox 18- */

  color: #fff;
  opacity: 1;
}
/* ------------------------------------
    25. Publication Section CSS
---------------------------------------*/
.rs-publication .product-list .image-product {
  position: relative;
  transition: all 0.3s;
}
.rs-publication .product-list .image-product .overley i {
  position: absolute;
  bottom: 0px;
  right: 22px;
  background: transparent;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
}
.rs-publication .product-list .image-product .overley i:before {
  font-size: 30px;
  color: #ffffff;
  font-weight: 400;
  background: #21a7d0;
  padding: 0px 14px 4px;
  height: 60px;
  width: 60px;
  line-height: 60px;
  display: block;
  border-radius: 5px;
}
.rs-publication .product-list .image-product .overley i:hover:before {
  background: #111111;
  color: #ffffff;
  transition: all 0.3s ease;
}
.rs-publication .product-list .content-desc {
  background: #ffffff;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.05);
  padding: 30px 0;
  border-style: solid;
  border-width: 0px 1px 1px 1px;
  border-color: #16AACA2B;
}
.rs-publication .product-list .content-desc .product-title {
  font-size: 22px;
  font-weight: 800;
  line-height: 32px;
  margin-bottom: 12px;
}
.rs-publication .product-list .content-desc .product-title a {
  color: #031a3d;
}
.rs-publication .product-list .content-desc .price {
  font-size: 22px;
  font-weight: 400;
  line-height: 32px;
  color: #54647b;
}
.rs-publication .product-list:hover .image-product .overley i {
  opacity: 1;
  visibility: visible;
  bottom: 280px;
}
/* ------------------------------------
    26. Facilities Section CSS
---------------------------------------*/
.rs-facilities .img-part {
  position: relative;
}
.rs-facilities .img-part .media-icon {
  position: absolute;
  left: 80px;
  top: 90px;
}
.rs-facilities .choose-part {
  max-width: 700px;
  border-radius: 10px;
  padding: 80px 40px;
  background: #e7f8fb;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.rs-facilities .choose-part .our-facilities {
  display: flex;
  align-items: center;
}
.rs-facilities .choose-part .our-facilities .icon-part img {
  max-width: 55px;
}
.rs-facilities .choose-part .our-facilities .content-part .text-part {
  padding-left: 30px;
}
.rs-facilities .choose-part .our-facilities .content-part .text-part .title {
  font-size: 24px;
  line-height: 22px;
  font-weight: 800;
  color: #031a3d;
  margin: 0;
  padding-bottom: 10px;
}
.rs-facilities .choose-part .our-facilities .content-part .text-part .desc {
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  color: #54647b;
  margin: 0;
}
/*----------------------------------------
    27. Faq Section CSS
----------------------------------------*/
.rs-faq-part .content-part .title .text-part {
  font-size: 20px;
  color: #111111;
  line-height: 28px;
  font-weight: 700;
  text-transform: uppercase;
}
.rs-faq-part .content-part .accordion {
  border-width: 12px;
}
.rs-faq-part .content-part .accordion .card {
  margin-bottom: 15px;
  border: none;
}
.rs-faq-part .content-part .accordion .card:last-child {
  margin-bottom: 0;
}
.rs-faq-part .content-part .accordion .card .card-header {
  padding: 0 !important;
  border: none;
}
.rs-faq-part .content-part .accordion .card .card-header .card-link {
  font-size: 17px;
  font-weight: 600;
  line-height: 28px;
  color: #ffffff;
  display: block;
  padding: 15px 15px;
  text-transform: capitalize;
  background: #21a7d0;
}
.rs-faq-part .content-part .accordion .card .card-header .card-link.collapsed {
  background: #f9f8ff;
  color: #505050;
}
.rs-faq-part .content-part .accordion .card .card-body {
  background: #ffffff;
  font-size: 15px;
  font-weight: 400;
  line-height: 25px;
  color: #505050;
  padding: 15px 14px 15px !important;
  border: 1px solid #f9f8ff;
}
.rs-faq-part.style1 .main-part {
  background: #f9f8f8;
  padding: 60px 60px 70px;
}
.rs-faq-part.style1 .main-part .title .text-part {
  font-size: 36px;
  color: #111111;
  line-height: 46px;
  font-weight: 700;
}
.rs-faq-part.style1 .main-part .faq-content .accordion {
  border-width: 12px;
}
.rs-faq-part.style1 .main-part .faq-content .accordion .card {
  margin-bottom: 25px;
  border: none;
}
.rs-faq-part.style1 .main-part .faq-content .accordion .card:last-child {
  margin-bottom: 0;
}
.rs-faq-part.style1 .main-part .faq-content .accordion .card .card-header {
  display: flex;
  align-items: center;
  padding: 0 !important;
  border: none;
}
.rs-faq-part.style1 .main-part .faq-content .accordion .card .card-header .card-link {
  position: relative;
  display: block;
  width: 100%;
  padding: 0 60px;
  height: 65px;
  line-height: 65px;
  font-size: 16px;
  font-weight: 600;
  background: #21a7d0;
  box-shadow: 0 0 49px 0 rgba(0, 0, 0, 0.08);
  color: #ffffff;
}
.rs-faq-part.style1 .main-part .faq-content .accordion .card .card-header .card-link:after {
  position: absolute;
  font-family: FontAwesome;
  content: "\f1f7";
  left: 25px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  font-weight: 400;
}
.rs-faq-part.style1 .main-part .faq-content .accordion .card .card-header .card-link.collapsed {
  background: #ffffff;
  color: #111111;
}
.rs-faq-part.style1 .main-part .faq-content .accordion .card .card-header .card-link.collapsed:after {
  content: "\f0a2";
}
.rs-faq-part.style1 .main-part .faq-content .accordion .card .card-body {
  background: #ffffff;
  font-size: 15px;
  font-weight: 400;
  line-height: 25px;
  color: #505050;
  padding: 15px 14px 15px !important;
  width: 100%;
}
.rs-faq-part.style1 .main-part.new-style {
  background: #f3fcf8;
  padding: 49px 50px 70px;
}
.rs-faq-part.style1 .main-part.new-style .faq-content .accordion .card:last-child {
  margin-bottom: 0;
}
.rs-faq-part.style1 .main-part.new-style .faq-content .accordion .card .card-header .card-link {
  background: #0c8b51;
}
.rs-faq-part.style1 .main-part.new-style .faq-content .accordion .card .card-header .card-link:after {
  position: absolute;
  font-family: FontAwesome;
  content: "\f107";
  left: 25px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  font-weight: 900;
}
.rs-faq-part.style1 .main-part.new-style .faq-content .accordion .card .card-header .card-link.collapsed {
  background: #ffffff;
  color: #111111;
}
.rs-faq-part.style1 .main-part.new-style .faq-content .accordion .card .card-header .card-link.collapsed:after {
  content: "\f105";
}
.rs-faq-part.style1 .main-part.new-style .faq-content .accordion .card .card-body {
  padding: 20px 14px 35px !important;
}
.rs-faq-part.style1 .img-part {
  background: url(assets/images/faq/1.jpg);
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100%;
  min-height: 615px;
}
.rs-faq-part.orange-color .content-part .accordion .card .card-header .card-link,
.rs-faq-part.orange-color .main-part .faq-content .accordion .card .card-header .card-link {
  background: #ff5421;
}
.rs-faq-part.orange-color .content-part .accordion .card .card-header .card-link.collapsed,
.rs-faq-part.orange-color .main-part .faq-content .accordion .card .card-header .card-link.collapse {
  background: #f9f8ff;
}
/* ------------------------------------
    28. Error Section CSS
---------------------------------------*/
.rs-page-error {
  padding: 250px 0;
}
.rs-page-error .error-text {
  color: #111111;
  text-align: center;
}
.rs-page-error .error-text .error-code {
  font-size: 100px;
  color: #111111;
  position: relative;
}
.rs-page-error .error-text .error-code:after {
  position: absolute;
  content: '';
  background: #eee;
  height: 1px;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 600px;
}
.rs-page-error .error-text h3.error-message {
  color: #111111;
}
.rs-page-error .error-text form {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}
.rs-page-error .error-text form input {
  border: 1px solid #ddd;
  color: #444444;
  padding: 10px 17px;
  width: 100%;
  border-radius: 5px;
  margin-bottom: 19px;
}
.rs-page-error .error-text form button {
  background: transparent;
  border: medium none;
  color: #666666;
  padding: 11px 16px;
  position: absolute;
  right: 0px;
  top: 0;
  z-index: 10;
  font-size: 20px;
  outline: none;
  border-radius: 25px;
}
/*----------------------------------------
    29. Shop Section CSS
----------------------------------------*/
.rs-shop-part .woocommerce-result-count {
  font-size: 15px;
  line-height: 26px;
  color: #505050;
  font-weight: 400;
  margin: 0;
}
.rs-shop-part .from-control {
  float: right;
  font-size: 15px;
  color: #505050;
  font-weight: 400;
  vertical-align: top;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 5px;
}
.rs-shop-part .product-list .image-product {
  position: relative;
}
.rs-shop-part .product-list .image-product .overley i {
  position: absolute;
  bottom: 150px;
  right: 13px;
  background: transparent;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
}
.rs-shop-part .product-list .image-product .overley i:before {
  font-size: 30px;
  color: #505050;
  font-weight: 400;
  background: #ffffff;
  border: none ;
  padding: 8px 10px 8px;
  height: 45px;
  width: 45px;
  line-height: 45px;
}
.rs-shop-part .product-list .image-product .onsale {
  font-size: 13px;
  color: #ffffff;
  font-weight: 700;
  line-height: 40px;
  background: #273c66;
  width: 40px;
  height: 40px;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  position: absolute;
  top: 13px;
  right: 13px;
}
.rs-shop-part .product-list .content-desc .loop-product-title {
  margin: 0;
  font-size: 18px;
  line-height: 50px;
  color: #505050;
  font-weight: 700;
}
.rs-shop-part .product-list .content-desc .loop-product-title a {
  color: #111111;
  font-size: 22px;
  font-weight: 600;
}
.rs-shop-part .product-list .content-desc .loop-product-title a:hover {
  color: #21a7d0;
}
.rs-shop-part .product-list .content-desc .price {
  font-size: 20px;
  line-height: 30px;
  color: #21a7d0;
  font-weight: 700;
  margin: 0;
}
.rs-shop-part .product-list .content-desc .price del {
  opacity: 0.6;
  padding-right: 10px;
}
.rs-shop-part .product-list:hover .image-product .overley i {
  opacity: 1;
  visibility: visible;
  bottom: 13px;
}
.rs-shop-part.part2 {
  /*Shop Single CSS*/

}
.rs-shop-part.part2 .img-part a {
  border: 1px solid #505050;
  font-size: 15px;
  line-height: 26px;
  font-weight: 400;
  color: #505050;
  background: #ffffff;
}
.rs-shop-part.part2 .rs-single-product .tab-area ul.nav-tabs {
  margin-bottom: 25px;
  border: none;
  border-bottom: 1px solid #e6ebee;
}
.rs-shop-part.part2 .rs-single-product .tab-area ul.nav-tabs li {
  margin-right: 40px;
}
.rs-shop-part.part2 .rs-single-product .tab-area ul.nav-tabs li a {
  display: inline-block;
  font-size: 18px;
  line-height: 31px;
  font-weight: 700;
  color: #ffffff;
  padding: 18px 40px 18px;
  background: #273c66;
}
.rs-shop-part.part2 .rs-single-product .tab-area ul.nav-tabs li a.active {
  color: #505050;
  background: transparent;
}
.rs-shop-part.part2 .rs-single-product .tab-area ul.nav-tabs li a.active:hover {
  color: #ffffff;
  background: #273c66;
}
.rs-shop-part.part2 .rs-single-product .tab-area ul.nav-tabs li:last-child {
  margin-right: 0;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .tab-title {
  margin-bottom: 25px;
  font-size: 30px;
  font-weight: 600;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .dsc-p {
  margin: 0;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .top-area {
  margin-bottom: 5px;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .top-area p {
  margin-bottom: 23px;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .top-area h4 {
  font-weight: 400;
  color: unset;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .comment-form p.comment-notes {
  margin-bottom: 15px;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .comment-form .placeholder-cntrl label {
  font-weight: 600;
  min-width: 100px;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .comment-form .placeholder-cntrl input {
  border: 1px solid rgba(54, 54, 54, 0.1);
  padding: 10px 15px;
  margin-bottom: 10px;
  outline: none;
  width: 50%;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .comment-form .placeholder-cntrl textarea {
  display: block;
  width: 100%;
  padding: 10px 15px;
  height: 75px;
  border: 1px solid rgba(54, 54, 54, 0.1);
  margin-bottom: 20px;
  outline: none;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .comment-form .ratings {
  font-weight: 600;
  margin-bottom: 18px;
  display: block;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .comment-form .ratings ul li {
  display: inline;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .comment-form .ratings ul li i {
  color: #f5d109;
  font-weight: normal;
}
.rs-shop-part.part2 .rs-single-product .tab-area .tab-content .tab-pane .reviews-grid .comment-form .readon {
  font-weight: 500;
  font-size: 15px;
  padding: 13px 45px;
  border: none;
  outline: none;
}
.rs-shop-part.part2 .content-part .loop-product {
  font-size: 25px;
  line-height: 25px;
  color: #111111;
  font-weight: 700;
}
.rs-shop-part.part2 .content-part .price {
  font-size: 20px;
  line-height: 34px;
  color: #21a7d0;
  font-weight: 800;
  display: block;
}
.rs-shop-part.part2 .content-part .desc-part {
  font-size: 15px;
  line-height: 26px;
  color: #505050;
  font-weight: 400;
}
.rs-shop-part.part2 .content-part .add-cart {
  font-size: 15px;
  line-height: 25px;
  color: #ffffff;
  font-weight: 400;
  background: #273c66;
  text-transform: uppercase;
  border-radius: 5px;
  padding: 10px 30px 10px;
  border: 2px solid #21a7d0;
}
.rs-shop-part.part2 .content-part .add-cart:hover {
  background: transparent;
  color: #21a7d0;
}
.rs-shop-part.orange-color .product-list .content-desc .loop-product-title a:hover {
  color: #ff5421;
}
.rs-shop-part.orange-color .product-list .content-desc .price {
  color: #ff5421;
}
/*----------------------------------------
    30. Single Shop Section CSS
----------------------------------------*/
.rs-single-shop .single-product-image .images-single {
  z-index: 1 !important;
}
.rs-single-shop .single-product-image img {
  width: 100%;
}
.rs-single-shop .single-price-info .product-title {
  font-size: 25px;
  font-weight: 700;
  margin-bottom: 12px;
}
.rs-single-shop .single-price-info .single-price {
  color: #21a7d0;
  font-weight: 600;
}
.rs-single-shop .single-price-info .some-text {
  margin-top: 15px;
}
.rs-single-shop .single-price-info form {
  margin-bottom: 30px;
}
.rs-single-shop .single-price-info form input {
  height: 40px;
  width: 70px;
  line-height: 40px;
  text-align: center;
  padding-left: 10px;
  border: 1px solid rgba(54, 54, 54, 0.1);
  outline: none;
}
.rs-single-shop .single-price-info p.category {
  margin: 0;
  padding-top: 25px;
  border-top: 1px solid #e6e6e6;
  font-size: 14px;
}
.rs-single-shop .single-price-info p.category span {
  font-weight: 700;
  padding-right: 10px;
}
.rs-single-shop .single-price-info p.category a {
  color: #505050;
}
.rs-single-shop .single-price-info p.category a:hover {
  color: #21a7d0;
}
.rs-single-shop .single-price-info .tag {
  margin: 0;
}
.rs-single-shop .single-price-info .tag span {
  font-weight: 700;
}
.rs-single-shop .tab-area {
  margin-top: 50px;
}
.rs-single-shop .tab-area ul.nav-tabs {
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 32px;
}
.rs-single-shop .tab-area ul.nav-tabs li {
  margin-right: 3px;
}
.rs-single-shop .tab-area ul.nav-tabs li a {
  padding: 10px 15px;
  display: inline-block;
  border-radius: 5px 5px 0 0;
  background: #f0f0f0;
  color: #111111;
  font-weight: 600;
}
.rs-single-shop .tab-area ul.nav-tabs li a:hover,
.rs-single-shop .tab-area ul.nav-tabs li a.active {
  background: #21a7d0;
  color: #ffffff;
}
.rs-single-shop .tab-area ul.nav-tabs li:last-child {
  margin-right: 0;
}
.rs-single-shop .tab-area .tab-content .tab-pane .tab-title {
  font-weight: 700;
  margin-bottom: 34px;
}
.rs-single-shop .tab-area .tab-content .tab-pane .dsc-p {
  margin: 0;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .top-area {
  margin-bottom: 5px;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .top-area p {
  margin-bottom: 23px;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .top-area h4 {
  font-weight: 400;
  color: unset;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .comment-form {
  max-width: 600px;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .comment-form p.comment-notes {
  margin-bottom: 15px;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .comment-form .placeholder-cntrl label {
  font-weight: 600;
  display: block;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .comment-form .placeholder-cntrl input {
  border: 1px solid rgba(54, 54, 54, 0.1);
  padding: 10px 15px;
  margin-bottom: 10px;
  width: 100%;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .comment-form .placeholder-cntrl textarea {
  width: 100%;
  padding: 10px 15px;
  height: 75px;
  border: 1px solid rgba(54, 54, 54, 0.1);
  margin-bottom: 20px;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .comment-form .ratings {
  font-weight: 600;
  margin-bottom: 18px;
  display: block;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .comment-form .ratings ul li {
  display: inline;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .comment-form .ratings ul li i {
  color: #21a7d0;
  font-weight: normal;
}
.rs-single-shop .tab-area .tab-content .tab-pane .reviews-grid .comment-form .readon {
  text-transform: uppercase;
  font-weight: 600;
  font-size: 15px;
  padding: 0 22px;
}
.rs-single-shop.orange-color .single-price-info .single-price,
.rs-single-shop.orange-color .single-price-info p.category a:hover,
.rs-single-shop.orange-color .tab-area .tab-content .tab-pane .reviews-grid .comment-form .ratings ul li i {
  color: #ff5421;
}
.rs-single-shop.orange-color .tab-area ul.nav-tabs li a:hover,
.rs-single-shop.orange-color .tab-area ul.nav-tabs li a.active {
  background: #ff5421;
}
/*----------------------------------------
    31. Cart Section CSS
----------------------------------------*/
.rs-cart .cart-wrap table.cart-table {
  border: 1px solid rgba(0, 0, 0, 0.1);
  width: 100%;
  border-collapse: collapse;
}
.rs-cart .cart-wrap table.cart-table td,
.rs-cart .cart-wrap table.cart-table th {
  padding: 25px;
  text-align: center;
  border: 1px solid #ccc;
}
.rs-cart .cart-wrap table.cart-table th {
  border: none;
  font-size: 18px;
  padding: 25px;
  text-align: center;
  vertical-align: middle;
  font-weight: 700;
}
.rs-cart .cart-wrap table.cart-table td {
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-top: 1px solid #e6e6e6;
}
.rs-cart .cart-wrap table.cart-table .product-remove a {
  margin: 0 auto;
  color: #111111;
  display: block;
  border-radius: 100%;
  border: 1px solid #ddd;
  font-size: 16px;
  font-weight: 400;
  height: 20px;
  width: 20px;
  line-height: 17px;
  text-align: center;
}
.rs-cart .cart-wrap table.cart-table .product-remove a:hover {
  background: #ff0000;
  color: #ffffff;
}
.rs-cart .cart-wrap table.cart-table .product-thumbnail {
  min-width: 32px;
}
.rs-cart .cart-wrap table.cart-table .product-thumbnail a img {
  width: 80px;
  height: auto;
}
.rs-cart .cart-wrap table.cart-table .product-name a {
  color: #505050;
  font-weight: 700;
}
.rs-cart .cart-wrap table.cart-table .product-name a:hover {
  color: #21a7d0;
}
.rs-cart .cart-wrap table.cart-table .product-price {
  font-weight: 700;
}
.rs-cart .cart-wrap table.cart-table .product-quantity input {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 0 0 0 10px;
  max-width: 65px;
  margin: 0 auto;
  outline: none;
}
.rs-cart .cart-wrap table.cart-table .action .coupon {
  float: left;
}
.rs-cart .cart-wrap table.cart-table .action .coupon input {
  box-sizing: border-box;
  border: 1px solid #d3ced2;
  padding: 6px 6px 5px;
  margin: 0 4px 0 0;
  outline: none;
  width: 320px;
  border-radius: 4px;
  height: 45px;
  margin-right: 20px;
  text-align: left;
  padding-left: 22px;
}
.rs-cart .cart-collaterals {
  width: 100%;
  overflow: hidden;
}
.rs-cart .cart-collaterals .cart-totals {
  float: right;
  width: 48%;
}
.rs-cart .cart-collaterals .cart-totals .title {
  font-weight: 700;
  color: #505050;
  text-transform: capitalize;
}
.rs-cart .cart-collaterals .cart-totals table.cart-total-table {
  text-align: left;
  width: 100%;
  border-collapse: collapse;
  border-radius: 5px;
}
.rs-cart .cart-collaterals .cart-totals table.cart-total-table tr {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.rs-cart .cart-collaterals .cart-totals table.cart-total-table tr th {
  font-size: 18px;
  font-weight: 700;
}
.rs-cart .cart-collaterals .cart-totals table.cart-total-table tr td,
.rs-cart .cart-collaterals .cart-totals table.cart-total-table tr th {
  padding: 30px 10px;
}
.rs-cart.orange-color .cart-wrap table.cart-table .product-name a:hover {
  color: #ff5421;
}
/*-----------------------------------------------
    32. Header Cart Section CSS
-----------------------------------------------*/
.woocommerce-mini-cart {
  position: absolute;
  right: 0;
  top: 200%;
  transition: all 0.3s ease;
  width: 270px;
  opacity: 0;
  padding-top: 120px;
  visibility: hidden;
  overflow: hidden;
}
.woocommerce-mini-cart .cart-bottom-part {
  padding: 30px 20px;
  background: #21a7d0;
}
.woocommerce-mini-cart.left-view {
  right: unset;
  left: 0;
}
.woocommerce-mini-cart li {
  border-bottom: 1px solid rgba(170, 170, 170, 0.25);
  padding: 0 0 20px 0 !important;
  margin: 0 0 20px 0 !important;
  margin-right: 0 !important;
  border: none !important;
  display: flex !important;
  justify-content: center;
  align-items: center;
}
.woocommerce-mini-cart li:last-child {
  margin: 0 !important;
}
.woocommerce-mini-cart .product-info {
  padding: 0 26px;
}
.woocommerce-mini-cart .product-info a {
  color: #ffffff !important;
  font-weight: 700;
}
.woocommerce-mini-cart .product-info a:hover {
  color: #273c66 !important;
}
.woocommerce-mini-cart .total-price {
  padding: 0 0 20px;
}
.woocommerce-mini-cart .quantity,
.woocommerce-mini-cart .total-price {
  color: #f3f8f9;
}
.woocommerce-mini-cart .icon-cart i {
  color: #ffffff;
  width: 18px;
  height: 18px;
  line-height: 16px;
  border: 1px solid #ffffff;
  border-radius: 30px;
  font-weight: 500;
  font-size: 12px;
  text-align: center;
  transition: all 0.3s ease;
}
.woocommerce-mini-cart .icon-cart i:hover {
  color: #273c66;
}
.woocommerce-mini-cart .icon-cart,
.woocommerce-mini-cart .product-image {
  margin-top: 2px;
}
.woocommerce-mini-cart .product-image {
  display: block;
  float: right;
  text-align: right;
  width: 56px;
}
.woocommerce-mini-cart .crt-btn {
  padding: 5px 10px;
  background: #273c66;
  color: #ffffff;
  display: inline-block;
  border-radius: 3px;
}
.woocommerce-mini-cart .crt-btn:hover {
  background: #2e4778;
  color: #ffffff;
}
.woocommerce-mini-cart .crt-btn.btn1 {
  margin-right: 10px;
}
.mini-cart-active:hover .woocommerce-mini-cart {
  visibility: visible;
  opacity: 1;
  top: 0;
}
/*----------------------------------------
    33. Checkout Section CSS
----------------------------------------*/
.rs-checkout .checkout-title {
  margin-bottom: 30px;
}
.rs-checkout .checkout-title h3 {
  font-size: 26px;
  margin: 0;
}
.rs-checkout .coupon-toggle .accordion .card {
  border: unset;
  border-top: 3px solid #21a7d0;
  border-radius: 0;
}
.rs-checkout .coupon-toggle .accordion .card .card-header {
  border: none;
  margin: 0;
  border-radius: unset;
}
.rs-checkout .coupon-toggle .accordion .card .card-header .card-title {
  margin: 0;
}
.rs-checkout .coupon-toggle .accordion .card .card-header .card-title span i {
  margin-right: 10px;
}
.rs-checkout .coupon-toggle .accordion .card .card-header .card-title button {
  background: unset;
  border: none;
  color: #273c66;
  transition: all 0.3s ease;
  outline: none;
  cursor: pointer;
}
.rs-checkout .coupon-toggle .accordion .card .card-header .card-title button:hover {
  color: #21a7d0;
}
.rs-checkout .coupon-toggle .accordion .card .card-body {
  border: 1px solid #d3ced2;
  padding: 20px 20px 50px 20px;
  margin-top: 2em;
  text-align: left;
}
.rs-checkout .coupon-toggle .accordion .card .card-body .coupon-code-input {
  width: 47%;
  float: left;
  margin-right: 50px;
}
.rs-checkout .coupon-toggle .accordion .card .card-body .coupon-code-input input {
  width: 100%;
  height: 45px;
  outline: none;
  padding: 10px 18px;
  color: #505050;
  border: 1px solid rgba(54, 54, 54, 0.1);
}
.rs-checkout .full-grid {
  margin-top: 25px;
}
.rs-checkout .full-grid .form-content-box {
  margin-bottom: 50px;
}
.rs-checkout .full-grid .form-content-box .form-group label {
  line-height: 2;
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}
.rs-checkout .full-grid .form-content-box .form-group select {
  color: #666666;
  opacity: .5;
  padding-left: 8px;
  padding-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: 1px solid #ccc;
  border-radius: 0;
  height: 45px;
  line-height: 45px;
  cursor: pointer;
  width: 100%;
  outline: none;
}
.rs-checkout .full-grid .form-content-box .form-group textarea {
  height: 4em;
  line-height: 1.5;
  display: block;
  box-shadow: none;
  width: 100%;
  border: 1px solid rgba(54, 54, 54, 0.1);
  padding: 10px 18px;
  margin: 0;
  outline: none;
}
.rs-checkout .full-grid .form-content-box .form-group .form-control-mod {
  border: 1px solid rgba(54, 54, 54, 0.1);
  height: 45px;
  padding: 10px 18px;
  width: 100%;
  margin: 0;
  outline: none;
  line-height: normal;
  border-radius: unset;
}
.rs-checkout .full-grid .form-content-box .form-group .form-control-mod.margin-bottom {
  margin-bottom: 10px !important;
}
.rs-checkout .full-grid .ordered-product table {
  width: 100%;
}
.rs-checkout .full-grid .ordered-product table tr th {
  border: 1px solid #ccc;
  padding: 9px 12px;
}
.rs-checkout .full-grid .ordered-product table tr td {
  border: 1px solid #ccc;
  padding: 6px 12px;
}
.rs-checkout .full-grid .payment-method .top-area {
  border-bottom: 1px solid #d3ced2;
}
.rs-checkout .full-grid .payment-method .top-area .payment-co {
  margin-bottom: 20px;
}
.rs-checkout .full-grid .payment-method .top-area .payment-co span {
  font-weight: 600;
  margin-right: 10px;
}
.rs-checkout .full-grid .payment-method .top-area .p-msg {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 1em;
  margin: 1em 0 2em;
  font-size: .92em;
  border-radius: 2px;
  line-height: 1.5;
  background-color: #f9f9f9;
}
.rs-checkout .full-grid .payment-method .top-area .p-msg:before {
  content: '';
  display: block;
  border: 1em solid #f9f9f9;
  border-right-color: transparent;
  border-left-color: transparent;
  border-top-color: transparent;
  position: absolute;
  top: -0.75em;
  left: 0;
  margin: -1em 0 0 2em;
}
.rs-checkout.orange-color .coupon-toggle .accordion .card {
  border-color: #ff5421;
}
.rs-checkout.orange-color .coupon-toggle .accordion .card .card-header .card-title button:hover {
  color: #ff5421;
}
/*----------------------------------------
    34. Login Section CSS
----------------------------------------*/
.rs-login .noticed {
  background: black;
  max-width: 800px;
  padding: 70px;
  margin: 0 auto;
}
.rs-login .noticed .main-part {
  max-width: 400px;
  margin: 0 auto;
  text-align: center;
  border-radius: 5px;
}
.rs-login .noticed .main-part .method-account .login {
  font-size: 40px;
  line-height: 50px;
  font-weight: 700;
  color: #fff;
}
.rs-login .noticed .main-part .method-account label {
  font-size: 15px;
  line-height: 30px;
  font-weight: 600;
  color: #505050;
}
.rs-login .noticed .main-part .method-account input {
  margin-bottom: 30px;
  font-size: 15px;
  font-weight: 400;
  color: #505050;
  width: 100%;
  display: block;
  border: solid 2px;
  height: 45px;
  padding: 10px 18px;
  border-radius: 5px;
  box-shadow: 0 1 30px #eee;
}
.rs-login .noticed .main-part .method-account span {
  font-size: 15px;
  line-height: 30px;
  font-weight: 600;
  color: #505050;
  padding-left: 10px;
}
.rs-login .noticed .main-part .method-account .last-password p {
  color: #505050;
  margin: 0;
}
.rs-login .noticed .main-part .method-account .last-password p a {
  font-size: 15px;
  line-height: 26px;
  font-weight: 400;
  color: #ff5421;
}
.rs-login .noticed .main-part .method-account .last-password p a:hover {
  color: #505050;
}
/* -----------------------------------
    35. Register Section CSS
-------------------------------------*/
.register-section {
  position: relative;
}
.register-section .register-box {
  position: relative;
  max-width: 970px;
  margin: 0 auto;
  padding: 45px 40px 35px;
  /*background: #f9f8f8;*/
}
.register-section .register-box .styled-form {
  max-width: 600px;
  margin: 0 auto;
}
.register-section .register-box .styled-form .form-group .title-box {
  position: relative;
  text-align: center;
  margin-bottom: 32px;
}
.register-section .register-box .styled-form .form-group .title-box h2 {
  position: relative;
  color: #03382e;
  font-weight: 700;
  line-height: 1.3em;
}
.register-section .register-box .styled-form .form-group .title-box .text {
  position: relative;
  color: #03382e;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.8em;
  margin-top: 12px;
}
/* Styled Form */
.styled-form {
  position: relative;
}
.styled-form .form-group {
  position: relative;
  /* CheckBox */

  /* RadioBox */

}
.styled-form .form-group .eye-icon {
  position: absolute;
  right: 20px;
  top: 52px;
  color: #222222;
  font-size: 18px;
  z-index: 1;
  opacity: 0.5;
}
.styled-form .form-group label {
  position: relative;
  color: #626262;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 15px;
}
.styled-form .form-group input {
  position: relative;
  height: 50px;
  padding: 6px 30px;
  width: 100%;
  font-size: 16px;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  border: solid 2px;
  /*box-shadow: 0 0 30px #eee;*/
}
.styled-form .form-group input:focus {
  /*border-color: #ff6262;*/
}
.styled-form .form-group textarea {
  position: relative;
  height: 150px;
  width: 100%;
  resize: none;
  padding: 15px 15px;
  border-radius: 50px;
  border: 1px solid #f2f2f2;
  transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}
.styled-form .form-group .users {
  position: relative;
  color: #626262;
  font-size: 16px;
  text-align: center;
}
.styled-form .form-group .users a {
  position: relative;
  color: #ff5421;
  font-weight: 600;
  text-decoration: underline;
}
.styled-form .form-group .users a:hover {
  color: #ed3600;
}
.styled-form .form-group .check-box {
  position: relative;
  margin-bottom: 3px;
  margin-top: 7px;
}
.styled-form .form-group .check-box label {
  position: relative;
  display: block;
  width: 100%;
  line-height: 22px;
  padding: 4px 10px 0px;
  padding-left: 25px;
  background: #ffffff;
  font-size: 16px;
  font-weight: 400;
  color: #626262;
  cursor: pointer;
  margin-bottom: 0;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
  font-family: 'Poppins', sans-serif;
}
.styled-form .form-group .check-box label:before {
  position: absolute;
  left: 0;
  top: 6px;
  height: 15px;
  width: 15px;
  background: #ffffff;
  content: "";
  border-radius: 3px;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
  border: 1px solid #21a7d0;
}
.styled-form .form-group .check-box label:after {
  position: absolute;
  left: 0px;
  top: 0;
  height: 15px;
  line-height: 15px;
  max-width: 0;
  font-size: 14px;
  color: #ffffff;
  font-weight: 800;
  overflow: hidden;
  background: transparent;
  text-align: center;
  font-family: 'FontAwesome';
  -webkit-transition: max-width 500ms ease;
  -moz-transition: max-width 500ms ease;
  -ms-transition: max-width 500ms ease;
  -o-transition: max-width 500ms ease;
  transition: max-width 500ms ease;
}
.styled-form .form-group .check-box input[type="checkbox"]:checked + label {
  border-color: #25a9e0;
}
.styled-form .form-group .radio-box {
  position: relative;
  margin-bottom: 3px;
  margin-top: 7px;
}
.styled-form .form-group .radio-box label {
  position: relative;
  display: block;
  height: 30px;
  width: 100%;
  line-height: 9px;
  padding: 4px 10px 0px;
  padding-left: 25px;
  background: #ffffff;
  font-size: 16px;
  font-weight: 400;
  color: #626262;
  cursor: pointer;
  margin-bottom: 0;
  font-family: 'Poppins', sans-serif;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}
.styled-form .form-group .radio-box label:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 15px;
  width: 15px;
  background: #ffffff;
  content: "";
  border-radius: 3px;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
  border: 1px solid #21a7d0;
}
.styled-form .form-group .radio-box label:after {
  position: absolute;
  left: 0px;
  top: 0;
  height: 15px;
  line-height: 15px;
  max-width: 0;
  font-size: 14px;
  color: #ffffff;
  font-weight: 800;
  overflow: hidden;
  background: transparent;
  text-align: center;
  font-family: 'FontAwesome';
  -webkit-transition: max-width 500ms ease;
  -moz-transition: max-width 500ms ease;
  -ms-transition: max-width 500ms ease;
  -o-transition: max-width 500ms ease;
  transition: max-width 500ms ease;
}
.styled-form .forgot {
  position: relative;
  font-weight: 500;
  color: #626262;
  font-size: 16px;
  margin-top: 10px;
  display: inline-block;
  text-decoration: underline;
}
.styled-form .form-group .form-group .check-box label {
  padding-left: 30px;
  padding-top: 1px;
  cursor: pointer;
}
.styled-form .form-group .check-box input[type="checkbox"] {
  display: none;
}
.styled-form .form-group .check-box input[type="checkbox"]:checked + label:before {
  border: 5px solid #21a7d0;
  background: #ffffff;
}
.styled-form .form-group .check-box input[type="checkbox"]:checked + label:after {
  max-width: 20px;
  opacity: 1;
}
.styled-form .form-group .radio-box input[type="radio"]:checked + label {
  border-color: #25a9e0;
}
.styled-form .form-group .form-group .radio-box label {
  padding-left: 30px;
  padding-top: 1px;
  cursor: pointer;
}
.styled-form .form-group .radio-box input[type="radio"] {
  display: none;
}
.styled-form .form-group .radio-box input[type="radio"]:checked + label:before {
  border: 5px solid #21a7d0;
  background: #ffffff;
}
.styled-form .form-group .radio-box input[type="radio"]:checked + label:after {
  max-width: 20px;
  opacity: 1;
}
.rs-free-contact {
  background: #0c8b51;
  padding: 50px 50px 50px 50px;
}
.rs-free-contact .from-control {
  width: 100%;
  border-style: solid;
  border-width: 1px 1px 1px 1px;
  border-color: #0C8B51;
  background-color: #127C4B;
  border-radius: 3px;
  padding: 10px 18px;
}
.rs-free-contact textarea {
  height: 140px;
}
.rs-free-contact ::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */

  color: #ffffff;
  opacity: 1;
}
.rs-free-contact ::-moz-placeholder {
  /* Firefox 19+ */

  color: #ffffff;
  opacity: 1;
}
.rs-free-contact :-ms-input-placeholder {
  /* IE 10+ */

  color: #ffffff;
  opacity: 1;
}
.rs-free-contact :-moz-placeholder {
  /* Firefox 18- */

  color: #ffffff;
  opacity: 1;
}
/*-------------------------------------
    36. Contact Section CSS
--------------------------------------*/
.contact-page-section .inner-part .title {
  font-size: 36px;
  color: #101010;
  line-height: 46px;
  font-weight: 700;
  margin-bottom: 14px;
}
.contact-page-section .inner-part .title2 {
  font-size: 28px;
  font-weight: 700;
  line-height: 1.25em;
  color: #111111;
}
.contact-page-section .inner-part p {
  font-size: 19px;
  color: #363636;
  line-height: 32px;
  font-weight: 400;
}
.contact-page-section .rs-quick-contact {
  background-color: #18191A;
  color: #fff;
  padding: 70px 150px 70px;
}
.contact-page-section .rs-quick-contact .from-control {
  border-style: solid;
  border-width: 1px 1px 1px 1px;
  border-color: #F1F1F1;
  /*box-shadow: 0 0 30px #eee;*/
  color: #fff;
  width: 100%;
  max-width: 100%;
  opacity: 1;
  border-radius: 3px;
  border: 1px solid #ffffff;
  color: #767676;
  background: #ffffff;
  padding: 10px 18px;
}
.contact-page-section .rs-quick-contact textarea {
  height: 140px;
}
.contact-page-section .rs-quick-contact .form-group .btn-send {
  padding: 16px 0px 16px 0px;
  text-transform: uppercase;
  box-shadow: 0px 28px 50px 0px rgba(0, 0, 0, 0.05);
  outline: none;
  border: none;
  padding: 12px 40px;
  border-radius: 3px;
  display: inline-block;
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
  background: #ff5421;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  width: 100%;
}
.contact-page-section .rs-quick-contact .form-group .btn-send:hover {
  opacity: 0.90;
}
.contact-page-section .rs-quick-contact.new-style {
  padding: 70px 60px 70px;
}
.contact-page-section .rs-contact-box .address-item {
  padding: 50px 40px 50px 40px;
  background: #ffffff;
  box-shadow: 0 0 30px #fff;
  text-align: center;
  margin: 0px 20px 0px 0px;
  border-radius: 5px 5px 5px 5px;
}
.contact-page-section .rs-contact-box .address-item .icon-part {
  padding-bottom: 20px;
}
.contact-page-section .rs-contact-box .address-item .icon-part img {
  width: 65px;
}
.contact-page-section .rs-contact-box .address-item .address-text .label {
  display: block;
  color: #101010;
  font-size: 22px;
  font-weight: 600;
  padding-bottom: 10px;
}
.contact-page-section .rs-contact-box .address-item .address-text .des {
  font-size: 17px;
  color: #031a3d;
}
.contact-page-section .rs-contact-box .address-item .address-text .des a {
  color: #031a3d;
}
.contact-page-section .rs-contact-box .address-item .address-text .des a:hover {
  color: #ff5421;
}
.contact-page-section .rs-contact-wrap {
  padding: 40px 35px 30px 35px;
  background-color: #F9F8F8;
  border-radius: 5px 5px 5px 5px;
}
.contact-page-section .rs-contact-wrap .address-item {
  display: flex;
  position: relative;
  margin-bottom: 40px;
}
.contact-page-section .rs-contact-wrap .address-item .address-icon {
  margin-right: 20px;
}
.contact-page-section .rs-contact-wrap .address-item .address-icon i {
  font-size: 35px;
  color: #ff5421;
}
.contact-page-section .rs-contact-wrap .address-item .address-text .label {
  display: block;
  color: #505050;
  font-size: 15px;
  font-weight: 700;
  padding-bottom: 10px;
  line-height: 1.2;
}
.contact-page-section .rs-contact-wrap .address-item .address-text .des {
  font-size: 18px;
  color: #111111;
  font-weight: 500;
}
.contact-page-section .rs-contact-wrap .address-item .address-text .des a {
  color: #111111;
  font-weight: 500;
}
.contact-page-section .rs-contact-wrap .address-item .address-text .des a:hover {
  color: #ff5421;
}
.contact-page-section .contact-comment-box .from-control {
  border-style: solid;
  border-width: 1px 1px 1px 1px;
  border-color: #F1F1F1;
  box-shadow: 0 0 30px #eee;
  color: #363636;
  width: 100%;
  max-width: 100%;
  opacity: 1;
  border-radius: 3px;
  border: 1px solid #ffffff;
  color: #767676;
  background: #ffffff;
  padding: 10px 18px;
}
.contact-page-section .contact-comment-box textarea {
  height: 140px;
}
.contact-page-section .contact-comment-box .form-group .btn-send {
  padding: 16px 0px 16px 0px;
  box-shadow: 0px 28px 50px 0px rgba(0, 0, 0, 0.05);
  outline: none;
  border: none;
  padding: 12px 40px;
  border-radius: 3px;
  display: inline-block;
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
  background: #ff5421;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  width: 100%;
  text-transform: uppercase;
}
.contact-page-section .contact-comment-box .form-group .btn-send:hover {
  opacity: 0.90;
}
.contact-page-section .contact-comment-box.new-style {
  padding: 60px 70px 60px !important;
}
.contact-page-section .contact-map {
  overflow: hidden;
}
.contact-page-section .contact-map iframe {
  float: left;
  width: 100%;
  height: 550px;
  border: none;
}
.contact-page-section .contact-map2 {
  overflow: hidden;
}
.contact-page-section .contact-map2 iframe {
  float: left;
  width: 100%;
  height: 720px;
  border: none;
}
.contact-page-section .contact-map3 {
  overflow: hidden;
}
.contact-page-section .contact-map3 iframe {
  float: left;
  width: 100%;
  height: 600px;
  border: none;
}
.contact-page-section .contact-address-section {
  text-align: center;
  margin: 0 0 50px;
}
.contact-page-section .contact-address-section .contact-info {
  background: #f9f9f9;
  display: flex;
  text-align: left;
  align-items: center;
  padding: 0 30px;
  min-height: 175px;
  border-radius: 5px;
  position: relative;
  transition: all 0.3s ease;
}
.contact-page-section .contact-address-section .contact-info .icon-part {
  margin-right: 30px;
}
.contact-page-section .contact-address-section .contact-info .icon-part i {
  color: #ff5421;
  font-size: 30px;
  margin: 0;
  padding: 0;
  width: 70px;
  height: 70px;
  line-height: 58px;
  border-radius: 50%;
  text-align: center;
  background: transparent;
  border: 6px solid #f9ede9;
}
.contact-page-section .contact-address-section .contact-info .content-part .info-subtitle {
  font-size: 18px;
  font-weight: 700;
  color: #505050;
  margin: 0 0 5px;
}
.contact-page-section .contact-address-section .contact-info .content-part .info-title {
  font-size: 24px;
  font-weight: 700;
  color: #111111;
  margin: 0;
}
.contact-page-section .contact-address-section .contact-info .content-part a {
  color: #111111;
  display: block;
}
.contact-page-section .contact-address-section .contact-info .content-part a:hover {
  color: #ff5421;
}
.contact-page-section .contact-address-section .contact-info:hover {
  transform: translateY(-5px);
}
.contact-page-section .contact-address-section.style2 {
  margin: 0;
}
.contact-page-section .contact-comment-section {
  padding: 50px;
}
.contact-page-section .contact-comment-section h3 {
  font-size: 36px;
  margin: 0;
  padding-bottom: 15px;
  font-weight: 700;
}
.contact-page-section .contact-comment-section form .form-group input {
  height: 43px;
  padding: 0 15px;
}
.contact-page-section .contact-comment-section form .form-group input,
.contact-page-section .contact-comment-section form .form-group textarea {
  border: none;
  background: #ffffff;
  border-radius: 0;
  box-shadow: none;
}
.contact-page-section .contact-comment-section form .form-group label {
  color: #505050;
  font-weight: 400;
}
.contact-page-section .contact-comment-section form .form-group input.btn-send {
  text-transform: uppercase;
  color: #ffffff;
  background-color: #ff5421;
  margin-top: 15px;
  border: none;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-weight: 600;
  padding: 0 50px;
  cursor: pointer;
  transition: 0.4s;
  -webkit-transition: 0.4s;
  -ms-transition: 0.4s;
}
.contact-page-section .contact-comment-section form .form-group input.btn-send:hover {
  background: #ed3600;
}
.contact-page-section .contact-bg1 {
  background: #f9f9f9;
}
/*------------------------------------
    37. Footer Section CSS
------------------------------------*/
.rs-footer {
  background-color: black;
  /*background-image: url(assets/images/bg/footer-bg.png);*/
  background-size: cover;
}
.rs-footer .footer-top {
  padding: 218px 0 93px;
}
.rs-footer .footer-top .widget-title {
  color: #ffffff;
  font-size: 18px;
  line-height: 26px;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 35px;
}
.rs-footer .footer-top .site-map li {
  padding-right: 15px;
  position: relative;
  margin-bottom: 11px;
}
.rs-footer .footer-top .site-map li a {
  color: #e8e8e8;
}
.rs-footer .footer-top .site-map li a:hover {
  color: #21a7d0;
}
.rs-footer .footer-top .site-map li:before {
  position: absolute;
  content: '';
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: #21a7d0;
}
.rs-footer .footer-top .site-map li:last-child {
  margin-bottom: 0;
}
.rs-footer .footer-top .address-widget li {
  position: relative;
  margin-bottom: 16px;
}
.rs-footer .footer-top .address-widget li i {
  /*margin-left: 10px;*/
  /*margin-right: 10px;*/
  position: absolute;
}
.rs-footer .footer-top .address-widget li i:before {
  color: #ffffff;
}
.rs-footer .footer-top .address-widget li .desc {
  padding-left: 35px;
  color: #ffffff;
}
.rs-footer .footer-top .address-widget li .desc a {
  color: #ffffff;
}
.rs-footer .footer-top .address-widget li .desc a:hover {
  color: #21a7d0;
}
.rs-footer .footer-top .address-widget li:last-child {
  margin-bottom: 0;
}
.rs-footer .footer-top.no-gap {
  padding-top: 93px;
}
.rs-footer .footer-bottom {
  padding: 40px 0;
  background: transparent;
  position: relative;
}
.rs-footer .footer-bottom .copyright p {
  margin-bottom: 0;
  color: #f3f8f9;
}
.rs-footer .footer-bottom .copyright a {
  color: #21a7d0;
}
.rs-footer .footer-bottom .footer-logo a {
  display: inline-block;
}
.rs-footer .footer-bottom .footer-logo a img {
  max-width: 190px;
}
.rs-footer .footer-bottom .footer-social li {
  display: inline;
  margin-right: 5px;
}
.rs-footer .footer-bottom .footer-social li a {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  border-radius: 100%;
  background: #21a7d0;
  color: #ffffff;
  text-align: center;
}
.rs-footer .footer-bottom .footer-social li a:hover {
  color: #ffffff;
  background: #273c66;
}
.rs-footer .footer-bottom .footer-social li:last-child {
  margin: 0;
}
.rs-footer .footer-bottom:before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 1px;
  width: 100%;
  max-width: 1240px;
  background: rgba(255, 255, 255, 0.1);
}
.rs-footer.style6 .footer-top {
  padding: 100px 0 93px;
  margin-right: 10px; 
}
.rs-footer.style6 .footer-top .site-map li a {
  color: #e8e8e8;
}
.rs-footer.style6 .footer-top .site-map li a:hover {
  color: #21a7d0;
}
.rs-footer.style6 .footer-top .site-map li:before {
  background: #18191A !important;
}
.rs-footer.style6 .footer-top .site-map li:last-child {
  margin-bottom: 0;
}
.rs-footer.style6 .footer-top .address-widget li i:before {
  color: #f4bf00 !important;
  margin-left: 10px;
  margin-right: 10px
}
.rs-footer.style6 .footer-bottom {
  background: #18191A;
}
.rs-footer.style6 .footer-bottom .main-part {
  padding-top: 30px;
  padding-bottom: 18px;
}
.rs-footer.style6 .footer-bottom .main-part p a {
  color: #f4bf00 !important;
}
.rs-footer.style6 .footer-bottom .main-part p a:hover {
  color: #21a7d0 !important;
}
.rs-footer.style6 .footer-bottom .main-part .footer-social li a {
  color: #fff !important;
  background: #17a2b8  !important;
}
.rs-footer.style7 .footer-top {
  padding: 100px 0 93px;
}
.rs-footer.style7 .footer-top .address-widget li i:before {
  color: #21a7d0 !important;
}
.rs-footer.style8 .footer-top {
  padding: 100px 0 93px;
}
.rs-footer.style8 .footer-top .site-map li a {
  color: #e8e8e8;
}
.rs-footer.style8 .footer-top .site-map li a:hover {
  color: #ff5421;
}
.rs-footer.style8 .footer-top .site-map li:before {
  background: #ff5421 !important;
}
.rs-footer.style8 .footer-top .site-map li:last-child {
  margin-bottom: 0;
}
.rs-footer.style8 .footer-top .address-widget li i:before {
  color: #ff5421 !important;
}
.rs-footer.style8 .footer-top .address-widget li .desc a:hover {
  color: #ff5421 !important;
}
.rs-footer.style8 .footer-bottom .copyright p a {
  color: #ff5421 !important;
}
.rs-footer.style8 .footer-bottom .copyright p a:hover {
  color: #ed3600 !important;
}
.rs-footer.style8 .footer-bottom .footer-social li a {
  background: #ff5421 !important;
}
.rs-footer.orange-footer {
  background-color: #151515;
  background-image: url(assets/images/bg/footer-bg2.jpg);
}
.rs-footer.orange-footer .footer-top .widget-title {
  color: #ffffff;
}
.rs-footer.orange-footer .footer-top .site-map li a:hover {
  color: #ff5421;
}
.rs-footer.orange-footer .footer-top .site-map li:before {
  background: #ff5421;
}
.rs-footer.orange-footer .footer-top .address-widget li i:before {
  color: #ffffff;
}
.rs-footer.orange-footer .footer-top .address-widget li .desc {
  color: #ffffff;
}
.rs-footer.orange-footer .footer-top .address-widget li .desc a {
  color: #ffffff;
}
.rs-footer.orange-footer .footer-top .address-widget li .desc a:hover {
  color: #ff5421;
}
.rs-footer.orange-footer .footer-bottom .copyright a {
  color: #ff5421;
}
.rs-footer.orange-footer .footer-bottom .footer-social li a {
  background: #ff5421;
}
.rs-footer.orange-footer .footer-bottom .footer-social li a:hover {
  background: #ed3600;
}
.rs-footer.home9-style .footer-top {
  padding: 218px 0 93px;
}
.rs-footer.home9-style .footer-top .widget-title {
  color: #ffffff;
  font-size: 18px;
  line-height: 26px;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 35px;
  position: relative;
}
.rs-footer.home9-style .footer-top .widget-title:before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -5px;
  height: 2px;
  width: 40px;
  background: #f4bf00;
}
.rs-footer.home9-style .footer-top .site-map li {
  padding-left: 15px;
  position: relative;
  margin-bottom: 11px;
}
.rs-footer.home9-style .footer-top .site-map li a {
  color: #e8e8e8;
}
.rs-footer.home9-style .footer-top .site-map li a:hover {
  color: #f4bf00;
}
.rs-footer.home9-style .footer-top .site-map li:before {
  position: absolute;
  content: '';
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: #f4bf00;
}
.rs-footer.home9-style .footer-top .site-map li:last-child {
  margin-bottom: 0;
}
.rs-footer.home9-style .footer-top .address-widget li {
  position: relative;
  margin-bottom: 16px;
}
.rs-footer.home9-style .footer-top .address-widget li i {
  position: absolute;
}
.rs-footer.home9-style .footer-top .address-widget li i:before {
  color: #f4bf00;
}
.rs-footer.home9-style .footer-top .address-widget li .desc {
  padding-left: 35px;
  color: #ffffff;
}
.rs-footer.home9-style .footer-top .address-widget li .desc a {
  color: #ffffff;
}
.rs-footer.home9-style .footer-top .address-widget li .desc a:hover {
  color: #f4bf00;
}
.rs-footer.home9-style .footer-top .address-widget li:last-child {
  margin-bottom: 0;
}
.rs-footer.home9-style .footer-top.no-gap {
  padding-top: 93px;
}
.rs-footer.home9-style .footer-top .recent-post {
  display: flex;
}
.rs-footer.home9-style .footer-top .recent-post .post-img {
  width: 130px;
  float: left;
  padding-right: 15px;
}
.rs-footer.home9-style .footer-top .recent-post .post-img img {
  border-radius: 4px;
}
.rs-footer.home9-style .footer-top .recent-post .post-item .post-desc a {
  font-size: 14px;
  color: #ffffff;
  font-weight: 400;
  line-height: 22px;
  text-transform: capitalize;
}
.rs-footer.home9-style .footer-top .recent-post .post-item .post-desc a:hover {
  color: #f4bf00;
}
.rs-footer.home9-style .footer-top .recent-post .post-item .post-date {
  font-size: 13px;
  color: #ffffff;
  font-weight: 400;
}
.rs-footer.home9-style .footer-top .recent-post .post-item .post-date i {
  font-size: 13px;
  color: #f4bf00;
  padding-right: 5px;
}
.rs-footer.home9-style .footer-bottom {
  padding: 40px 0;
  background: transparent;
  position: relative;
}
.rs-footer.home9-style .footer-bottom .copyright p {
  margin-bottom: 0;
  color: #f3f8f9;
}
.rs-footer.home9-style .footer-bottom .copyright a {
  color: #f4bf00;
}
.rs-footer.home9-style .footer-bottom .copyright a:hover {
  color: #21a7d0;
}
.rs-footer.home9-style .footer-bottom .footer-logo a {
  display: inline-block;
}
.rs-footer.home9-style .footer-bottom .footer-logo a img {
  max-width: 190px;
}
.rs-footer.home9-style .footer-bottom .footer-social li {
  display: inline;
  margin-right: 5px;
}
.rs-footer.home9-style .footer-bottom .footer-social li a {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  border-radius: 100%;
  background: #f4bf00;
  color: #ffffff;
  text-align: center;
}
.rs-footer.home9-style .footer-bottom .footer-social li a:hover {
  color: #ffffff;
  background: #273c66;
}
.rs-footer.home9-style .footer-bottom .footer-social li:last-child {
  margin: 0;
}
.rs-footer.home9-style .footer-bottom:before {
  background: rgba(255, 255, 255, 0.1);
}
.rs-footer.home9-style.main-home {
  background: #151515;
}
.rs-footer.home9-style.main-home .footer-top .widget-title:before {
  background: #ff5421;
}
.rs-footer.home9-style.main-home .footer-top .footer-logo a {
  display: inline-block;
}
.rs-footer.home9-style.main-home .footer-top .footer-logo a img {
  max-width: 190px;
}
.rs-footer.home9-style.main-home .footer-top .textwidget p {
  color: #ffffff !important;
}
.rs-footer.home9-style.main-home .footer-top .footer_social li {
  display: inline-block;
  margin-right: 6px;
  color: #ffffff;
}
.rs-footer.home9-style.main-home .footer-top .footer_social li a {
  color: #ffffff;
  width: auto;
  height: auto;
  line-height: 16px;
  font-size: 16px;
  margin-right: 10px;
}
.rs-footer.home9-style.main-home .footer-top .footer_social li a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home .footer-top .site-map li a {
  color: #e8e8e8;
}
.rs-footer.home9-style.main-home .footer-top .site-map li a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home .footer-top .site-map li:before {
  background: #ff5421;
}
.rs-footer.home9-style.main-home .footer-top .address-widget li i:before {
  color: #ff5421;
}
.rs-footer.home9-style.main-home .footer-top .address-widget li .desc a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home .footer-top.no-gap {
  padding-top: 93px;
}
.rs-footer.home9-style.main-home .footer-top .recent-post .post-img img {
  border-radius: 4px;
}
.rs-footer.home9-style.main-home .footer-top .recent-post .post-item .post-desc a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home .footer-top .recent-post .post-item .post-date i {
  font-size: 13px;
  color: #ff5421;
  padding-right: 5px;
}
.rs-footer.home9-style.main-home .footer-bottom {
  padding: 40px 0;
  background: transparent;
  position: relative;
}
.rs-footer.home9-style.main-home .footer-bottom .copyright p {
  margin-bottom: 0;
  color: #f3f8f9;
}
.rs-footer.home9-style.main-home .footer-bottom .copyright a {
  color: #ffffff;
}
.rs-footer.home9-style.main-home .footer-bottom .copyright a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home .footer-bottom .copy-right-menu li {
  display: inline-block;
  position: relative;
  padding: 0px 19px 0 25px;
}
.rs-footer.home9-style.main-home .footer-bottom .copy-right-menu li a {
  color: #e8e8e8;
}
.rs-footer.home9-style.main-home .footer-bottom .copy-right-menu li a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home .footer-bottom .copy-right-menu li:before {
  display: block;
  content: "";
  position: absolute;
  font-size: 18px;
  background: #ff5421;
  left: 0;
  top: 50%;
  width: 6px;
  height: 6px;
  transform: translateY(-50%);
  border-radius: 50%;
}
.rs-footer.home9-style.main-home .footer-bottom .copy-right-menu li:first-child:before {
  display: none;
}
.rs-footer.home9-style.main-home .footer-bottom:before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 1px;
  width: 100%;
  max-width: 1240px;
  background: rgba(255, 255, 255, 0.1);
}
.rs-footer.home9-style.main-home.home13-style {
  background: url(assets/images/bg/home13/footer.jpg);
  background-repeat: no-repeat;
  background-size: cover;
}
.rs-footer.home9-style.main-home.home13-style .footer-top {
  padding: 100px 0 93px;
}
.rs-footer.home9-style.main-home.home14-style {
  background: #f9f7f8;
}
.rs-footer.home9-style.main-home.home14-style .footer-top {
  padding: 100px 0 93px;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .widget-title {
  color: #101010;
  text-transform: capitalize;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .widget-title:before {
  bottom: -8px;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .textwidget p {
  color: #333333 !important;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .footer_social li a {
  color: #101010;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .footer_social li a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .address-widget li i:before {
  color: #ff5421;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .address-widget li .desc {
  color: #101010;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .address-widget li .desc a {
  color: #505050;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .address-widget li .desc a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .site-map li a {
  color: #101010;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .site-map li a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .site-map li:before {
  background: #ff5421;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .recent-post .post-item .post-desc {
  color: #505050;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .recent-post .post-item .post-desc a {
  color: #505050;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .recent-post .post-item .post-desc a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .recent-post .post-item .post-date {
  color: #505050;
}
.rs-footer.home9-style.main-home.home14-style .footer-top .recent-post .post-item .post-date i {
  color: #ff5421;
}
.rs-footer.home9-style.main-home.home14-style .footer-bottom .copyright p {
  color: #101010;
}
.rs-footer.home9-style.main-home.home14-style .footer-bottom .copyright a {
  color: #101010;
}
.rs-footer.home9-style.main-home.home14-style .footer-bottom .copyright a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home.home14-style .footer-bottom .copy-right-menu li a {
  color: #101010;
}
.rs-footer.home9-style.main-home.home14-style .footer-bottom .copy-right-menu li a:hover {
  color: #ff5421;
}
.rs-footer.home9-style.main-home.home14-style .footer-bottom .copy-right-menu li:before {
  background: #ff5421;
}
.rs-footer.home9-style.main-home.home14-style .footer-bottom .copy-right-menu li:first-child:before {
  display: none;
}
.rs-footer.home9-style.main-home.home14-style .footer-bottom:before {
  background: #f0ecee;
}
.rs-footer.home9-style.home12-style {
  background: #d0f4e4;
}
.rs-footer.home9-style.home12-style .footer-top .widget-title {
  color: #101010;
}
.rs-footer.home9-style.home12-style .footer-top .widget-title:before {
  background: #0c8b51;
}
.rs-footer.home9-style.home12-style .footer-top .footer-logo a {
  display: inline-block;
}
.rs-footer.home9-style.home12-style .footer-top .footer-logo a img {
  max-width: 190px;
}
.rs-footer.home9-style.home12-style .footer-top .footer_social li {
  display: inline-block;
  margin-right: 6px;
  color: #333333;
}
.rs-footer.home9-style.home12-style .footer-top .footer_social li a {
  color: #333333;
  width: auto;
  height: auto;
  line-height: 16px;
  font-size: 16px;
  margin-right: 10px;
}
.rs-footer.home9-style.home12-style .footer-top .footer_social li a:hover {
  color: #0c8b51;
}
.rs-footer.home9-style.home12-style .footer-top .site-map li a {
  color: #333333;
}
.rs-footer.home9-style.home12-style .footer-top .site-map li a:hover {
  color: #0c8b51;
}
.rs-footer.home9-style.home12-style .footer-top .site-map li:before {
  background: #0c8b51;
}
.rs-footer.home9-style.home12-style .footer-top .address-widget li i:before {
  color: #0c8b51;
}
.rs-footer.home9-style.home12-style .footer-top .address-widget li .desc {
  color: #333333;
}
.rs-footer.home9-style.home12-style .footer-top .address-widget li .desc a {
  color: #333333;
}
.rs-footer.home9-style.home12-style .footer-top .address-widget li .desc a:hover {
  color: #0c8b51;
}
.rs-footer.home9-style.home12-style .footer-top.no-gap {
  padding-top: 93px;
}
.rs-footer.home9-style.home12-style .footer-top .recent-post .post-img img {
  border-radius: 4px;
}
.rs-footer.home9-style.home12-style .footer-top .recent-post .post-item .post-desc a {
  color: #333333;
}
.rs-footer.home9-style.home12-style .footer-top .recent-post .post-item .post-desc a:hover {
  color: #0c8b51;
}
.rs-footer.home9-style.home12-style .footer-top .recent-post .post-item .post-date {
  color: #333333;
}
.rs-footer.home9-style.home12-style .footer-top .recent-post .post-item .post-date i {
  font-size: 13px;
  color: #0c8b51;
  padding-right: 5px;
}
.rs-footer.home9-style.home12-style .footer-bottom {
  padding: 40px 0;
  background: transparent;
  position: relative;
}
.rs-footer.home9-style.home12-style .footer-bottom .copyright p {
  margin-bottom: 0;
  color: #333333;
}
.rs-footer.home9-style.home12-style .footer-bottom .copyright a {
  color: #333333;
}
.rs-footer.home9-style.home12-style .footer-bottom .copyright a:hover {
  color: #0c8b51;
}
.rs-footer.home9-style.home12-style .footer-bottom .copy-right-menu li {
  display: inline-block;
  position: relative;
  padding: 0px 19px 0 25px;
}
.rs-footer.home9-style.home12-style .footer-bottom .copy-right-menu li a {
  color: #333333;
}
.rs-footer.home9-style.home12-style .footer-bottom .copy-right-menu li a:hover {
  color: #0c8b51;
}
.rs-footer.home9-style.home12-style .footer-bottom .copy-right-menu li:before {
  display: block;
  content: "";
  position: absolute;
  font-size: 18px;
  background: #0c8b51;
  left: 0;
  top: 50%;
  width: 6px;
  height: 6px;
  transform: translateY(-50%);
  border-radius: 50%;
}
.rs-footer.home9-style.home12-style .footer-bottom .copy-right-menu li:first-child:before {
  display: none;
}
.rs-footer.home9-style.home12-style .footer-bottom:before {
  background: #8fcea4;
}
.rs-footer.home11-style {
  background: #4e49a1;
}
.rs-footer.home11-style .footer-top {
  padding: 100px 0 93px;
}
.rs-footer.home11-style .footer-top .site-map li a:hover {
  color: #ff5421;
}
.rs-footer.home11-style .footer-top .site-map li:before {
  background: #ff5421 !important;
}
.rs-footer.home11-style .footer-top .site-map li:last-child {
  margin-bottom: 0;
}
.rs-footer.home11-style .footer-bottom {
  padding: 40px 0;
  background: transparent;
  position: relative;
}
.rs-footer.home11-style .footer-bottom .copyright p {
  color: #ffffff;
}
.rs-footer.home11-style .footer-bottom .footer-logo a {
  display: inline-block;
}
.rs-footer.home11-style .footer-bottom .footer-logo a img {
  max-width: 190px;
}
.rs-footer.home11-style .footer-bottom .footer-social li a {
  color: #4e49a1;
  background: #ffffff;
}
.rs-footer.home11-style .footer-bottom .footer-social li a:hover {
  color: #ff5421;
}
/* -----------------------
    38. Scroll Up CSS
--------------------------*/
#scrollUp {
  text-align: center;
  bottom: 40px;
  cursor: pointer;
  display: none;
  position: fixed;
  z-index: 999;
  border-radius: 50px 50px 4px 4px;
  padding: 10px;
}
#scrollUp i {
  display: flex;
  justify-content: center;
  background: #7b7d7f;
  border-radius: 50%;
  height: 40px;
  font-size: 24px;
  font-weight: 600;
  width: 42px;
  color: #fff;
  line-height: 36px;
  transition: all 0.3s ease;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.4);
}
#scrollUp i:hover {
  background-color: #a1a2a4;
}
#scrollUp.orange-color i {
  background: #ff5421;
}
#scrollUp.orange-color i:hover {
  background-color: #ed3600;
}
#scrollUp.yellow-color i {
  background: #f4bf00;
}
#scrollUp.yellow-color i:hover {
  background-color: #c19700;
}
#scrollUp.purple-color i {
  background: #4e49a1;
}
#scrollUp.purple-color i:hover {
  background-color: #3d397e;
}
#scrollUp.green-color i {
  background: #0c8b51;
}
#scrollUp.green-color i:hover {
  background-color: #085c36;
}
/*Pulse Border Animation*/
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}
@-webkit-keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}
/*Pulse Border Animation*/
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}
@-webkit-keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}
@keyframes circle-ripple {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 84, 33, 0.3), 0 0 0 1em rgba(255, 84, 33, 0.3), 0 0 0 3em rgba(255, 84, 33, 0.3), 0 0 0 5em rgba(255, 84, 33, 0.3);
  }
  100% {
    box-shadow: 0 0 0 1em rgba(255, 84, 33, 0.3), 0 0 0 3em rgba(255, 84, 33, 0.3), 0 0 0 5em rgba(255, 84, 33, 0.3), 0 0 0 8em rgba(255, 84, 33, 0);
  }
}
@-webkit-keyframes circle-ripple {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 84, 33, 0.3), 0 0 0 1em rgba(255, 84, 33, 0.3), 0 0 0 3em rgba(255, 84, 33, 0.3), 0 0 0 5em rgba(255, 84, 33, 0.3);
  }
  100% {
    box-shadow: 0 0 0 1em rgba(255, 84, 33, 0.3), 0 0 0 3em rgba(255, 84, 33, 0.3), 0 0 0 5em rgba(255, 84, 33, 0.3), 0 0 0 8em rgba(255, 84, 33, 0);
  }
}
.up-down {
  animation: up-down 4s alternate infinite;
  -webkit-animation: up-down 4s alternate infinite;
}
.up-down-new {
  animation: up-down 8s alternate infinite;
  -webkit-animation: up-down 8s alternate infinite;
}
@keyframes up-down {
  0% {
    transform: translateY(30px);
    -webkit-transform: translateY(0);
  }
  50% {
    transform: translateY(-50px);
    -webkit-transform: translateY(-50px);
  }
  100% {
    transform: translateY(0);
    -webkit-transform: translateY(0);
  }
}
@-webkit-keyframes up-down {
  0% {
    transform: translateY(30px);
    -webkit-transform: translateY(0);
  }
  50% {
    transform: translateY(-50px);
    -webkit-transform: translateY(-50px);
  }
  100% {
    transform: translateY(0);
    -webkit-transform: translateY(0);
  }
}
.left-right {
  animation: left-right 5s cubic-bezier(0.41, 0.04, 0.03, 1.1) infinite;
  -webkit-animation: left-right 5s cubic-bezier(0.41, 0.04, 0.03, 1.1) infinite;
}
.left-right-new {
  animation: left-right 8s cubic-bezier(0.41, 0.04, 0.03, 1.1) infinite;
  -webkit-animation: left-right 8s cubic-bezier(0.41, 0.04, 0.03, 1.1) infinite;
}
@keyframes left-right {
  0% {
    transform: translatex(0);
    -webkit-transform: translatex(0);
  }
  50% {
    transform: translateY(-50px);
    -webkit-transform: translatex(-50px);
  }
  100% {
    transform: translatex(0);
    -webkit-transform: translatex(0);
  }
}
@-webkit-keyframes left-right {
  0% {
    transform: translatex(0);
    -webkit-transform: translatex(0);
  }
  50% {
    transform: translatex(-50px);
    -webkit-transform: translatex(-50px);
  }
  100% {
    transform: translatex(0);
    -webkit-transform: translatex(0);
  }
}
.spine {
  animation: spine 5s linear infinite;
  -webkit-animation: spine 5s linear infinite;
}
@keyframes spine {
  0% {
    transform: rotate(0);
    -webkit-transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
  }
}
@-webkit-keyframes spine {
  0% {
    transform: rotate(0);
    -webkit-transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
  }
}
.spine-r {
  animation: spine-r 5s linear infinite;
  -webkit-animation: spine-r 5s linear infinite;
}
@keyframes spine-r {
  0% {
    transform: rotate(0);
    -webkit-transform: rotate(0);
  }
  100% {
    transform: rotate(-360deg);
    -webkit-transform: rotate(-360deg);
  }
}
@-webkit-keyframes spine-r {
  0% {
    transform: rotate(0);
    -webkit-transform: rotate(0);
  }
  100% {
    transform: rotate(-360deg);
    -webkit-transform: rotate(-360deg);
  }
}
.spine2 {
  animation: spine 8s linear infinite;
}
@keyframes spine2 {
  from {
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
  }
  from {
    transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
  }
}
@-webkit-keyframes spine2 {
  from {
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
  }
  from {
    transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
  }
}
/* ------------------------------------
    39. Preloader CSS
---------------------------------------*/
.loader {
  background-color: #fff;
  height: 100%;
  width: 100%;
  position: fixed;
  margin-top: 0px;
  top: 0px;
  z-index: 9999999;
}
.loader .loader-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140px;
  height: 140px;
  border: 5px solid #ebebec;
  border-radius: 50%;
}
.loader .loader-container:before {
  position: absolute;
  content: "";
  display: block;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140px;
  height: 140px;
  border-top: 4px solid #21a7d0;
  border-radius: 50%;
  animation: loaderspin 1.8s infinite ease-in-out;
  -webkit-animation: loaderspin 1.8s infinite ease-in-out;
}
.loader .loader-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
}
.loader .loader-icon img {
  animation: loaderpulse alternate 900ms infinite;
}
.loader.orange-color .loader-container:before {
  border-color: #ff5421;
}
.loader.yellow-color .loader-container:before {
  border-color: #f4bf00;
}
.loader.purple-color .loader-container:before {
  border-color: #4e49a1;
}
.loader.green-color .loader-container:before {
  border-color: #0c8b51;
}
@keyframes loaderspin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@-webkit-keyframes loaderspin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@keyframes loaderpulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.2);
  }
}


::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #fff;
  opacity: 1; /* Firefox */
}

:-ms-input-placeholder { /* Internet Explorer 10-11 */
  color: #fff;
}

::-ms-input-placeholder { /* Microsoft Edge */
  color: #fff;
}


.text-white,
.text-center {
    font-family: 'DroidArabicKufiRegular';
    font-weight: normal;
    font-style: normal;
}