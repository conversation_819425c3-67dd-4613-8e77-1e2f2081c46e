@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">
        <section class="content-header">
            <h1>تفاصيل طلب الكورس</h1>
            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
                <li><a href="{{ route('dashboard.purchases.index') }}">طلبات الكورسات</a></li>
                <li class="active">تفاصيل الطلب</li>
            </ol>
        </section>

        <section class="content">

            <!-- معلومات الطالب -->
            <div class="row">
                <div class="col-md-6">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات الطالب</h3>
                        </div>
                        <div class="box-body">
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>الاسم الأول:</strong></td>
                                    <td>{{ $purchase->first_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم الأخير:</strong></td>
                                    <td>{{ $purchase->last_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>البريد الإلكتروني:</strong></td>
                                    <td>{{ $purchase->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>رقم الهاتف:</strong></td>
                                    <td>{{ $purchase->phone }}</td>
                                </tr>
                                <tr>
                                    <td><strong>اسم الكورس:</strong></td>
                                    <td><span class="label label-info">{{ $purchase->name_course }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ التسجيل:</strong></td>
                                    <td>{{ $purchase->created_at->format('Y-m-d H:i') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>حالة الطلب:</strong></td>
                                    <td>
                                        @if($purchase->status == '1')
                                            <span class="label label-success">مقبول</span>
                                        @elseif($purchase->status == '0')
                                            <span class="label label-warning">في الانتظار</span>
                                        @else
                                            <span class="label label-danger">مرفوض</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- معلومات الدفع -->
                <div class="col-md-6">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات الدفع</h3>
                        </div>
                        <div class="box-body">
                            @php
                                // تحميل العلاقة مع الدورة
                                $purchase->load('course');

                                // جلب المعلومات من النموذج
                                $coursePrice = $purchase->getCoursePrice();
                                $totalPaid = $purchase->paid_amount;
                                $remaining = $purchase->getRemainingAmount();
                                $progress = $purchase->getPaymentProgress();
                                $isFullyPaid = $purchase->isFullyPaid();
                            @endphp

                            <div class="row">
                                <div class="col-xs-6">
                                    <div class="info-box bg-blue">
                                        <span class="info-box-icon"><i class="fa fa-money"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">سعر الكورس</span>
                                            <span class="info-box-number">{{ number_format($coursePrice, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="info-box bg-green">
                                        <span class="info-box-icon"><i class="fa fa-check"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">المدفوع</span>
                                            <span class="info-box-number">{{ number_format($totalPaid, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-xs-6">
                                    <div class="info-box bg-yellow">
                                        <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">المتبقي</span>
                                            <span class="info-box-number">{{ number_format($remaining, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="info-box bg-red">
                                        <span class="info-box-icon"><i class="fa fa-percent"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">التقدم</span>
                                            <span class="info-box-number">{{ $progress }}%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- شريط التقدم -->
                            <div class="progress progress-lg">
                                <div class="progress-bar progress-bar-{{ $progress >= 100 ? 'success' : ($progress >= 50 ? 'warning' : 'danger') }}"
                                     style="width: {{ $progress }}%">
                                    {{ $progress }}%
                                </div>
                            </div>

                            @if($isFullyPaid)
                                <div class="alert alert-success">
                                    <i class="fa fa-check-circle"></i> تم دفع المبلغ بالكامل!
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- صورة الفاتورة -->
            @if($purchase->bill_image)
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">صورة الفاتورة المرفقة</h3>
                        </div>
                        <div class="box-body text-center">
                            <img src="{{ asset('uploads/purchases/' . $purchase->bill_image) }}"
                                 alt="صورة الفاتورة"
                                 class="img-responsive"
                                 style="max-height: 400px; margin: 0 auto;">
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- الإجراءات -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title">الإجراءات المتاحة</h3>
                        </div>
                        <div class="box-body">
                            <div class="btn-group" style="margin-right: 10px;">
                                <a href="{{ route('dashboard.purchases.edit', $purchase) }}" class="btn btn-primary">
                                    <i class="fa fa-edit"></i> تعديل الطلب
                                </a>
                            </div>

                            @if(!$isFullyPaid)
                            <div class="btn-group" style="margin-right: 10px;">
                                <a href="{{ route('dashboard.add-payment-simple.show', $purchase) }}" class="btn btn-success btn-lg">
                                    <i class="fa fa-plus"></i> إضافة دفعة
                                </a>
                            </div>
                            @endif

                            <div class="btn-group" style="margin-right: 10px;">
                                <a href="{{ route('dashboard.student-payments.show', $purchase) }}" class="btn btn-info">
                                    <i class="fa fa-list"></i> عرض تفاصيل الدفعات
                                </a>
                            </div>

                            @if($purchase->status == '0')
                            <div class="btn-group" style="margin-right: 10px;">
                                <button type="button" class="btn btn-success" onclick="updateStatus('{{ $purchase->id }}', '1')">
                                    <i class="fa fa-check"></i> قبول الطلب
                                </button>
                                <button type="button" class="btn btn-danger" onclick="updateStatus('{{ $purchase->id }}', '2')">
                                    <i class="fa fa-times"></i> رفض الطلب
                                </button>
                            </div>
                            @endif

                            <div class="btn-group">
                                <a href="{{ route('dashboard.purchases.index') }}" class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> العودة للقائمة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </section>
    </div>

@endsection

@push('scripts')
<script>
function updateStatus(id, status) {
    var statusText = status == '1' ? 'قبول' : 'رفض';
    if (confirm('هل أنت متأكد من ' + statusText + ' هذا الطلب؟')) {
        // يمكن إضافة AJAX request هنا لتحديث الحالة
        alert('سيتم تطوير هذه الميزة قريباً');
    }
}
</script>
@endpush

@push('styles')
<style>
.info-box {
    margin-bottom: 15px;
}

.progress-lg {
    height: 30px;
    margin-bottom: 20px;
}

.progress-lg .progress-bar {
    font-size: 16px;
    line-height: 30px;
}

.btn-group {
    margin-bottom: 10px;
}
</style>
@endpush
