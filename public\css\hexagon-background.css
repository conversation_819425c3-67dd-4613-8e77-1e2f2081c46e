/* ===================================
   خلفية الأشكال السداسية - أكاديمية Leaders Vision
   Hexagon Background - Leaders Vision Academy
   =================================== */

/* الخلفية الأساسية مع الأشكال السداسية */
.hexagon-background {
    position: relative;
    background: linear-gradient(135deg, #1e293b 0%, #475569 30%, #4ade80 70%, #14b8a6 100%);
    overflow: hidden;
}

.hexagon-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(74, 222, 128, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(20, 184, 166, 0.1) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagon" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,2 18,7 18,13 10,18 2,13 2,7" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagon)"/></svg>');
    background-size: 
        300px 300px,
        400px 400px,
        50px 50px;
    background-position: 
        0% 0%,
        100% 100%,
        0 0;
    animation: backgroundMove 20s ease-in-out infinite;
    z-index: 1;
}

.hexagon-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(74, 222, 128, 0.05) 50%, transparent 70%);
    z-index: 2;
}

.hexagon-background > * {
    position: relative;
    z-index: 3;
}

/* حركة الخلفية */
@keyframes backgroundMove {
    0%, 100% {
        background-position: 
            0% 0%,
            100% 100%,
            0 0;
    }
    25% {
        background-position: 
            100% 0%,
            0% 100%,
            25px 25px;
    }
    50% {
        background-position: 
            100% 100%,
            0% 0%,
            50px 0px;
    }
    75% {
        background-position: 
            0% 100%,
            100% 0%,
            25px -25px;
    }
}

/* خلفية متدرجة للأقسام */
.section-green-gray {
    background: linear-gradient(135deg, #4ade80 0%, #475569 50%, #1e293b 100%);
    position: relative;
}

.section-green-gray::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="dots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="60" height="60" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.section-green-gray > * {
    position: relative;
    z-index: 2;
}

/* خلفية للبطاقات */
.card-green-glass {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(74, 222, 128, 0.2) !important;
    border-radius: 15px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.card-green-glass:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(74, 222, 128, 0.4) !important;
    box-shadow: 0 12px 40px rgba(74, 222, 128, 0.2) !important;
    transform: translateY(-5px) !important;
}

/* تأثيرات الأشكال الهندسية */
.geometric-shapes {
    position: relative;
    overflow: hidden;
}

.geometric-shapes::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(74, 222, 128, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(71, 85, 105, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(20, 184, 166, 0.1) 0%, transparent 50%);
    animation: rotateShapes 30s linear infinite;
    z-index: 1;
}

@keyframes rotateShapes {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* خلفية للهيدر */
.header-green-gradient {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(71, 85, 105, 0.95) 100%) !important;
    backdrop-filter: blur(10px) !important;
    border-bottom: 2px solid #4ade80 !important;
}

/* خلفية للفوتر */
.footer-green-gradient {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
    position: relative;
}

.footer-green-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(74,222,128,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.footer-green-gradient > * {
    position: relative;
    z-index: 2;
}

/* تأثيرات الجسيمات المتحركة */
.floating-particles {
    position: relative;
    overflow: hidden;
}

.floating-particles::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(2px 2px at 20px 30px, rgba(74, 222, 128, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(20, 184, 166, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(134, 239, 172, 0.3), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(74, 222, 128, 0.3), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(20, 184, 166, 0.3), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: floatParticles 15s linear infinite;
    z-index: 1;
}

@keyframes floatParticles {
    0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(-100px);
    }
}

/* خلفية متموجة */
.wave-background {
    background: linear-gradient(135deg, #1e293b 0%, #475569 50%, #4ade80 100%);
    position: relative;
    overflow: hidden;
}

.wave-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="rgba(74,222,128,0.1)"/><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="rgba(74,222,128,0.1)"/><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="rgba(74,222,128,0.1)"/></svg>') repeat-x;
    background-size: 1200px 120px;
    animation: waveMove 10s ease-in-out infinite;
    z-index: 1;
}

@keyframes waveMove {
    0%, 100% {
        transform: translateX(0px);
    }
    50% {
        transform: translateX(-50px);
    }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .hexagon-background::before {
        background-size: 
            200px 200px,
            250px 250px,
            30px 30px;
    }
    
    .floating-particles::after {
        background-size: 150px 75px;
    }
    
    .wave-background::before {
        background-size: 800px 80px;
    }
}

/* تأثيرات إضافية للتفاعل */
.interactive-bg {
    transition: all 0.3s ease;
}

.interactive-bg:hover {
    transform: scale(1.02);
}

.glow-effect {
    box-shadow: 0 0 20px rgba(74, 222, 128, 0.3);
    transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
    box-shadow: 0 0 30px rgba(74, 222, 128, 0.5);
}
