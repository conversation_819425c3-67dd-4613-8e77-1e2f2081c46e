<aside class="main-sidebar">

    <section class="sidebar">

        <div class="user-panel">
            <div class="pull-left image">
                <img src="<?php echo e(auth()->user()->image_path); ?>" class="img-circle" alt="User Image">
            </div>
            <div class="pull-left info">
                <p><?php echo e(auth()->user()->name); ?></p>
            </div>
        </div>

        <ul class="sidebar-menu" data-widget="tree">
            <li><a href="<?php echo e(route('dashboard.welcome')); ?>"><i class="fa fa-th"></i><span><?php echo app('translator')->get('dashboard.dashboard'); ?></span></a></li>

            <?php if(auth()->user()->hasPermission('users_read')): ?>
                <li><a href="<?php echo e(route('dashboard.users.index')); ?>"><i class="fa fa-th"></i><span><?php echo app('translator')->get('dashboard.users'); ?></span></a></li>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('categories_read')): ?>
                <li><a href="<?php echo e(route('dashboard.categories.index')); ?>"><i class="fa fa-th"></i><span><?php echo app('translator')->get('dashboard.categories'); ?></span></a></li>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('courses_read')): ?>
                <li><a href="<?php echo e(route('dashboard.courses.index')); ?>"><i class="fa fa-graduation-cap"></i><span>الدورات</span></a></li>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('certificates_read')): ?>
                <li><a href="<?php echo e(route('dashboard.certificates.index')); ?>"><i class="fa fa-th"></i><span><?php echo app('translator')->get('home.certificates'); ?></span></a></li>
            <?php endif; ?>

            
                <li><a href="<?php echo e(route('dashboard.purchases.index')); ?>"><i class="fa fa-shopping-cart"></i><span>طلبات الدورات</span></a></li>
            

            
            <li><a href="<?php echo e(route('dashboard.payments.simple')); ?>"><i class="fa fa-money"></i><span>اشتراكات الطلاب</span></a></li>
            <li><a href="<?php echo e(route('dashboard.subscriptions')); ?>"><i class="fa fa-dashboard"></i><span>متابعة الاشتراكات</span></a></li>
            <li><a href="<?php echo e(route('dashboard.simple-payment-tracking')); ?>"><i class="fa fa-credit-card"></i><span>متابعة دفعات المشتركين</span></a></li>

            <?php if(auth()->user()->hasPermission('coaches_read')): ?>
                <li><a href="<?php echo e(route('dashboard.coaches.index')); ?>"><i class="fa fa-th"></i><span>المدربين</span></a></li>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('posts_read')): ?>
                <li><a href="<?php echo e(route('dashboard.posts.index')); ?>"><i class="fa fa-th"></i><span> <?php echo app('translator')->get('dashboard.posts'); ?></span></a></li>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('advisoryServices_read')): ?>
                <li><a href="<?php echo e(route('dashboard.advisoryServices.index')); ?>"><i class="fa fa-th"></i><span> <?php echo app('translator')->get('dashboard.advisoryServices'); ?></span></a></li>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('settings_read')): ?>
                <li class="treeview" style="height: auto;">
                  <a href="#">
                    <i class="fa fa-gear"></i> <span>الاعدادات</span>
                    <span class="pull-right-container">
                      <i class="fa fa-angle-left pull-right"></i>
                    </span>
                  </a>
                  <ul class="treeview-menu" style="display: none;">
                    <li><a href="<?php echo e(route('dashboard.institution.index')); ?>"><i class="fa fa-university"></i> معلومات المؤسسة</a></li>
                    <li><a href="<?php echo e(route('dashboard.about_index')); ?>"><i class="fa fa-circle-o"></i> عن الاكادميه</a></li>
                    <li><a href="<?php echo e(route('dashboard.links_index')); ?>"><i class="fa fa-circle-o"></i> روابط التواصل</a></li>
                    <li><a href="<?php echo e(route('dashboard.title_index')); ?>"><i class="fa fa-circle-o"></i> العناوين</a></li>
                    <li><a href="<?php echo e(route('dashboard.founder',1)); ?>"><i class="fa fa-circle-o"></i> المؤسس</a></li>
                  </ul>
                </li>
            <?php endif; ?>
        </ul>

    </section>

</aside>

<?php /**PATH C:\xampp\htdocs\Academy\resources\views/layouts/dashboard/_aside.blade.php ENDPATH**/ ?>