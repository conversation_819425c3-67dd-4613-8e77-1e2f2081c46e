@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">
        <section class="content-header">
            <h1>دفعات الطالب</h1>
            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
                <li><a href="{{ route('dashboard.purchases.index') }}">طلبات الكورسات</a></li>
                <li class="active">دفعات الطالب</li>
            </ol>
        </section>

        <section class="content">

            <!-- معلومات الطالب -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات الطالب</h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>اسم الطالب:</strong>
                                    <p>{{ $purchase->first_name }} {{ $purchase->last_name }}</p>
                                </div>
                                <div class="col-md-3">
                                    <strong>البريد الإلكتروني:</strong>
                                    <p>{{ $purchase->email }}</p>
                                </div>
                                <div class="col-md-3">
                                    <strong>رقم الهاتف:</strong>
                                    <p>{{ $purchase->phone }}</p>
                                </div>
                                <div class="col-md-3">
                                    <strong>الدورة:</strong>
                                    <p><span class="label label-info">{{ $purchase->name_course ?: ($purchase->course ? $purchase->course->name : 'غير محدد') }}</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الدفع -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">ملخص الدفعات</h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="info-box bg-blue">
                                        <span class="info-box-icon"><i class="fa fa-money"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">سعر الدورة</span>
                                            <span class="info-box-number">{{ number_format($coursePrice, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-green">
                                        <span class="info-box-icon"><i class="fa fa-check"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">المدفوع</span>
                                            <span class="info-box-number">{{ number_format($totalPaid, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-yellow">
                                        <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">المتبقي</span>
                                            <span class="info-box-number">{{ number_format($remaining, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-red">
                                        <span class="info-box-icon"><i class="fa fa-percent"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">التقدم</span>
                                            <span class="info-box-number">{{ $progress }}%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- شريط التقدم -->
                            <div class="progress progress-lg">
                                <div class="progress-bar progress-bar-{{ $progress >= 100 ? 'success' : ($progress >= 50 ? 'warning' : 'danger') }}"
                                     style="width: {{ $progress }}%">
                                    {{ $progress }}%
                                </div>
                            </div>

                            @if($isFullyPaid)
                                <div class="alert alert-success">
                                    <i class="fa fa-check-circle"></i> تم دفع المبلغ بالكامل! الطالب مؤهل للدورة.
                                </div>
                            @elseif($totalPaid > 0)
                                <div class="alert alert-warning">
                                    <i class="fa fa-clock-o"></i> تم دفع جزء من المبلغ. المتبقي: {{ number_format($remaining, 0) }} دج
                                </div>
                            @else
                                <div class="alert alert-danger">
                                    <i class="fa fa-warning"></i> لم يتم دفع أي مبلغ بعد.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الدفعات -->
            <div class="row">
                <div class="col-md-8">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">تفاصيل الدفعات</h3>
                        </div>
                        <div class="box-body">
                            @if($payments->count() > 0)
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>المبلغ</th>
                                            <th>تاريخ الدفع</th>
                                            <th>طريقة الدفع</th>
                                            <th>الحالة</th>
                                            <th>ملاحظات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($payments as $payment)
                                            <tr>
                                                <td>{{ $payment->installment_number }}</td>
                                                <td>{{ number_format($payment->amount, 0) }} دج</td>
                                                <td>
                                                    @if($payment->paid_date)
                                                        {{ $payment->paid_date->format('Y-m-d') }}
                                                    @else
                                                        <span class="text-muted">لم يدفع بعد</span>
                                                    @endif
                                                </td>
                                                <td>{{ $payment->payment_method ?: 'غير محدد' }}</td>
                                                <td>
                                                    <span class="label label-{{ $payment->status == 'paid' ? 'success' : ($payment->status == 'pending' ? 'warning' : 'danger') }}">
                                                        @if($payment->status == 'paid')
                                                            مدفوع
                                                        @elseif($payment->status == 'pending')
                                                            في الانتظار
                                                        @elseif($payment->status == 'overdue')
                                                            متأخر
                                                        @else
                                                            {{ $payment->status }}
                                                        @endif
                                                    </span>
                                                </td>
                                                <td>
                                                    {{ $payment->notes }}
                                                    @if($payment->transaction_id)
                                                        <br><small class="text-muted">رقم المعاملة: {{ $payment->transaction_id }}</small>
                                                    @endif
                                                    @if($payment->due_date)
                                                        <br><small class="text-info">تاريخ الاستحقاق: {{ $payment->due_date->format('Y-m-d') }}</small>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            @else
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle"></i> لا توجد دفعات مسجلة لهذا الطالب بعد.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- الإجراءات -->
                <div class="col-md-4">
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title">الإجراءات المتاحة</h3>
                        </div>
                        <div class="box-body">
                            @if(!$isFullyPaid)
                                <a href="{{ route('dashboard.add-payment-simple.show', $purchase) }}" class="btn btn-success btn-block">
                                    <i class="fa fa-plus"></i> إضافة دفعة جديدة
                                </a>
                                <br>
                            @endif

                            <a href="{{ route('dashboard.purchases.show', $purchase) }}" class="btn btn-primary btn-block">
                                <i class="fa fa-eye"></i> عرض تفاصيل الطلب
                            </a>

                            <a href="{{ route('dashboard.purchases.edit', $purchase) }}" class="btn btn-info btn-block">
                                <i class="fa fa-edit"></i> تعديل الطلب
                            </a>

                            <a href="{{ route('dashboard.simple-payment-tracking') }}" class="btn btn-default btn-block">
                                <i class="fa fa-list"></i> متابعة جميع الدفعات
                            </a>

                            <a href="{{ route('dashboard.purchases.index') }}" class="btn btn-default btn-block">
                                <i class="fa fa-arrow-left"></i> العودة للطلبات
                            </a>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="box box-default">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات إضافية</h3>
                        </div>
                        <div class="box-body">
                            <p><strong>تاريخ التسجيل:</strong><br>{{ $purchase->created_at->format('Y-m-d H:i') }}</p>

                            <p><strong>آخر تحديث:</strong><br>{{ $purchase->updated_at->format('Y-m-d H:i') }}</p>

                            <p><strong>حالة الطلب:</strong><br>
                                @if($purchase->status == '1')
                                    <span class="label label-success">مفعل</span>
                                @else
                                    <span class="label label-warning">غير مفعل</span>
                                @endif
                            </p>

                            <p><strong>حالة الدفع:</strong><br>
                                @if($isFullyPaid)
                                    <span class="label label-success">مدفوع بالكامل</span>
                                @elseif($totalPaid > 0)
                                    <span class="label label-warning">دفع جزئي</span>
                                @else
                                    <span class="label label-danger">لم يدفع</span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>

        </section>
    </div>

@endsection

@push('styles')
<style>
.info-box {
    margin-bottom: 15px;
}

.progress-lg {
    height: 30px;
    margin-bottom: 20px;
}

.progress-lg .progress-bar {
    font-size: 16px;
    line-height: 30px;
}

.btn-block {
    margin-bottom: 10px;
}
</style>
@endpush
