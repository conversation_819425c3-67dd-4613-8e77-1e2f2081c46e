<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PurchasePayment extends Model
{
    protected $fillable = [
        'purchase_id',
        'amount',
        'payment_date',
        'payment_method',
        'receipt_number',
        'receipt_image',
        'notes',
        'received_by',
        'status',
        'installment_number'
    ];

    protected $dates = [
        'payment_date'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'date'
    ];

    // العلاقات
    public function purchase()
    {
        return $this->belongsTo(Purchase::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeVerified($query)
    {
        return $query->where('status', 'verified');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('payment_date', Carbon::today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('payment_date', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('payment_date', Carbon::now()->month)
                    ->whereYear('payment_date', Carbon::now()->year);
    }

    // Accessors
    public function getStatusArabicAttribute()
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'verified' => 'مؤكد',
            'rejected' => 'مرفوض'
        ];

        return $statuses[$this->status] ?? 'غير محدد';
    }

    public function getPaymentMethodArabicAttribute()
    {
        $methods = [
            'cash' => 'نقداً',
            'bank_transfer' => 'تحويل بنكي',
            'credit_card' => 'بطاقة ائتمان',
            'mobile_payment' => 'دفع عبر الهاتف',
            'check' => 'شيك',
            'other' => 'أخرى'
        ];

        return $methods[$this->payment_method] ?? $this->payment_method;
    }

    public function getReceiptImagePathAttribute()
    {
        if (!$this->receipt_image) return null;
        return asset('uploads/receipts/' . $this->receipt_image);
    }

    // Methods
    public function verify($receivedBy = null)
    {
        $this->status = 'verified';
        $this->received_by = $receivedBy;
        $this->save();
        
        // تحديث إجمالي المدفوع في جدول purchases
        $this->updatePurchasePaidAmount();
        
        return $this;
    }

    public function reject($reason = null)
    {
        $this->status = 'rejected';
        if ($reason) {
            $this->notes = $reason;
        }
        $this->save();
        
        // تحديث إجمالي المدفوع في جدول purchases
        $this->updatePurchasePaidAmount();
        
        return $this;
    }

    public function updatePurchasePaidAmount()
    {
        $purchase = $this->purchase;
        if ($purchase) {
            $totalPaid = self::where('purchase_id', $purchase->id)
                            ->where('status', 'verified')
                            ->sum('amount');
            
            $purchase->paid_amount = $totalPaid;
            
            // تحديث حالة الطلب إذا اكتمل الدفع
            if ($purchase->isFullyPaid()) {
                $purchase->status = '1';
            }
            
            $purchase->save();
        }
    }

    // Static methods
    public static function getNextInstallmentNumber($purchaseId)
    {
        $lastInstallment = self::where('purchase_id', $purchaseId)
                              ->orderBy('installment_number', 'desc')
                              ->first();
        
        return $lastInstallment ? $lastInstallment->installment_number + 1 : 1;
    }

    public static function getTotalPaidForPurchase($purchaseId)
    {
        return self::where('purchase_id', $purchaseId)
                  ->where('status', 'verified')
                  ->sum('amount');
    }

    public static function createPayment($purchaseId, $amount, $paymentMethod, $receiptNumber = null, $receiptImage = null, $notes = null)
    {
        $installmentNumber = self::getNextInstallmentNumber($purchaseId);
        
        $payment = self::create([
            'purchase_id' => $purchaseId,
            'amount' => $amount,
            'payment_date' => Carbon::now(),
            'payment_method' => $paymentMethod,
            'receipt_number' => $receiptNumber,
            'receipt_image' => $receiptImage,
            'notes' => $notes,
            'received_by' => auth()->user()->name ?? 'النظام',
            'status' => 'verified',
            'installment_number' => $installmentNumber
        ]);

        // تحديث إجمالي المدفوع
        $payment->updatePurchasePaidAmount();

        return $payment;
    }
}
