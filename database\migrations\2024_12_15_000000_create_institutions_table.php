<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInstitutionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('institutions', function (Blueprint $table) {
            $table->id();
            
            // معلومات أساسية
            $table->string('name_ar')->default('أكاديمية Leaders Vision')->comment('اسم المؤسسة بالعربية');
            $table->string('name_en')->default('Leaders Vision Academy')->comment('اسم المؤسسة بالإنجليزية');
            $table->string('name_fr')->nullable()->comment('اسم المؤسسة بالفرنسية');
            
            // الوصف والشعار
            $table->text('description_ar')->default('أكاديمية متخصصة في التكوينات المهنية والدراسات الاقتصادية')->comment('وصف المؤسسة');
            $table->string('slogan_ar')->default('نصنع قادة المستقبل بالعلم والتقنية')->comment('الشعار بالعربية');
            $table->string('slogan_en')->default('Building Future Leaders with Science and Technology')->comment('الشعار بالإنجليزية');
            
            // معلومات الاتصال
            $table->string('phone_1')->default('**********')->comment('رقم الهاتف الأول');
            $table->string('phone_2')->default('0665657400')->comment('رقم الهاتف الثاني');
            $table->string('email')->default('<EMAIL>')->comment('البريد الإلكتروني');
            $table->string('website')->default('www.leadersvision.academy')->comment('الموقع الإلكتروني');
            
            // العنوان
            $table->string('address_ar')->default('عوين زريقة، شارع ب رقم 16')->comment('العنوان بالعربية');
            $table->string('address_en')->default('Aouin Zrika Street B No. 16')->comment('العنوان بالإنجليزية');
            $table->string('city')->default('برج بوعريريج')->comment('المدينة');
            $table->string('state')->default('برج بوعريريج')->comment('الولاية');
            $table->string('country')->default('الجزائر')->comment('البلد');
            $table->string('postal_code')->nullable()->comment('الرمز البريدي');
            
            // الموقع الجغرافي (برج بوعريريج)
            $table->decimal('latitude', 10, 8)->default(36.0731)->comment('خط العرض');
            $table->decimal('longitude', 11, 8)->default(4.7617)->comment('خط الطول');
            
            // وسائل التواصل الاجتماعي
            $table->string('facebook')->default('https://www.facebook.com/Leadersvisionacademy')->comment('رابط فيسبوك');
            $table->string('instagram')->nullable()->comment('رابط انستغرام');
            $table->string('twitter')->nullable()->comment('رابط تويتر');
            $table->string('linkedin')->nullable()->comment('رابط لينكد إن');
            $table->string('youtube')->nullable()->comment('رابط يوتيوب');
            $table->string('whatsapp')->default('**********')->comment('رقم واتساب');
            
            // معلومات إضافية
            $table->year('established_year')->default(2024)->comment('سنة التأسيس');
            $table->string('license_number')->nullable()->comment('رقم الترخيص');
            $table->string('tax_number')->nullable()->comment('الرقم الضريبي');
            $table->string('commercial_register')->nullable()->comment('السجل التجاري');
            
            // العملة الجزائرية
            $table->string('currency', 3)->default('DZD')->comment('العملة - الدينار الجزائري');
            $table->string('currency_symbol', 10)->default('د.ج')->comment('رمز العملة');
            $table->string('currency_name_ar')->default('دينار جزائري')->comment('اسم العملة بالعربية');
            $table->string('currency_name_en')->default('Algerian Dinar')->comment('اسم العملة بالإنجليزية');
            
            // الشعار والصور
            $table->string('logo_path')->default('images/logo.png')->comment('مسار الشعار');
            $table->string('cover_image_path')->nullable()->comment('مسار صورة الغلاف');
            $table->json('gallery')->nullable()->comment('معرض الصور');
            
            // نوع المؤسسة
            $table->string('institution_type')->default('مدرسة خاصة')->comment('نوع المؤسسة');
            $table->string('specialization')->default('التكوينات المهنية والدراسات الاقتصادية')->comment('التخصص');
            
            // الخدمات المقدمة
            $table->json('services')->default(json_encode([
                'تكوينات مهنية متخصصة',
                'ورشات تطبيقية',
                'دراسات الجدوى',
                'مرافقة المشاريع'
            ]))->comment('الخدمات المقدمة');
            
            // أوقات العمل
            $table->json('working_hours')->default(json_encode([
                'السبت' => '08:00-17:00',
                'الأحد' => '08:00-17:00',
                'الاثنين' => '08:00-17:00',
                'الثلاثاء' => '08:00-17:00',
                'الأربعاء' => '08:00-17:00',
                'الخميس' => '08:00-17:00',
                'الجمعة' => 'مغلق'
            ]))->comment('أوقات العمل');
            
            // الإحصائيات
            $table->integer('students_count')->default(0)->comment('عدد الطلاب');
            $table->integer('courses_count')->default(0)->comment('عدد الدورات');
            $table->integer('trainers_count')->default(0)->comment('عدد المدربين');
            $table->integer('graduates_count')->default(0)->comment('عدد الخريجين');
            
            // الحالة
            $table->boolean('is_active')->default(true)->comment('حالة المؤسسة');
            $table->boolean('is_featured')->default(true)->comment('مؤسسة مميزة');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('institutions');
    }
}
