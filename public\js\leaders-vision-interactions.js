/**
 * نموذج أكاديمية Leaders Vision - التفاعلات
 * Leaders Vision Academy Model - Interactions
 * أكاديمية Leaders Vision - Leaders Vision Academy
 */

$(document).ready(function() {

    // تأثيرات العد التصاعدي للإحصائيات
    function animateCounters() {
        $('.stat-number').each(function() {
            const $this = $(this);
            const countTo = parseInt($this.text().replace(/[^0-9]/g, ''));
            const suffix = $this.text().replace(/[0-9]/g, '');

            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum) + suffix);
                },
                complete: function() {
                    $this.text(countTo + suffix);
                }
            });
        });
    }

    // تشغيل العد التصاعدي عند الوصول للقسم
    function initCounterAnimation() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        const statsSection = document.querySelector('.banner-stats');
        if (statsSection) {
            observer.observe(statsSection);
        }
    }

    // تأثيرات الظهور التدريجي
    function initScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.1 });

        // العناصر التي تحتاج تأثيرات ظهور
        const animatedElements = document.querySelectorAll(
            '.vision-mission-card, .goal-card, .category-card, .partner-card, .cert-badge, .stat-card'
        );

        animatedElements.forEach(el => {
            el.classList.add('animate-element');
            observer.observe(el);
        });
    }

    // تأثيرات التحويم المتقدمة
    function initHoverEffects() {
        // تأثير التحويم للبطاقات
        $('.vision-mission-card, .goal-card, .category-card').hover(
            function() {
                $(this).addClass('floating');
            },
            function() {
                $(this).removeClass('floating');
            }
        );

        // تأثير التحويم للشراكات
        $('.partner-card').hover(
            function() {
                $(this).find('.partner-logo i').addClass('pulsing');
            },
            function() {
                $(this).find('.partner-logo i').removeClass('pulsing');
            }
        );

        // تأثير التحويم للشهادات
        $('.cert-badge').hover(
            function() {
                $(this).find('i').addClass('rotating');
            },
            function() {
                $(this).find('i').removeClass('rotating');
            }
        );
    }

    // تأثيرات الأزرار المتقدمة
    function initButtonEffects() {
        $('.btn-primary-custom, .btn-warning').on('mouseenter', function() {
            $(this).addClass('btn-glow');
        }).on('mouseleave', function() {
            $(this).removeClass('btn-glow');
        });

        // تأثير النقر
        $('.btn').on('click', function(e) {
            const ripple = $('<span class="ripple"></span>');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.css({
                width: size,
                height: size,
                left: x,
                top: y
            });

            $(this).append(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }

    // تحسين التنقل السلس
    function initSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();

            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 1000, 'easeInOutQuart');
            }
        });
    }

    // تأثيرات الشعار
    function initLogoEffects() {
        $('.logo-image').on('mouseenter', function() {
            $(this).addClass('logo-spin');
        }).on('mouseleave', function() {
            $(this).removeClass('logo-spin');
        });
    }

    // تأثيرات الخلفية التفاعلية
    function initBackgroundEffects() {
        // تأثير الجسيمات للخلفية
        if (typeof particlesJS !== 'undefined') {
            particlesJS('particles-js', {
                particles: {
                    number: { value: 50 },
                    color: { value: '#ffffff' },
                    opacity: { value: 0.1 },
                    size: { value: 3 },
                    move: {
                        enable: true,
                        speed: 1,
                        direction: 'none',
                        random: true
                    }
                }
            });
        }
    }

    // تحسين الأداء
    function initPerformanceOptimizations() {
        // تحميل الصور بشكل تدريجي
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    // تأثيرات الكتابة المتحركة
    function initTypingEffect() {
        const subtitle = $('.banner-subtitle');
        if (subtitle.length) {
            const text = subtitle.text();
            subtitle.text('');

            let i = 0;
            const typeWriter = () => {
                if (i < text.length) {
                    subtitle.text(subtitle.text() + text.charAt(i));
                    i++;
                    setTimeout(typeWriter, 100);
                }
            };

            setTimeout(typeWriter, 1000);
        }
    }

    // تهيئة جميع التأثيرات
    function initAllEffects() {
        initCounterAnimation();
        initScrollAnimations();
        initHoverEffects();
        initButtonEffects();
        initSmoothScrolling();
        initLogoEffects();
        initBackgroundEffects();
        initPerformanceOptimizations();
        initTypingEffect();

        console.log('✅ تم تحميل نموذج أكاديمية Leaders Vision بنجاح');
    }

    // تشغيل التأثيرات
    initAllEffects();

    // إضافة أنماط CSS للتأثيرات
    const styles = `
        <style>
        .animate-element {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .btn-glow {
            box-shadow: 0 0 20px rgba(245, 158, 11, 0.6);
        }

        .logo-spin {
            animation: logoSpin 0.5s ease;
        }

        .rotating {
            animation: rotate 1s linear infinite;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: rippleEffect 0.6s linear;
            pointer-events: none;
        }

        @keyframes logoSpin {
            0% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(180deg); }
            100% { transform: scale(1) rotate(360deg); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes rippleEffect {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        </style>
    `;

    $('head').append(styles);
});

// تحسين الأداء عند تغيير حجم النافذة
$(window).on('resize', debounce(function() {
    // إعادة حساب المواضع والأحجام
    console.log('تم تحديث التخطيط للشاشة الجديدة');
}, 250));

// دالة تأخير التنفيذ
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
