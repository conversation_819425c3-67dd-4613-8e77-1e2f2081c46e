/**
 * ضمان الجودة والتحقق من الأداء
 * Quality Assurance and Performance Monitoring
 * أكاديمية Leaders Vision - Leaders Vision Academy
 */

$(document).ready(function() {

    // تشغيل فحوصات الجودة
    initQualityChecks();

    // مراقبة الأداء
    monitorPerformance();

    // التحقق من إمكانية الوصول
    checkAccessibility();

    // مراقبة الأخطاء
    monitorErrors();

});

/**
 * تشغيل فحوصات الجودة الأساسية
 */
function initQualityChecks() {

    // التحقق من تحميل الخطوط
    checkFontsLoaded();

    // التحقق من الصور
    checkImagesLoaded();

    // التحقق من النماذج
    validateForms();

    // التحقق من الروابط
    checkLinks();

    console.log('✅ فحوصات الجودة مكتملة');
}

/**
 * التحقق من تحميل الخطوط
 */
function checkFontsLoaded() {
    if (document.fonts && document.fonts.ready) {
        document.fonts.ready.then(function() {
            console.log('✅ تم تحميل جميع الخطوط بنجاح');

            // إضافة فئة للإشارة إلى اكتمال تحميل الخطوط
            document.body.classList.add('fonts-loaded');
        });
    }
}

/**
 * التحقق من تحميل الصور
 */
function checkImagesLoaded() {
    const images = document.querySelectorAll('img');
    let loadedImages = 0;
    let totalImages = images.length;

    if (totalImages === 0) {
        console.log('⚠️ لا توجد صور للتحقق منها');
        return;
    }

    images.forEach(function(img) {
        if (img.complete) {
            loadedImages++;
        } else {
            img.addEventListener('load', function() {
                loadedImages++;
                checkAllImagesLoaded();
            });

            img.addEventListener('error', function() {
                console.error('❌ فشل في تحميل الصورة:', img.src);
                loadedImages++;
                checkAllImagesLoaded();
            });
        }
    });

    function checkAllImagesLoaded() {
        if (loadedImages === totalImages) {
            console.log('✅ تم تحميل جميع الصور بنجاح');
            document.body.classList.add('images-loaded');
        }
    }

    // التحقق الأولي
    checkAllImagesLoaded();
}

/**
 * التحقق من صحة النماذج
 */
function validateForms() {
    const forms = document.querySelectorAll('form');

    forms.forEach(function(form) {
        // إضافة التحقق المخصص
        form.addEventListener('submit', function(e) {
            if (!validateForm(form)) {
                e.preventDefault();
                console.warn('⚠️ النموذج يحتوي على أخطاء');
            }
        });

        // التحقق من الحقول المطلوبة
        const requiredFields = form.querySelectorAll('[required]');
        requiredFields.forEach(function(field) {
            field.addEventListener('blur', function() {
                validateField(field);
            });
        });
    });

    console.log('✅ تم إعداد التحقق من النماذج');
}

/**
 * التحقق من نموذج واحد
 */
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(function(field) {
        if (!validateField(field)) {
            isValid = false;
        }
    });

    return isValid;
}

/**
 * التحقق من حقل واحد
 */
function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type || field.tagName.toLowerCase();

    // إزالة رسائل الخطأ السابقة
    field.classList.remove('is-invalid', 'is-valid');
    const existingError = field.parentNode.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }

    let isValid = true;
    let errorMessage = '';

    // التحقق من الحقول المطلوبة
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب';
    }

    // التحقق حسب نوع الحقل
    if (value && isValid) {
        switch(fieldType) {
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'يرجى إدخال بريد إلكتروني صحيح';
                }
                break;

            case 'tel':
                const phoneRegex = /^[0-9+\-\s()]+$/;
                if (!phoneRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'يرجى إدخال رقم هاتف صحيح';
                }
                break;

            case 'url':
                try {
                    new URL(value);
                } catch {
                    isValid = false;
                    errorMessage = 'يرجى إدخال رابط صحيح';
                }
                break;
        }
    }

    // إضافة الفئات والرسائل المناسبة
    if (isValid) {
        field.classList.add('is-valid');
    } else {
        field.classList.add('is-invalid');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message text-danger small mt-1';
        errorDiv.textContent = errorMessage;
        field.parentNode.appendChild(errorDiv);
    }

    return isValid;
}

/**
 * التحقق من الروابط
 */
function checkLinks() {
    const links = document.querySelectorAll('a[href]');

    links.forEach(function(link) {
        const href = link.getAttribute('href');

        // التحقق من الروابط الداخلية
        if (href.startsWith('#')) {
            const target = document.querySelector(href);
            if (!target) {
                console.warn('⚠️ رابط داخلي مكسور:', href);
                link.classList.add('broken-link');
            }
        }

        // إضافة target="_blank" للروابط الخارجية
        if (href.startsWith('http') && !href.includes(window.location.hostname)) {
            link.setAttribute('target', '_blank');
            link.setAttribute('rel', 'noopener noreferrer');
        }
    });

    console.log('✅ تم فحص الروابط');
}

/**
 * مراقبة الأداء
 */
function monitorPerformance() {

    // مراقبة وقت التحميل
    window.addEventListener('load', function() {
        const loadTime = performance.now();
        console.log(`⏱️ وقت التحميل: ${Math.round(loadTime)}ms`);

        // إرسال البيانات للتحليل (اختياري)
        if (loadTime > 3000) {
            console.warn('⚠️ وقت التحميل بطيء');
        }
    });

    // مراقبة استخدام الذاكرة
    if ('memory' in performance) {
        setInterval(function() {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1048576);

            if (usedMB > 50) {
                console.warn(`⚠️ استخدام ذاكرة عالي: ${usedMB}MB`);
            }
        }, 30000); // كل 30 ثانية
    }

    console.log('✅ تم تشغيل مراقبة الأداء');
}

/**
 * التحقق من إمكانية الوصول
 */
function checkAccessibility() {

    // التحقق من النصوص البديلة للصور
    const images = document.querySelectorAll('img');
    images.forEach(function(img) {
        if (!img.hasAttribute('alt') || !img.getAttribute('alt').trim()) {
            console.warn('⚠️ صورة بدون نص بديل:', img.src);
            img.setAttribute('alt', 'صورة');
        }
    });

    // التحقق من تسميات النماذج
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(function(input) {
        const label = document.querySelector(`label[for="${input.id}"]`);
        if (!label && !input.hasAttribute('aria-label')) {
            console.warn('⚠️ حقل بدون تسمية:', input);
        }
    });

    // التحقق من التباين
    checkColorContrast();

    console.log('✅ تم فحص إمكانية الوصول');
}

/**
 * التحقق من تباين الألوان
 */
function checkColorContrast() {
    // فحص أساسي للتباين
    const elements = document.querySelectorAll('*');

    elements.forEach(function(element) {
        const styles = window.getComputedStyle(element);
        const color = styles.color;
        const backgroundColor = styles.backgroundColor;

        // فحص بسيط للألوان الفاتحة على خلفية فاتحة
        if (color === 'rgb(255, 255, 255)' && backgroundColor === 'rgb(255, 255, 255)') {
            console.warn('⚠️ تباين ضعيف محتمل:', element);
        }
    });
}

/**
 * مراقبة الأخطاء
 */
function monitorErrors() {

    // مراقبة أخطاء JavaScript
    window.addEventListener('error', function(e) {
        console.error('❌ خطأ JavaScript:', {
            message: e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno
        });

        // يمكن إرسال الخطأ لخدمة التتبع
        reportError('JavaScript Error', e.message);
    });

    // مراقبة الوعود المرفوضة
    window.addEventListener('unhandledrejection', function(e) {
        console.error('❌ وعد مرفوض:', e.reason);
        reportError('Unhandled Promise Rejection', e.reason);
    });

    console.log('✅ تم تشغيل مراقبة الأخطاء');
}

/**
 * إرسال تقرير الخطأ
 */
function reportError(type, message) {
    // يمكن تخصيص هذه الوظيفة لإرسال الأخطاء لخدمة التتبع
    const errorData = {
        type: type,
        message: message,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
    };

    // مثال: إرسال للخادم
    // fetch('/api/errors', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(errorData)
    // });

    console.log('📊 تقرير الخطأ:', errorData);
}

/**
 * تشغيل اختبارات الأداء
 */
function runPerformanceTests() {
    const tests = [
        testPageLoadSpeed,
        testImageOptimization,
        testCSSOptimization,
        testJavaScriptOptimization
    ];

    tests.forEach(function(test) {
        try {
            test();
        } catch (error) {
            console.error('❌ فشل في اختبار الأداء:', error);
        }
    });
}

/**
 * اختبار سرعة تحميل الصفحة
 */
function testPageLoadSpeed() {
    const navigation = performance.getEntriesByType('navigation')[0];
    const loadTime = navigation.loadEventEnd - navigation.loadEventStart;

    if (loadTime > 3000) {
        console.warn('⚠️ وقت تحميل الصفحة بطيء:', loadTime + 'ms');
    } else {
        console.log('✅ وقت تحميل الصفحة جيد:', loadTime + 'ms');
    }
}

/**
 * اختبار تحسين الصور
 */
function testImageOptimization() {
    const images = document.querySelectorAll('img');

    images.forEach(function(img) {
        if (img.naturalWidth > 1920 || img.naturalHeight > 1080) {
            console.warn('⚠️ صورة كبيرة الحجم:', img.src);
        }

        if (!img.hasAttribute('loading')) {
            console.warn('⚠️ صورة بدون lazy loading:', img.src);
        }
    });
}

/**
 * اختبار تحسين CSS
 */
function testCSSOptimization() {
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');

    if (stylesheets.length > 10) {
        console.warn('⚠️ عدد كبير من ملفات CSS:', stylesheets.length);
    }
}

/**
 * اختبار تحسين JavaScript
 */
function testJavaScriptOptimization() {
    const scripts = document.querySelectorAll('script[src]');

    if (scripts.length > 15) {
        console.warn('⚠️ عدد كبير من ملفات JavaScript:', scripts.length);
    }
}

// تشغيل الاختبارات عند اكتمال التحميل
window.addEventListener('load', function() {
    setTimeout(runPerformanceTests, 1000);
});
