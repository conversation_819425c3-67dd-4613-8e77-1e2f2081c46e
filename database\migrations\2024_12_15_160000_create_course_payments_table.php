<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCoursePaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_payments', function (Blueprint $table) {
            $table->id();
            
            // ربط مع جدول المشتريات
            $table->bigInteger('purchase_id')->unsigned();
            $table->foreign('purchase_id')->references('id')->on('purchases')->onDelete('cascade');
            
            // ربط مع جدول الدورات
            $table->bigInteger('course_id')->unsigned();
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
            
            // معلومات الدفعة
            $table->decimal('amount', 10, 2)->comment('مبلغ الدفعة');
            $table->date('payment_date')->comment('تاريخ الدفع');
            $table->string('payment_method', 100)->comment('طريقة الدفع');
            
            // معلومات الإيصال
            $table->string('receipt_number', 100)->nullable()->comment('رقم الإيصال');
            $table->string('receipt_image')->nullable()->comment('صورة الإيصال');
            
            // ملاحظات
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->string('received_by', 100)->nullable()->comment('استلمها');
            
            // حالة الدفعة
            $table->enum('status', ['pending', 'verified', 'rejected'])->default('verified')->comment('حالة الدفعة');
            
            // معلومات إضافية
            $table->integer('installment_number')->default(1)->comment('رقم القسط');
            
            $table->timestamps();
            
            // فهارس للبحث السريع
            $table->index(['purchase_id']);
            $table->index(['course_id']);
            $table->index(['payment_date']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course_payments');
    }
}
