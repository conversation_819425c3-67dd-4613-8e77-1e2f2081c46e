@extends('layouts.home.app')

@section('content')
<div dir="rtl" style="direction: rtl; text-align: right; margin-top: 80px;">

    <!-- Course Main Section -->
    <section class="course-main-section" style="background: #f8f9fa; padding: 30px 0;">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Course Info Card -->
                    <div class="card shadow-sm mb-4" style="border: none; border-radius: 15px;" dir="rtl">
                        <div class="card-body p-4">
                            <!-- Breadcrumb -->
                            <nav aria-label="breadcrumb" class="mb-3">
                                <ol class="breadcrumb" style="background: transparent; padding: 0; direction: rtl;">
                                    <li class="breadcrumb-item"><a href="{{ route('home') }}" style="color: #6c757d;">الرئيسية</a></li>
                                    <li class="breadcrumb-item"><a href="#" style="color: #6c757d;">الدورات</a></li>
                                    <li class="breadcrumb-item active" aria-current="page" style="color: #495057;">{{ $details_course->name }}</li>
                                </ol>
                            </nav>

                            <!-- Course Title -->
                            <h1 class="course-title mb-4" style="font-size: 2.2rem; font-weight: 700; color: #2c3e50; line-height: 1.2; text-align: right;">
                                {{ $details_course->name }}
                            </h1>

                            <!-- Course Content Tabs -->
                            <div class="course-content-tabs">
                                <!-- Tab Navigation -->
                                <ul class="nav nav-tabs d-flex" id="courseContentTabs" role="tablist" style="direction: rtl; justify-content: flex-end; flex-direction: row-reverse;">
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link active" id="overview-tab" data-toggle="tab" href="#overview" role="tab" aria-controls="overview" aria-selected="true">
                                            <i class="fa fa-info-circle"></i>
                                            نظرة عامة
                                        </a>
                                    </li>
                                    @if($details_course->course_objectives)
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link" id="objectives-tab" data-toggle="tab" href="#objectives" role="tab" aria-controls="objectives" aria-selected="false">
                                            <i class="fa fa-target"></i>
                                            الأهداف
                                        </a>
                                    </li>
                                    @endif
                                    @if($details_course->target_audience)
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link" id="audience-tab" data-toggle="tab" href="#audience" role="tab" aria-controls="audience" aria-selected="false">
                                            <i class="fa fa-users"></i>
                                            الفئة المستهدفة
                                        </a>
                                    </li>
                                    @endif
                                    @if($instructors && $instructors->count() > 0)
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link" id="instructors-tab" data-toggle="tab" href="#instructors" role="tab" aria-controls="instructors" aria-selected="false">
                                            <i class="fa fa-graduation-cap"></i>
                                            المدربون
                                        </a>
                                    </li>
                                    @endif
                                </ul>
                            </div>

                            <!-- Tab Content -->
                            <div class="tab-content" id="courseContentTabsContent" style="padding: 30px 0;">
                                <!-- Overview Tab -->
                                <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                                    <div class="overview-content" style="text-align: right;">
                                        <h3 class="mb-4" style="color: #2c3e50; font-weight: 700; text-align: right;">وصف الدورة</h3>
                                        <div class="description-content" style="line-height: 1.8; color: #495057; font-size: 16px; text-align: right;">
                                            {!! nl2br(e($details_course->description)) !!}
                                        </div>


                                    </div>
                                </div>

                                <!-- Objectives Tab -->
                                @if($details_course->course_objectives)
                                <div class="tab-pane fade" id="objectives" role="tabpanel" aria-labelledby="objectives-tab">
                                    <div class="objectives-content" style="text-align: right;">
                                        <h3 class="mb-4" style="color: #2c3e50; font-weight: 700; text-align: right;">أهداف الدورة</h3>
                                        <div class="objectives-list" style="line-height: 1.8; color: #495057; font-size: 16px; text-align: right;">
                                            @php
                                                $objectives = explode("\n", $details_course->course_objectives);
                                                $objectives = array_filter($objectives, function($obj) {
                                                    return !empty(trim($obj));
                                                });
                                            @endphp

                                            @if(count($objectives) > 1)
                                            <ul class="objectives-list-styled" style="direction: rtl; list-style: none; padding: 0;">
                                                @foreach($objectives as $objective)
                                                    @if(!empty(trim($objective)))
                                                    <li class="mb-3 d-flex align-items-start" style="direction: rtl;">
                                                        <span style="text-align: right; flex-grow: 1;">{{ trim(str_replace(['✅', '✓', '-'], '', $objective)) }}</span>
                                                        <i class="fa fa-check-circle text-success ml-3 mt-1" style="font-size: 18px;"></i>
                                                    </li>
                                                    @endif
                                                @endforeach
                                            </ul>
                                            @else
                                            <div class="single-objective" style="text-align: right;">
                                                {!! nl2br(e($details_course->course_objectives)) !!}
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                @endif

                                <!-- Target Audience Tab -->
                                @if($details_course->target_audience)
                                <div class="tab-pane fade" id="audience" role="tabpanel" aria-labelledby="audience-tab">
                                    <div class="audience-content" style="text-align: right;">
                                        <h3 class="mb-4" style="color: #2c3e50; font-weight: 700; text-align: right;">الفئة المستهدفة</h3>
                                        <div class="audience-list" style="line-height: 1.8; color: #495057; font-size: 16px; text-align: right;">
                                            @php
                                                $audiences = explode("\n", $details_course->target_audience);
                                                $audiences = array_filter($audiences, function($aud) {
                                                    return !empty(trim($aud));
                                                });
                                            @endphp

                                            @if(count($audiences) > 1)
                                            <ul class="audience-list-styled" style="direction: rtl; list-style: none; padding: 0;">
                                                @foreach($audiences as $audience)
                                                    @if(!empty(trim($audience)))
                                                    <li class="mb-3 d-flex align-items-start" style="direction: rtl;">
                                                        <span style="text-align: right; flex-grow: 1;">{{ trim(str_replace(['✔️', '✓', '-'], '', $audience)) }}</span>
                                                        <i class="fa fa-user text-primary ml-3 mt-1" style="font-size: 18px;"></i>
                                                    </li>
                                                    @endif
                                                @endforeach
                                            </ul>
                                            @else
                                            <div class="single-audience" style="text-align: right;">
                                                {!! nl2br(e($details_course->target_audience)) !!}
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                @endif

                                <!-- Instructors Tab -->
                                @if($instructors && $instructors->count() > 0)
                                <div class="tab-pane fade" id="instructors" role="tabpanel" aria-labelledby="instructors-tab">
                                    <div class="instructors-content" style="text-align: right;">
                                        <h3 class="mb-4" style="color: #2c3e50; font-weight: 700; text-align: right;">مدربو الدورة</h3>
                                        <div class="instructors-grid">
                                            @foreach($instructors as $instructor)
                                            <div class="instructor-card mb-4 p-4" style="background: #f8f9fa; border-radius: 15px; border: 1px solid #e9ecef; direction: rtl;">
                                                <div class="d-flex align-items-start" style="direction: rtl;">
                                                    <div class="instructor-info flex-grow-1 ml-4" style="text-align: right;">
                                                        <h5 class="instructor-name mb-2" style="color: #2c3e50; font-weight: 700; text-align: right;">
                                                            {{ $instructor->name ?? 'مدرب' }}
                                                        </h5>
                                                        <p class="instructor-title mb-2" style="color: #6c757d; font-size: 16px; text-align: right;">
                                                            {{ $instructor->jobs ?? 'مدرب' }}
                                                        </p>
                                                        @if($instructor->rating)
                                                        <div class="instructor-rating mb-3" style="text-align: right;">
                                                            <span class="rating-text mr-2" style="color: #6c757d; font-weight: 600;">
                                                                ({{ $instructor->rating }}/5)
                                                            </span>
                                                            @for($i = 1; $i <= 5; $i++)
                                                                @if($i <= $instructor->rating)
                                                                    <i class="fa fa-star text-warning"></i>
                                                                @else
                                                                    <i class="fa fa-star-o text-muted"></i>
                                                                @endif
                                                            @endfor
                                                        </div>
                                                        @endif
                                                        @if($instructor->description)
                                                        <p class="instructor-bio mb-0" style="color: #495057; line-height: 1.6; text-align: right;">
                                                            {{ $instructor->description }}
                                                        </p>
                                                        @endif
                                                    </div>
                                                    <div class="instructor-avatar">
                                                        @if($instructor->image)
                                                            <img src="{{ asset('uploads/user_images/' . $instructor->image) }}"
                                                                 alt="{{ $instructor->name }}"
                                                                 class="rounded-circle"
                                                                 style="width: 80px; height: 80px; object-fit: cover;">
                                                        @else
                                                            <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center"
                                                                 style="width: 80px; height: 80px; background: #007bff; color: white; font-weight: 600; font-size: 24px;">
                                                                {{ substr($instructor->name ?? 'مدرب', 0, 1) }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Course Preview Card -->
                    <div class="course-preview-card" style="position: sticky; top: 100px;" dir="rtl">
                        <div class="card shadow-sm mb-4" style="border: none; border-radius: 15px;">
                            <div class="course-media" style="position: relative;">
                                @if($details_course->demo_video)
                                    <video
                                        poster="{{ $details_course->image_path }}"
                                        style="width: 100%; height: 250px; object-fit: cover; border-radius: 15px 15px 0 0;"
                                        class="course-video">
                                        <source src="{{ asset('uploads/'.$details_course->demo_video) }}" type="video/mp4">
                                        <source src="{{ asset('uploads/'.$details_course->demo_video) }}" type="video/webm">
                                        متصفحك لا يدعم تشغيل الفيديو
                                    </video>
                                    <div class="video-overlay position-absolute w-100 h-100 d-flex align-items-center justify-content-center"
                                         style="top: 0; left: 0; background: rgba(0,0,0,0.3); border-radius: 15px 15px 0 0; cursor: pointer;"
                                         onclick="showVideoModal()">
                                        <div class="play-button d-flex align-items-center justify-content-center"
                                             style="width: 80px; height: 80px; background: rgba(255,255,255,0.9); border-radius: 50%; color: #007bff; font-size: 30px;">
                                            <i class="fa fa-play"></i>
                                        </div>
                                    </div>
                                @else
                                    <img src="{{ $details_course->image_path }}"
                                         alt="{{ $details_course->name }}"
                                         style="width: 100%; height: 250px; object-fit: cover; border-radius: 15px 15px 0 0;">
                                @endif
                            </div>

                            <div class="card-body p-4">
                                <!-- Price Section -->
                                <div class="price-section text-center mb-4">
                                    @if($details_course->show_price)
                                        <div class="current-price mb-2">
                                            <span class="price-text" style="font-size: 2rem; font-weight: 700; color: #007bff;">
                                                {{ number_format($details_course->price, 0) }}
                                            </span>
                                            <span style="font-size: 1.2rem; color: #6c757d; font-weight: 600;">دج</span>
                                        </div>
                                    @else
                                        <div class="contact-price mb-2">
                                            <span style="font-size: 1.5rem; font-weight: 700; color: #dc3545;">
                                                اتصل للاستفسار عن السعر
                                            </span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Enrollment Button -->
                                <div class="enrollment-section mb-4">
                                    <a href="{{ route('purchase.create', $details_course->id) }}"
                                       class="btn btn-primary btn-lg btn-block"
                                       style="border-radius: 10px; padding: 15px; font-weight: 600;">
                                        @if($details_course->show_price)
                                            اشترك الآن
                                        @else
                                            استفسر عن الدورة
                                        @endif
                                        <i class="fa fa-shopping-cart mr-2"></i>
                                    </a>
                                </div>

                                <!-- Course Info -->
                                <div class="course-info">
                                    <div class="info-item mb-3 p-3" style="background: #f8f9fa; border-radius: 10px; text-align: center;">
                                        <div style="color: #495057; font-weight: 600; font-size: 16px;">مدة الدورة</div>
                                        <div style="color: #007bff; font-weight: 700; font-size: 18px;">{{ $details_course->time }} ساعة</div>
                                    </div>

                                    <div class="info-item mb-3 p-3" style="background: #f8f9fa; border-radius: 10px; text-align: center;">
                                        <div style="color: #495057; font-weight: 600; font-size: 16px;">عدد الطلبة</div>
                                        <div style="color: #28a745; font-weight: 700; font-size: 18px;">{{ $details_course->studant_count ?? '0' }} طالب</div>
                                    </div>

                                    <div class="contact-info p-3" style="background: #e3f2fd; border-radius: 10px; text-align: center;">
                                        <div style="color: #495057; font-weight: 600; font-size: 16px; margin-bottom: 10px;">للاستفسار</div>
                                        <div style="color: #1976d2; font-weight: 600; margin-bottom: 5px;">0774479525</div>
                                        <div style="color: #1976d2; font-weight: 600; margin-bottom: 5px;">0665657400</div>
                                        <div style="color: #666; font-size: 14px;">برج بوعريريج، الجزائر</div>
                                    </div>
                                </div>


                            </div>
                        </div>

                        <!-- Contact Card -->
                        <div class="contact-card">
                            <div class="card shadow-sm" style="border: none; border-radius: 15px;">
                                <div class="card-header text-center" style="background: #007bff; color: white; border-radius: 15px 15px 0 0; padding: 20px;">
                                    <h5 class="mb-0" style="font-weight: 600;">
                                        <i class="fa fa-phone ml-2"></i>تحتاج مساعدة؟
                                    </h5>
                                </div>
                                <div class="card-body p-4 text-center">
                                    <p class="mb-4" style="color: #6c757d; line-height: 1.6;">
                                        تواصل معنا للحصول على استشارة مجانية حول الدورة
                                    </p>
                                    <div class="contact-info">
                                        <div class="contact-item mb-3 p-3" style="background: #f8f9fa; border-radius: 10px; border-right: 4px solid #28a745; text-align: center;">
                                            <i class="fa fa-phone text-success ml-2"></i>
                                            <span style="color: #495057; font-weight: 600;">0774479525</span>
                                        </div>
                                        <div class="contact-item mb-3 p-3" style="background: #f8f9fa; border-radius: 10px; border-right: 4px solid #28a745; text-align: center;">
                                            <i class="fa fa-phone text-success ml-2"></i>
                                            <span style="color: #495057; font-weight: 600;">0665657400</span>
                                        </div>
                                        <div class="contact-item p-3" style="background: #f8f9fa; border-radius: 10px; border-right: 4px solid #dc3545; text-align: center;">
                                            <i class="fa fa-map-marker text-danger ml-2"></i>
                                            <span style="color: #495057; font-weight: 600;">برج بوعريريج، الجزائر</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>


    <!-- Video Modal -->
    @if($details_course->demo_video)
    <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content" style="border-radius: 15px; border: none;">
                <div class="modal-header" style="border-bottom: none; background: #007bff; color: white; border-radius: 15px 15px 0 0;">
                    <h5 class="modal-title" id="videoModalLabel">
                        <i class="fa fa-play-circle ml-2"></i>
                        معاينة الدورة - {{ $details_course->name }}
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-0">
                    <div class="video-container" style="position: relative; border-radius: 0 0 15px 15px; overflow: hidden;">
                        <video
                            id="courseVideo"
                            controls
                            style="width: 100%; height: 400px; object-fit: cover;"
                            class="course-video">
                            <source src="{{ asset('uploads/'.$details_course->demo_video) }}" type="video/mp4">
                            <source src="{{ asset('uploads/'.$details_course->demo_video) }}" type="video/webm">
                            متصفحك لا يدعم تشغيل الفيديو
                        </video>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif



	<!-- End intro Courses -->
</div>
@endsection

@push('styles')
<style>
/* Course Header Section */
.course-header-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.breadcrumb-item a {
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #007bff !important;
}

.course-title {
    animation: fadeInUp 1s ease-out;
}

.course-subtitle {
    animation: fadeInUp 1s ease-out 0.2s both;
}

.course-meta-info {
    animation: fadeInUp 1s ease-out 0.4s both;
}

.course-preview-card {
    animation: fadeInRight 1s ease-out 0.6s both;
}

/* Course Preview Card */
.course-preview-card .card {
    transition: all 0.3s ease;
    border: none !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
}

.course-preview-card .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.course-media {
    position: relative;
    overflow: hidden;
}

.video-overlay {
    transition: all 0.3s ease;
    opacity: 0;
}

.course-media:hover .video-overlay {
    opacity: 1;
}

.play-button {
    transition: all 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
    background: rgba(255,255,255,1) !important;
    box-shadow: 0 8px 25px rgba(0,123,255,0.3);
}

/* Course Tabs */
.nav-tabs {
    border-bottom: none !important;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 20px;
}

.nav-tabs .nav-item {
    margin-bottom: 0 !important;
}

.nav-tabs .nav-link {
    border: 2px solid transparent !important;
    border-radius: 8px !important;
    transition: all 0.3s ease;
    color: #6c757d !important;
    font-weight: 600;
    padding: 12px 20px;
    margin: 0 5px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-tabs .nav-link:hover {
    border-color: #007bff !important;
    color: #007bff !important;
    background: rgba(0,123,255,0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}

.nav-tabs .nav-link.active {
    border-color: #007bff !important;
    color: white !important;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.nav-tabs .nav-link i {
    font-size: 16px;
    margin-left: 8px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover i {
    transform: scale(1.1);
}

.nav-tabs .nav-link.active i {
    transform: scale(1.1);
    text-shadow: 0 0 8px rgba(255,255,255,0.5);
}

/* Tab Content Animation */
.tab-content {
    animation: fadeInUp 0.5s ease-out;
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.tab-pane {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.tab-pane.active {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Tabs */
@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column !important;
        padding: 10px;
    }

    .nav-tabs .nav-link {
        margin: 5px 0;
        text-align: center;
    }
}

.tab-pane {
    min-height: 300px;
}

/* Objectives and Audience Lists */
.objectives-list-styled,
.audience-list-styled {
    list-style: none;
    padding: 0;
}

.objectives-list-styled li,
.audience-list-styled li {
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.objectives-list-styled li:hover {
    background: rgba(40, 167, 69, 0.1) !important;
    transform: translateX(10px);
}

.audience-list-styled li:hover {
    background: rgba(0, 123, 255, 0.1) !important;
    transform: translateX(10px);
}

/* Instructor Cards */
.instructor-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef !important;
}

.instructor-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.15) !important;
    border-color: #007bff !important;
}

.instructor-avatar img,
.avatar-placeholder {
    transition: all 0.3s ease;
}

.instructor-card:hover .instructor-avatar img,
.instructor-card:hover .avatar-placeholder {
    transform: scale(1.05);
}

/* Contact Card */
.contact-card .card {
    transition: all 0.3s ease;
}

.contact-card .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0,123,255,0.15) !important;
}

.contact-item {
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Price Display */
.current-price {
    animation: pulse 2s infinite;
    text-shadow: 2px 2px 4px rgba(0,123,255,0.3);
}

.price-text {
    animation: pulse 2s infinite;
}

/* Course Includes */
.course-includes ul li {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 6px;
    margin: -8px;
}

.course-includes ul li:hover {
    background: rgba(0,123,255,0.05);
    transform: translateX(10px);
}

/* Course Details */
.detail-item {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 6px;
    margin: -8px;
}

.detail-item:hover {
    background: rgba(0,123,255,0.05);
    transform: translateX(5px);
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    border: none !important;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.4) !important;
    background: linear-gradient(135deg, #0056b3 0%, #007bff 100%) !important;
}

/* Instructors in Header */
.instructor-item {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 8px;
    margin: -8px;
}

.instructor-item:hover {
    background: rgba(0,123,255,0.05);
    transform: translateY(-2px);
}

/* Hero Section Enhancements */
.course-hero-section {
    position: relative;
    background-attachment: fixed;
}

/* Course Image and Video Play Button */
.course-image-container {
    transition: all 0.3s ease;
}

.course-image-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.4) !important;
}

.course-main-image {
    transition: all 0.3s ease;
}

.video-play-overlay {
    opacity: 0;
    transition: all 0.3s ease;
}

.course-image-container:hover .video-play-overlay {
    opacity: 1;
}

.play-button {
    transition: all 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
    background: rgba(231, 76, 60, 1) !important;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

.hero-media-section {
    animation: fadeInRight 1s ease-out;
}

.hero-title {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    animation: fadeInUp 1s ease-out 0.2s both;
}

.course-meta {
    animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-price-card {
    animation: fadeInUp 1s ease-out 0.6s both;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.hero-price-card:hover {
    transform: translateY(-5px);
}

/* Card Enhancements */
.card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05) !important;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.card:hover .card-header::before {
    left: 100%;
}

/* Video Enhancements */
.course-video {
    transition: all 0.3s ease;
    border-radius: 0 0 15px 15px;
}

.course-video:hover {
    transform: scale(1.02);
}

/* Button Enhancements */
.enrollment-btn {
    position: relative;
    overflow: hidden;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.enrollment-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.enrollment-btn:hover::before {
    left: 100%;
}

.enrollment-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    color: white !important;
}

/* Info Items Animation */
.info-item {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 8px;
    margin: -8px;
}

.info-item:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

/* Rating Stars Animation */
.rating-display i {
    transition: all 0.2s ease;
}

.rating-display:hover i {
    transform: scale(1.2);
}

/* Contact Items */
.contact-item {
    transition: all 0.3s ease;
    border-radius: 8px;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Contact Card Enhancement */
.contact-info .contact-item {
    background: #f8f9fa !important;
    border-radius: 8px !important;
    padding: 8px !important;
    margin-bottom: 12px !important;
}

.contact-info .contact-item span {
    color: #2c3e50 !important;
    font-weight: 600 !important;
}

/* Feature Items */
.feature-item {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 8px;
    margin: -8px;
}

.feature-item:hover {
    background: rgba(40, 167, 69, 0.1);
    transform: translateX(5px);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem !important;
    }

    .hero-subtitle {
        font-size: 1rem !important;
    }

    .course-meta {
        flex-direction: column;
        gap: 10px !important;
    }

    .meta-item {
        justify-content: center;
    }

    .hero-price-card {
        margin-top: 30px;
    }
}

/* Price Display Enhancement */
.price-amount {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s infinite;
}

/* Course Badge */
.course-badge .badge {
    animation: fadeInDown 1s ease-out;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation for Video */
.video-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-container.loading::before {
    opacity: 1;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Instructor Cards */
.instructor-card {
    transition: all 0.3s ease;
    background: #fff;
}

.instructor-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(111, 66, 193, 0.15);
    border-color: #6f42c1 !important;
}

.instructor-avatar img,
.avatar-placeholder {
    transition: all 0.3s ease;
}

.instructor-card:hover .instructor-avatar img,
.instructor-card:hover .avatar-placeholder {
    transform: scale(1.1);
}

.instructor-name {
    transition: color 0.3s ease;
}

.instructor-card:hover .instructor-name {
    color: #8e44ad !important;
}

/* Objectives and Target Audience */
.objectives-content,
.target-audience-content {
    color: #555;
}

.objectives-content i,
.target-audience-content i {
    color: #28a745;
    margin-left: 8px;
}

/* Contact for Price Animation */
.contact-for-price {
    animation: pulse 2s infinite;
}

.contact-numbers div {
    transition: all 0.3s ease;
    padding: 2px 0;
}

.contact-numbers div:hover {
    color: #e74c3c !important;
    transform: translateX(5px);
}

/* Enhanced Button Styles */
.enrollment-btn-hero {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.enrollment-btn-hero:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%) !important;
}

.enrollment-btn-sidebar {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.enrollment-btn-sidebar:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%) !important;
    color: white !important;
    text-decoration: none !important;
}

/* Card Headers */
.card-header {
    border-bottom: none !important;
}

/* Text Colors */
.course-description,
.objectives-content,
.target-audience-content {
    color: #2c3e50 !important;
}

/* Info Items */
.info-item .info-label {
    color: #2c3e50 !important;
}

.info-item .info-value {
    color: #2c3e50 !important;
}

/* Hero Section Text */
.course-hero-section h1 {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.course-hero-section p {
    color: rgba(255,255,255,0.9) !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Price Display */
.price-amount {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* Instructor Cards Enhancement */
.instructor-card {
    background: #ffffff !important;
    border: 1px solid #e9ecef !important;
}

.instructor-card:hover {
    border-color: #9b59b6 !important;
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.15) !important;
}

/* Objectives and Target Audience Items */
.objective-item {
    transition: all 0.3s ease;
    cursor: default;
}

.objective-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.15);
    background: #e8f5e8 !important;
}

.audience-item {
    transition: all 0.3s ease;
    cursor: default;
}

.audience-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.15);
    background: #fef9e7 !important;
}

.objective-icon i,
.audience-icon i {
    transition: all 0.3s ease;
}

.objective-item:hover .objective-icon i {
    transform: scale(1.2);
    color: #27ae60 !important;
}

.audience-item:hover .audience-icon i {
    transform: scale(1.2);
    color: #f39c12 !important;
}

/* Responsive adjustments for objectives and audience */
@media (max-width: 768px) {
    .objective-item,
    .audience-item {
        margin-bottom: 15px;
    }

    .col-md-6 {
        padding-left: 10px;
        padding-right: 10px;
    }
}
</style>
@endpush

@push('scripts')
<script>
// Function to show video modal
function showVideoModal() {
    $('#videoModal').modal('show');
}

$(document).ready(function() {
    // Initialize Bootstrap tabs with enhanced animations
    $('#courseContentTabs a').on('click', function (e) {
        e.preventDefault();

        // Remove active class from all tabs
        $('#courseContentTabs .nav-link').removeClass('active');

        // Add active class to clicked tab
        $(this).addClass('active');

        // Show corresponding tab content with animation
        var target = $(this).attr("href");
        $('.tab-pane').removeClass('show active');

        setTimeout(function() {
            $(target).addClass('show active');
        }, 150);
    });

    // Tab switching animation with smooth transition
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        var target = $(e.target).attr("href");
        $(target).addClass('animated fadeInUp');

        // Add ripple effect to active tab
        var $tab = $(e.target);
        $tab.addClass('tab-clicked');

        setTimeout(function() {
            $(target).removeClass('animated fadeInUp');
            $tab.removeClass('tab-clicked');
        }, 500);
    });

    // Add hover effects for tabs
    $('.nav-tabs .nav-link').hover(
        function() {
            if (!$(this).hasClass('active')) {
                $(this).addClass('tab-hover');
            }
        },
        function() {
            $(this).removeClass('tab-hover');
        }
    );

    // Pause video when modal is closed
    $('#videoModal').on('hidden.bs.modal', function () {
        var video = document.getElementById('courseVideo');
        if (video) {
            video.pause();
            video.currentTime = 0;
        }
    });

    // Smooth scroll for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Video loading animation
    $('.course-video').on('loadstart', function() {
        $(this).closest('.video-container').addClass('loading');
    });

    $('.course-video').on('loadeddata', function() {
        $(this).closest('.video-container').removeClass('loading');
    });

    // Parallax effect for hero section
    $(window).scroll(function() {
        var scrolled = $(this).scrollTop();
        var parallax = $('.course-hero-section');
        var speed = 0.5;

        parallax.css('transform', 'translateY(' + (scrolled * speed) + 'px)');
    });

    // Counter animation for stats
    $('.price-amount, .info-value').each(function() {
        var $this = $(this);
        var countTo = $this.text().replace(/[^0-9]/g, '');

        if (countTo && !isNaN(countTo)) {
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    var formattedNumber = Math.floor(this.countNum).toLocaleString();
                    if ($this.hasClass('price-amount')) {
                        $this.text(formattedNumber);
                    }
                },
                complete: function() {
                    var formattedNumber = parseInt(countTo).toLocaleString();
                    if ($this.hasClass('price-amount')) {
                        $this.text(formattedNumber);
                    }
                }
            });
        }
    });

    // Intersection Observer for animations
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        });

        document.querySelectorAll('.card').forEach(card => {
            observer.observe(card);
        });
    }
});
</script>
@endpush