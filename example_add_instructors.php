<?php

// مثال على كيفية إضافة مدربين لدورة

// 1. إضافة مدرب واحد لدورة
$course = Course::find(2); // دورة تسيير المخزون
$instructor = User::where('jobs', 'مدرب')->where('name', 'Hassan')->first();

if ($course && $instructor) {
    CourseInstructor::create([
        'course_id' => $course->id,
        'user_id' => $instructor->id
    ]);
}

// 2. إضافة عدة مدربين لدورة واحدة
$course = Course::find(2);
$instructorIds = User::where('jobs', 'مدرب')->pluck('id')->toArray();

foreach ($instructorIds as $instructorId) {
    CourseInstructor::firstOrCreate([
        'course_id' => $course->id,
        'user_id' => $instructorId
    ]);
}

// 3. استخدام العلاقة المباشرة
$course = Course::find(2);
$instructorIds = [2, 3, 4]; // <PERSON>, <PERSON>, <PERSON>

$course->instructors()->attach($instructorIds);

// 4. عرض مدربي دورة معينة
$course = Course::with('instructors')->find(2);
echo "مدربو الدورة: " . $course->instructor_names;

foreach ($course->instructors as $instructor) {
    echo "المدرب: " . $instructor->name . " - التقييم: " . $instructor->rating . "\n";
}

// 5. عرض دورات مدرب معين
$instructor = User::with('courses')->find(2); // Hassan
echo "دورات المدرب " . $instructor->name . ":\n";

foreach ($instructor->courses as $course) {
    echo "- " . $course->name . "\n";
}

// 6. تحديث بيانات الدورة مع الأهداف والفئة المستهدفة
$course = Course::find(2);
$course->update([
    'course_objectives' => '
        ✅ إكساب المشاركين مهارات متقدمة في إدارة المخزون وعمليات الشراء
        ✅ تحسين عمليات اتخاذ القرار لتقليل التكاليف وزيادة الكفاءة
        ✅ تطبيق أفضل الممارسات في التخطيط الاستراتيجي للمشتريات
        ✅ التعرف على أحدث التقنيات في الجرد وإعداد البطاقات التقنية
        ✅ فهم العلاقة بين تسيير المخزون وأداء المؤسسة بشكل عام
    ',
    'target_audience' => '
        ✔️ مدراء وأخصائيو المشتريات والمخازن
        ✔️ أصحاب المؤسسات الصغيرة والمتوسطة
        ✔️ العاملون في أقسام التوريد وسلاسل الإمداد
        ✔️ الطلبة والباحثون في التخصصات الاقتصادية والإدارية
    '
]);

?>
