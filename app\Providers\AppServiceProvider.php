<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Http\ViewComposers\CategoryComposer;
use PaginateRoute;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // تسجيل View Composer للفئات في جميع صفحات الموقع
        View::composer([
            'layouts.home.include._header',
            'layouts.home.include._footer',
            'home.*',
            'auth.*'
        ], CategoryComposer::class);
    }
}
