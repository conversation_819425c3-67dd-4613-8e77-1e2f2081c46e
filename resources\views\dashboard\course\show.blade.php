@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">
        <section class="content-header">
            <h1>تفاصيل الدورة التدريبية</h1>
            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
                <li><a href="{{ route('dashboard.courses.index') }}">الدورات التدريبية</a></li>
                <li class="active">تفاصيل الدورة</li>
            </ol>
        </section>

        <section class="content">
            <!-- معلومات الدورة الأساسية -->
            <div class="row">
                <!-- الصورة والمعلومات الأساسية -->
                <div class="col-md-4">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">صورة الدورة</h3>
                        </div>
                        <div class="box-body text-center">
                            <img src="{{ $course->image_path }}"
                                 alt="{{ $course->name }}"
                                 class="img-responsive course-main-image"
                                 style="width: 100%; max-height: 300px; object-fit: cover; border-radius: 8px;">

                            <div class="course-basic-info mt-3">
                                <div class="info-item">
                                    <strong>معرف الدورة:</strong> #{{ $course->id }}
                                </div>
                                <div class="info-item">
                                    <strong>تاريخ الإنشاء:</strong> {{ $course->created_at->format('Y-m-d') }}
                                </div>
                                <div class="info-item">
                                    <strong>آخر تحديث:</strong> {{ $course->updated_at->format('Y-m-d') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الدورة -->
                <div class="col-md-8">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">{{ $course->name }}</h3>
                            <div class="box-tools pull-right">
                                <span class="label label-success">
                                    نشط
                                </span>
                            </div>
                        </div>
                        <div class="box-body">
                            <!-- الوصف -->
                            <div class="course-description mb-4">
                                <h4><i class="fa fa-info-circle"></i> الوصف</h4>
                                <div class="well">
                                    {{ $course->description ?? 'لا يوجد وصف متاح' }}
                                </div>
                            </div>

                            <!-- الوصف المختصر -->
                            @if($course->Short_description)
                            <div class="course-short-description mb-4">
                                <h4><i class="fa fa-file-text-o"></i> الوصف المختصر</h4>
                                <p class="text-muted">{{ $course->Short_description }}</p>
                            </div>
                            @endif

                            <!-- معلومات تفصيلية -->
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="info-box bg-aqua">
                                        <span class="info-box-icon"><i class="fa fa-money"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">السعر</span>
                                            <span class="info-box-number">
                                                @if($course->price > 0)
                                                    {{ $course->price }} دينار جزائري
                                                @else
                                                    مجاني
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="info-box bg-green">
                                        <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">المدة</span>
                                            <span class="info-box-number">{{ $course->time ?? 'غير محدد' }} ساعة</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="info-box bg-yellow">
                                        <span class="info-box-icon"><i class="fa fa-star"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">التقييم</span>
                                            <span class="info-box-number">
                                                {{ $course->rating }}/5
                                                <small>
                                                    @for ($i = 1; $i <= 5; $i++)
                                                        @if ($i <= $course->rating)
                                                            <i class="fa fa-star text-yellow"></i>
                                                        @else
                                                            <i class="fa fa-star-o text-muted"></i>
                                                        @endif
                                                    @endfor
                                                </small>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="info-box bg-red">
                                        <span class="info-box-icon"><i class="fa fa-users"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">عدد الطلاب</span>
                                            <span class="info-box-number">{{ $course->studant_count ?? 0 }} طالب</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="row">
                <div class="col-md-6">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات إضافية</h3>
                        </div>
                        <div class="box-body">
                            <table class="table table-bordered">
                                <tr>
                                    <td><strong>رابط الدورة:</strong></td>
                                    <td>
                                        <a href="{{ $course->url }}" target="_blank" class="btn btn-xs btn-info">
                                            <i class="fa fa-external-link"></i> فتح الرابط
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>معرف الفئة:</strong></td>
                                    <td>#{{ $course->categories_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>عدد الطلاب المسجلين:</strong></td>
                                    <td>{{ $course->studant_count ?? 0 }} طالب</td>
                                </tr>
                                <tr>
                                    <td><strong>حالة الدورة:</strong></td>
                                    <td><span class="label label-success">نشط</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title">إجراءات سريعة</h3>
                        </div>
                        <div class="box-body">
                            <div class="btn-group-vertical btn-block">
                                @if (auth()->user()->hasPermission('courses_update'))
                                    <a href="{{ route('dashboard.courses.edit', $course->id) }}"
                                       class="btn btn-primary btn-lg">
                                        <i class="fa fa-edit"></i> تعديل الدورة
                                    </a>
                                @endif

                                <a href="{{ $course->url }}"
                                   target="_blank"
                                   class="btn btn-info btn-lg">
                                    <i class="fa fa-external-link"></i> فتح رابط الدورة
                                </a>

                                <a href="{{ route('dashboard.courses.index') }}"
                                   class="btn btn-default btn-lg">
                                    <i class="fa fa-arrow-right"></i> العودة للقائمة
                                </a>

                                @if (auth()->user()->hasPermission('courses_delete'))
                                    <button class="btn btn-danger btn-lg delete-course"
                                            data-course-id="{{ $course->id }}">
                                        <i class="fa fa-trash"></i> حذف الدورة
                                    </button>

                                    <form id="delete-form-{{ $course->id }}"
                                          action="{{ route('dashboard.courses.destroy', $course->id) }}"
                                          method="post" style="display: none;">
                                        {{ csrf_field() }}
                                        {{ method_field('delete') }}
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فيديو العرض التوضيحي -->
            @if($course->demo_video)
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">فيديو العرض التوضيحي</h3>
                        </div>
                        <div class="box-body text-center">
                            <div class="video-container">
                                <video controls class="video-responsive" style="width: 100%; max-height: 400px; border-radius: 8px;">
                                    <source src="{{ asset('storage/' . $course->demo_video) }}" type="video/mp4">
                                    متصفحك لا يدعم عرض الفيديو.
                                </video>
                            </div>
                            <p class="text-muted" style="margin-top: 10px;">
                                <i class="fa fa-info-circle"></i>
                                اسم الملف: {{ $course->demo_video }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            @endif

        </section>
    </div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تأكيد الحذف
    $('.delete-course').on('click', function(e) {
        e.preventDefault();
        const courseId = $(this).data('course-id');

        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'سيتم حذف الدورة نهائياً ولا يمكن التراجع عن هذا الإجراء',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف الدورة',
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $('#delete-form-' + courseId).submit();
            }
        });
    });
});
</script>
@endpush

@push('styles')
<style>
.course-main-image {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border: 2px solid #f0f0f0;
}

.course-basic-info .info-item {
    margin: 10px 0;
    padding: 8px;
    background: #f9f9f9;
    border-radius: 4px;
    border-right: 3px solid #3c8dbc;
}

.course-description .well {
    background: #f8f9fa;
    border-right: 4px solid #3c8dbc;
    line-height: 1.6;
}

.info-box {
    margin-bottom: 15px;
}

.btn-group-vertical .btn {
    margin-bottom: 10px;
}

.video-responsive {
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.box-title {
    font-weight: bold;
}

.text-yellow {
    color: #f39c12 !important;
}
</style>
@endpush
