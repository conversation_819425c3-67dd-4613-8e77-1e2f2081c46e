<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class StudentPayment extends Model
{
    protected $fillable = [
        'student_name',
        'student_email', 
        'student_phone',
        'course_id',
        'course_name',
        'total_amount',
        'paid_amount',
        'remaining_amount',
        'payment_status',
        'enrollment_date',
        'due_date',
        'completion_date',
        'notes',
        'payment_method',
        'reference_number',
        'purchase_id'
    ];

    protected $dates = [
        'enrollment_date',
        'due_date', 
        'completion_date'
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'enrollment_date' => 'date',
        'due_date' => 'date',
        'completion_date' => 'date'
    ];

    // العلاقات
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function purchase()
    {
        return $this->belongsTo(Purchase::class);
    }

    public function installments()
    {
        return $this->hasMany(PaymentInstallment::class);
    }

    // Scopes للبحث والفلترة
    public function scopeWhenSearch($query, $search)
    {
        return $query->when($search, function ($q) use ($search) {
            return $q->where('student_name', 'like', "%$search%")
                    ->orWhere('student_email', 'like', "%$search%")
                    ->orWhere('student_phone', 'like', "%$search%")
                    ->orWhere('course_name', 'like', "%$search%")
                    ->orWhere('reference_number', 'like', "%$search%");
        });
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('payment_status', $status);
    }

    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', Carbon::now())
                    ->whereIn('payment_status', ['pending', 'partial']);
    }

    // Accessors
    public function getPaymentStatusArabicAttribute()
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'partial' => 'دفع جزئي', 
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            'refunded' => 'مسترد'
        ];

        return $statuses[$this->payment_status] ?? 'غير محدد';
    }

    public function getPaymentProgressAttribute()
    {
        if ($this->total_amount == 0) return 0;
        return round(($this->paid_amount / $this->total_amount) * 100, 2);
    }

    public function getIsOverdueAttribute()
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               in_array($this->payment_status, ['pending', 'partial']);
    }

    public function getDaysOverdueAttribute()
    {
        if (!$this->is_overdue) return 0;
        return $this->due_date->diffInDays(Carbon::now());
    }

    // Methods
    public function addPayment($amount, $method = null, $transactionId = null, $notes = null)
    {
        $this->paid_amount += $amount;
        $this->remaining_amount = $this->total_amount - $this->paid_amount;
        
        if ($this->remaining_amount <= 0) {
            $this->payment_status = 'completed';
            $this->completion_date = Carbon::now();
            $this->remaining_amount = 0;
        } else {
            $this->payment_status = 'partial';
        }
        
        $this->save();
        
        return $this;
    }

    public function createInstallmentPlan($numberOfInstallments)
    {
        $installmentAmount = $this->total_amount / $numberOfInstallments;
        $currentDate = Carbon::parse($this->enrollment_date);
        
        for ($i = 1; $i <= $numberOfInstallments; $i++) {
            PaymentInstallment::create([
                'student_payment_id' => $this->id,
                'installment_number' => $i,
                'amount' => $installmentAmount,
                'due_date' => $currentDate->copy()->addMonths($i - 1),
                'status' => 'pending'
            ]);
        }
        
        return $this;
    }
}
