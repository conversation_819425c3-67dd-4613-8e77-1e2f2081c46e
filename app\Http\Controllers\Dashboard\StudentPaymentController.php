<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\StudentPayment;
use App\Models\PaymentInstallment;
use App\Models\Course;
use Illuminate\Http\Request;
use Carbon\Carbon;

class StudentPaymentController extends Controller
{
    public function __construct()
    {
        // تم تعطيل middleware الصلاحيات مؤقتاً
        // $this->middleware(['permission:payments_read'])->only('index', 'show');
        // $this->middleware(['permission:payments_create'])->only('create', 'store');
        // $this->middleware(['permission:payments_update'])->only('edit', 'update', 'markAsPaid');
        // $this->middleware(['permission:payments_delete'])->only('destroy');
    }

    /**
     * عرض قائمة الدفعات
     */
    public function index(Request $request)
    {
        $query = StudentPayment::with(['course', 'installments']);

        // البحث
        if ($request->search) {
            $query->whenSearch($request->search);
        }

        // فلترة حسب الحالة
        if ($request->status) {
            $query->byStatus($request->status);
        }

        // فلترة حسب الدورة
        if ($request->course_id) {
            $query->byCourse($request->course_id);
        }

        // فلترة المتأخرة
        if ($request->overdue) {
            $query->overdue();
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(15);

        // إحصائيات سريعة
        $stats = [
            'total' => StudentPayment::count(),
            'pending' => StudentPayment::byStatus('pending')->count(),
            'partial' => StudentPayment::byStatus('partial')->count(),
            'completed' => StudentPayment::byStatus('completed')->count(),
            'overdue' => StudentPayment::overdue()->count(),
            'total_amount' => StudentPayment::sum('total_amount'),
            'paid_amount' => StudentPayment::sum('paid_amount'),
            'remaining_amount' => StudentPayment::sum('remaining_amount')
        ];

        $courses = Course::all();

        return view('dashboard.payments.index', compact('payments', 'stats', 'courses'));
    }

    /**
     * عرض تفاصيل دفعة طالب
     */
    public function show(StudentPayment $payment)
    {
        $payment->load(['course', 'installments' => function($query) {
            $query->orderBy('installment_number');
        }]);

        return view('dashboard.payments.show', compact('payment'));
    }

    /**
     * إنشاء دفعة جديدة
     */
    public function create()
    {
        $courses = Course::all();
        return view('dashboard.payments.create', compact('courses'));
    }

    /**
     * حفظ دفعة جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'student_name' => 'required|string|max:255',
            'student_email' => 'required|email|max:255',
            'student_phone' => 'required|string|max:20',
            'course_id' => 'required|exists:courses,id',
            'total_amount' => 'required|numeric|min:0',
            'payment_method' => 'nullable|string|max:100',
            'due_date' => 'nullable|date',
            'notes' => 'nullable|string',
            'installments_count' => 'nullable|integer|min:1|max:12'
        ]);

        try {
            $course = Course::findOrFail($request->course_id);

            $payment = StudentPayment::create([
                'student_name' => $request->student_name,
                'student_email' => $request->student_email,
                'student_phone' => $request->student_phone,
                'course_id' => $request->course_id,
                'course_name' => $course->name,
                'total_amount' => $request->total_amount,
                'paid_amount' => 0,
                'remaining_amount' => $request->total_amount,
                'payment_status' => 'pending',
                'enrollment_date' => Carbon::now(),
                'due_date' => $request->due_date,
                'payment_method' => $request->payment_method,
                'notes' => $request->notes,
                'reference_number' => 'PAY-' . time() . '-' . rand(1000, 9999)
            ]);

            // إنشاء خطة الدفعات إذا طُلب ذلك
            if ($request->installments_count && $request->installments_count > 1) {
                $payment->createInstallmentPlan($request->installments_count);
            }

            session()->flash('success', 'تم إنشاء سجل الدفع بنجاح');
            return redirect()->route('dashboard.payments.show', $payment);

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withErrors(['error' => 'حدث خطأ: ' . $e->getMessage()])
                           ->withInput();
        }
    }

    /**
     * تحديث حالة الدفعة
     */
    public function markAsPaid(Request $request, PaymentInstallment $installment)
    {
        $request->validate([
            'payment_method' => 'required|string|max:100',
            'transaction_id' => 'nullable|string|max:100',
            'receipt_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'notes' => 'nullable|string'
        ]);

        try {
            $receiptImage = null;

            if ($request->hasFile('receipt_image')) {
                $receiptImage = time() . '_' . $request->file('receipt_image')->getClientOriginalName();
                $request->file('receipt_image')->move(public_path('uploads/receipts'), $receiptImage);
            }

            $installment->markAsPaid(
                $request->payment_method,
                $request->transaction_id,
                $receiptImage,
                $request->notes
            );

            session()->flash('success', 'تم تسجيل الدفعة بنجاح');
            return redirect()->back();

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withErrors(['error' => 'حدث خطأ: ' . $e->getMessage()]);
        }
    }

    /**
     * تقرير الدفعات المتأخرة
     */
    public function overdueReport()
    {
        $overduePayments = StudentPayment::overdue()
                                        ->with(['course', 'installments'])
                                        ->orderBy('due_date')
                                        ->get();

        $overdueInstallments = PaymentInstallment::overdue()
                                                ->with(['studentPayment.course'])
                                                ->orderBy('due_date')
                                                ->get();

        return view('dashboard.payments.overdue', compact('overduePayments', 'overdueInstallments'));
    }

    /**
     * تقرير الدفعات المستحقة اليوم
     */
    public function dueTodayReport()
    {
        $dueTodayInstallments = PaymentInstallment::dueToday()
                                                 ->with(['studentPayment.course'])
                                                 ->orderBy('due_date')
                                                 ->get();

        return view('dashboard.payments.due-today', compact('dueTodayInstallments'));
    }

    /**
     * إحصائيات الدفعات
     */
    public function statistics()
    {
        $stats = [
            'total_students' => StudentPayment::distinct('student_email')->count(),
            'total_payments' => StudentPayment::count(),
            'total_revenue' => StudentPayment::sum('paid_amount'),
            'pending_amount' => StudentPayment::sum('remaining_amount'),
            'completion_rate' => StudentPayment::byStatus('completed')->count() / max(StudentPayment::count(), 1) * 100,

            'monthly_revenue' => StudentPayment::whereMonth('created_at', Carbon::now()->month)
                                              ->sum('paid_amount'),

            'overdue_count' => StudentPayment::overdue()->count(),
            'due_today_count' => PaymentInstallment::dueToday()->count(),

            'by_status' => [
                'pending' => StudentPayment::byStatus('pending')->count(),
                'partial' => StudentPayment::byStatus('partial')->count(),
                'completed' => StudentPayment::byStatus('completed')->count(),
                'cancelled' => StudentPayment::byStatus('cancelled')->count(),
            ],

            'by_course' => Course::withCount('studentPayments')->get()
        ];

        return view('dashboard.payments.statistics', compact('stats'));
    }

    /**
     * صفحة متابعة الاشتراكات
     */
    public function subscriptionsTracking(Request $request)
    {
        // استخدام جدول purchases الموجود مؤقتاً
        $purchases = \App\Models\Purchase::with('course')->get();

        // إحصائيات عامة
        $generalStats = [
            'total_subscriptions' => $purchases->count(),
            'active_subscriptions' => $purchases->where('status', '0')->count(), // في الانتظار
            'completed_subscriptions' => $purchases->where('status', '1')->count(), // مقبول
            'cancelled_subscriptions' => $purchases->where('status', '2')->count(), // مرفوض
            'total_students' => $purchases->unique('email')->count(),
            'total_revenue' => 0, // سيتم حسابه لاحقاً
            'pending_revenue' => 0, // سيتم حسابه لاحقاً
        ];

        // اشتراكات حسب الحالة
        $subscriptionsByStatus = [
            'pending' => $purchases->where('status', '0')->take(10), // في الانتظار
            'partial' => collect([]), // فارغ مؤقتاً
            'completed' => $purchases->where('status', '1')->take(10), // مقبول
            'overdue' => collect([]), // فارغ مؤقتاً
        ];

        // اشتراكات حسب الدورة
        $subscriptionsByCourse = Course::withCount('purchases')
                                      ->having('purchases_count', '>', 0)
                                      ->get()
                                      ->map(function($course) use ($purchases) {
                                          $coursePurchases = $purchases->where('course_id', $course->id);
                                          $course->completed_count = $coursePurchases->where('status', '1')->count();
                                          $course->pending_count = $coursePurchases->where('status', '0')->count();
                                          $course->partial_count = 0; // مؤقتاً
                                          $course->total_amount_sum = $coursePurchases->count() * 1000; // قيمة افتراضية
                                          $course->paid_amount_sum = $course->completed_count * 1000; // قيمة افتراضية
                                          $course->student_payments_count = $coursePurchases->count();
                                          return $course;
                                      });

        // الاشتراكات الحديثة
        $recentSubscriptions = $purchases->sortByDesc('created_at')->take(10)->map(function($purchase) {
            // تحويل purchase إلى شكل مشابه لـ StudentPayment
            $purchase->student_name = $purchase->first_name . ' ' . $purchase->last_name;
            $purchase->student_email = $purchase->email;
            $purchase->course_name = $purchase->name_course;
            $purchase->total_amount = 1000; // قيمة افتراضية
            $purchase->payment_status = $purchase->status == '1' ? 'completed' : ($purchase->status == '0' ? 'pending' : 'cancelled');
            $purchase->payment_status_arabic = $purchase->status == '1' ? 'مكتمل' : ($purchase->status == '0' ? 'في الانتظار' : 'ملغي');
            $purchase->enrollment_date = $purchase->created_at;
            return $purchase;
        });

        // الدفعات المستحقة قريباً (فارغ مؤقتاً)
        $upcomingPayments = collect([]);

        // إحصائيات شهرية
        $monthlyStats = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $monthPurchases = $purchases->filter(function($purchase) use ($month) {
                return $purchase->created_at->year == $month->year &&
                       $purchase->created_at->month == $month->month;
            });

            $monthlyStats[] = [
                'month' => $month->format('Y-m'),
                'month_name' => $month->format('F Y'),
                'month_arabic' => $month->locale('ar')->format('F Y'),
                'subscriptions' => $monthPurchases->count(),
                'revenue' => $monthPurchases->where('status', '1')->count() * 1000, // قيمة افتراضية
                'completed' => $monthPurchases->where('status', '1')->count(),
            ];
        }

        return view('dashboard.payments.subscriptions-tracking', compact(
            'generalStats',
            'subscriptionsByStatus',
            'subscriptionsByCourse',
            'recentSubscriptions',
            'upcomingPayments',
            'monthlyStats'
        ));
    }
}
