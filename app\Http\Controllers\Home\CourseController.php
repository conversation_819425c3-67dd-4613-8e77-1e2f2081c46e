<?php

namespace App\Http\Controllers\Home;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Course;

class CourseController extends Controller
{
    public function ShowCourse($id)
    {
    	$categorys = Category::all();
    	$courses   = Course::all()->where('categories_id', $id);
    	$name_courses   = Category::all()->where('id', $id)->pluck('name')->first();

    	return view('home.show_sourse',compact('categorys','courses','name_courses'));
    }//end of show course

    public function DetailsCourse($id)
    {
        $categorys = Category::all();

        // تحميل الدورة مع جميع العلاقات المطلوبة
        $details_course = Course::with(['category', 'instructors.user', 'purchases'])
            ->find($id);

        if (!$details_course) {
            abort(404, 'الدورة غير موجودة');
        }

        // حساب إحصائيات إضافية
        $studentsCount = $details_course->purchases ? $details_course->purchases->count() : 0;
        $instructorsCount = $details_course->instructors ? $details_course->instructors->count() : 0;

        // تحضير بيانات المدربين
        $instructors = $details_course->instructors()->with('user')->get();

        return view('home.details_course', compact(
            'categorys',
            'details_course',
            'studentsCount',
            'instructorsCount',
            'instructors'
        ));
    }//end of Details_Course

}//end of controller
