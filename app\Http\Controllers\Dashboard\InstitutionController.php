<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Institution;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class InstitutionController extends Controller
{
    public function __construct()
    {
        $this->middleware(['permission:settings_read'])->only('index', 'show');
        $this->middleware(['permission:settings_read'])->only('edit', 'update');
    }

    /**
     * Display institution settings
     */
    public function index()
    {
        $institution = Institution::first() ?? Institution::getDefault();
        
        return view('dashboard.institution.index', compact('institution'));
    }

    /**
     * Show the form for editing institution
     */
    public function edit()
    {
        $institution = Institution::first() ?? Institution::getDefault();
        
        return view('dashboard.institution.edit', compact('institution'));
    }

    /**
     * Update institution information
     */
    public function update(Request $request)
    {
        $request->validate([
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description_ar' => 'required|string',
            'slogan_ar' => 'required|string|max:255',
            'phone_1' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'address_ar' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        $institution = Institution::first() ?? new Institution();

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($institution->logo_path && Storage::disk('public')->exists($institution->logo_path)) {
                Storage::disk('public')->delete($institution->logo_path);
            }

            $logoPath = $request->file('logo')->store('images/institution', 'public');
            $institution->logo_path = $logoPath;
        }

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            // Delete old cover image if exists
            if ($institution->cover_image_path && Storage::disk('public')->exists($institution->cover_image_path)) {
                Storage::disk('public')->delete($institution->cover_image_path);
            }

            $coverPath = $request->file('cover_image')->store('images/institution', 'public');
            $institution->cover_image_path = $coverPath;
        }

        // Update institution data
        $institution->fill($request->except(['logo', 'cover_image']));

        // Handle services array
        if ($request->has('services')) {
            $services = array_filter(explode("\n", $request->services));
            $institution->services = array_map('trim', $services);
        }

        // Handle working hours
        if ($request->has('working_hours')) {
            $workingHours = [];
            foreach ($request->working_hours as $day => $hours) {
                $workingHours[$day] = $hours;
            }
            $institution->working_hours = $workingHours;
        }

        $institution->save();

        // Update statistics
        $institution->updateStatistics();

        return redirect()->route('dashboard.institution.index')
            ->with('success', 'تم تحديث معلومات المؤسسة بنجاح');
    }

    /**
     * Get institution data as JSON
     */
    public function getInstitutionData()
    {
        $institution = Institution::first() ?? Institution::getDefault();
        
        return response()->json([
            'success' => true,
            'data' => $institution
        ]);
    }

    /**
     * Update statistics
     */
    public function updateStatistics()
    {
        $institution = Institution::first();
        
        if ($institution) {
            $institution->updateStatistics();
            
            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الإحصائيات بنجاح',
                'data' => [
                    'students_count' => $institution->students_count,
                    'courses_count' => $institution->courses_count,
                    'trainers_count' => $institution->trainers_count,
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'لم يتم العثور على بيانات المؤسسة'
        ], 404);
    }

    /**
     * Upload gallery images
     */
    public function uploadGallery(Request $request)
    {
        $request->validate([
            'gallery_images.*' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120'
        ]);

        $institution = Institution::first() ?? Institution::getDefault();
        $gallery = $institution->gallery ?? [];

        if ($request->hasFile('gallery_images')) {
            foreach ($request->file('gallery_images') as $image) {
                $path = $image->store('images/institution/gallery', 'public');
                $gallery[] = $path;
            }
        }

        $institution->gallery = $gallery;
        $institution->save();

        return response()->json([
            'success' => true,
            'message' => 'تم رفع الصور بنجاح',
            'gallery' => $gallery
        ]);
    }

    /**
     * Delete gallery image
     */
    public function deleteGalleryImage(Request $request)
    {
        $request->validate([
            'image_path' => 'required|string'
        ]);

        $institution = Institution::first();
        
        if ($institution && $institution->gallery) {
            $gallery = $institution->gallery;
            $imagePath = $request->image_path;

            // Remove from gallery array
            $gallery = array_filter($gallery, function($path) use ($imagePath) {
                return $path !== $imagePath;
            });

            // Delete file from storage
            if (Storage::disk('public')->exists($imagePath)) {
                Storage::disk('public')->delete($imagePath);
            }

            $institution->gallery = array_values($gallery);
            $institution->save();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الصورة بنجاح'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'لم يتم العثور على الصورة'
        ], 404);
    }
}
