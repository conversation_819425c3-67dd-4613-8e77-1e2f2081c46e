<?php

use Illuminate\Support\Facades\Route;


Route::prefix('dashboard')->name('dashboard.')->middleware(['auth'])->group(function () {

    Route::get('/', 'WelcomeController@index')->name('welcome');

    //user routes
    Route::resource('users', 'UserController')->except(['show']);

    //categories routes
    Route::resource('categories', 'CategoryController')->except(['show']);

    //categories routes
    Route::resource('courses', 'CourseController')->except(['show']);
    Route::get('courses/{course}/details', 'CourseController@details')->name('courses.details');
    Route::get('courses/{course}/view', 'CourseController@show')->name('courses.show');

    // Course instructors routes
    Route::get('courses/{course}/instructors', 'CourseController@manageInstructors')->name('courses.instructors');
    Route::post('courses/{course}/add-instructor', 'CourseController@addInstructor')->name('courses.add-instructor');
    Route::delete('courses/{course}/remove-instructor/{instructor}', 'CourseController@removeInstructor')->name('courses.remove-instructor');

    // Route مؤقت لإضافة المدربين
    Route::get('setup-instructors', function() {
        $course = App\Models\Course::find(2);

        if (!$course) {
            return "الدورة رقم 2 غير موجودة";
        }

        // إضافة المدربين
        $instructorIds = [2, 3, 4, 5]; // Hassan, Ali, Ahmed, أمينة

        // التحقق من وجود المدربين
        $instructors = App\Models\User::whereIn('id', $instructorIds)->where('jobs', 'مدرب')->get();

        if ($instructors->count() == 0) {
            return "لا يوجد مدربين في النظام";
        }

        // إضافة المدربين للدورة (تجنب التكرار)
        foreach ($instructorIds as $instructorId) {
            if (!$course->instructors()->where('user_id', $instructorId)->exists()) {
                $course->instructors()->attach($instructorId);
            }
        }

        // تحديث البيانات
        $course->load('instructors');

        $result = "<h2>✅ تم إضافة المدربين بنجاح!</h2>";
        $result .= "<p><strong>الدورة:</strong> " . $course->name . "</p>";
        $result .= "<p><strong>عدد المدربين:</strong> " . $course->instructors->count() . "</p>";
        $result .= "<p><strong>أسماء المدربين:</strong> " . $course->instructor_names . "</p>";

        $result .= "<h3>تفاصيل المدربين:</h3><ul>";
        foreach ($course->instructors as $instructor) {
            $result .= "<li>" . $instructor->name . " (تقييم: " . $instructor->rating . ")</li>";
        }
        $result .= "</ul>";

        $result .= "<br><a href='/dashboard/courses' style='background: #3c8dbc; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>عرض قائمة الدورات</a>";
        $result .= " <a href='/dashboard/courses/2/instructors' style='background: #00a65a; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>إدارة المدربين</a>";

        return $result;
    })->name('setup.instructors');

    // Student Payments routes
    Route::resource('payments', 'StudentPaymentController');
    Route::post('installments/{installment}/mark-paid', 'StudentPaymentController@markAsPaid')->name('installments.mark-paid');
    Route::get('payments-overdue', 'StudentPaymentController@overdueReport')->name('payments.overdue');
    Route::get('payments-due-today', 'StudentPaymentController@dueTodayReport')->name('payments.due-today');
    Route::get('payments-statistics', 'StudentPaymentController@statistics')->name('payments.statistics');
    Route::get('subscriptions-tracking', 'StudentPaymentController@subscriptionsTracking')->name('payments.subscriptions-tracking');

    // Route بديل للاختبار
    Route::get('subscriptions', function() {
        return view('dashboard.subscriptions-simple');
    })->name('subscriptions');

    // Route بديل للدفعات
    Route::get('payments-simple', function() {
        return view('dashboard.payments-simple');
    })->name('payments.simple');

    // Payment Tracking routes
    Route::resource('payment-tracking', 'PaymentTrackingController');
    Route::get('payment-tracking/{paymentTracking}/add-payment', 'PaymentTrackingController@showAddPayment')->name('payment-tracking.show-add-payment');
    Route::post('payment-tracking/{purchase}/create-from-purchase', 'PaymentTrackingController@createFromPurchase')->name('payment-tracking.create-from-purchase');
    Route::post('payment-tracking/{paymentTracking}/add-payment', 'PaymentTrackingController@addPayment')->name('payment-tracking.add-payment');
    Route::post('individual-payments/{payment}/verify', 'PaymentTrackingController@verifyPayment')->name('individual-payments.verify');
    Route::post('individual-payments/{payment}/reject', 'PaymentTrackingController@rejectPayment')->name('individual-payments.reject');
    Route::get('payment-tracking-pending', 'PaymentTrackingController@pendingPayments')->name('payment-tracking.pending');
    Route::get('payment-tracking-overdue', 'PaymentTrackingController@overdueReport')->name('payment-tracking.overdue');
    Route::get('payment-tracking-statistics', 'PaymentTrackingController@statistics')->name('payment-tracking.statistics');
    Route::post('payment-tracking-update-overdue', 'PaymentTrackingController@updateOverdueStatus')->name('payment-tracking.update-overdue');

    // Simple Payment routes
    Route::get('purchases/{purchase}/add-payment', 'SimplePaymentController@showAddPayment')->name('add-payment-simple.show');
    Route::post('purchases/{purchase}/add-payment', 'SimplePaymentController@storePayment')->name('add-payment-simple.store');
    Route::get('purchases/{purchase}/payments', 'SimplePaymentController@showStudentPayments')->name('student-payments.show');
    Route::get('payments-report', 'SimplePaymentController@paymentsReport')->name('payments-report');
    Route::get('simple-payment-tracking', 'SimplePaymentController@simplePaymentTracking')->name('simple-payment-tracking');

    //categories routes
    Route::resource('coaches', 'CoacheContoller')->except(['show']);

    //categories routes
    Route::resource('certificates', 'CertificateController')->except(['show']);

    //AdvisoryService routes
    Route::resource('advisoryServices', 'AdvisoryServiceController')->except(['show']);

    //purchase routes
    Route::resource('purchases', 'PurchaseController');

    //purchase routes
    Route::resource('posts', 'PostController');

    //settings  routes
    Route::post('settings.store', 'SettingController@store')->name('settings.store');
    Route::get('about_index', 'SettingController@about_index')->name('about_index');
    Route::get('links_index', 'SettingController@links_index')->name('links_index');
    Route::get('title_index', 'SettingController@title_index')->name('title_index');
    Route::get('/founder/{id}', 'SettingController@founder')->name('founder');
    Route::put('/founder_update/{id}', 'SettingController@founder_update')->name('founder.update');

    //institution routes
    Route::get('institution', 'InstitutionController@index')->name('institution.index');
    Route::get('institution/edit', 'InstitutionController@edit')->name('institution.edit');
    Route::put('institution/update', 'InstitutionController@update')->name('institution.update');
    Route::get('institution/data', 'InstitutionController@getInstitutionData')->name('institution.data');
    Route::post('institution/statistics', 'InstitutionController@updateStatistics')->name('institution.statistics');
    Route::post('institution/gallery', 'InstitutionController@uploadGallery')->name('institution.gallery.upload');
    Route::delete('institution/gallery', 'InstitutionController@deleteGalleryImage')->name('institution.gallery.delete');

});//end of route group
