<?php

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategoriesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // إنشاء فئات تجريبية لأكاديمية Leaders Vision
        $categories = [
            [
                'name' => 'البرمجة وتطوير المواقع',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'التسويق الرقمي',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'إدارة الأعمال',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'التصميم الجرافيكي',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'القيادة والإدارة',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'ريادة الأعمال',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
