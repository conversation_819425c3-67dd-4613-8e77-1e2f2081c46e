@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">
        <section class="content-header">
            <h1>متابعة دفعات المشتركين</h1>
            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
                <li class="active">متابعة الدفعات</li>
            </ol>
        </section>

        <section class="content">
            
            <!-- إحصائيات سريعة -->
            <div class="row">
                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-blue">
                        <div class="inner">
                            <h3>{{ $stats['total_subscribers'] }}</h3>
                            <p>إجمالي المشتركين</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-users"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-green">
                        <div class="inner">
                            <h3>{{ number_format($stats['total_revenue'], 0) }}</h3>
                            <p>إجمالي المحصل (دج)</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-money"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-yellow">
                        <div class="inner">
                            <h3>{{ number_format($stats['pending_revenue'], 0) }}</h3>
                            <p>المبلغ المتبقي (دج)</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-clock-o"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-red">
                        <div class="inner">
                            <h3>{{ $stats['fully_paid'] }}</h3>
                            <p>مدفوع بالكامل</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات السريعة -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">إجراءات سريعة</h3>
                        </div>
                        <div class="box-body">
                            <a href="{{ route('dashboard.purchases.index') }}" class="btn btn-primary">
                                <i class="fa fa-list"></i> جميع الطلبات
                            </a>
                            <a href="{{ route('dashboard.purchases.create') }}" class="btn btn-success">
                                <i class="fa fa-plus"></i> إضافة طلب جديد
                            </a>
                            <a href="{{ route('dashboard.courses.index') }}" class="btn btn-info">
                                <i class="fa fa-graduation-cap"></i> إدارة الدورات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">البحث والفلترة</h3>
                        </div>
                        <div class="box-body">
                            <form method="GET" action="{{ route('dashboard.simple-payment-tracking') }}">
                                <div class="row">
                                    <div class="col-md-3">
                                        <input type="text" name="search" class="form-control" 
                                               placeholder="البحث..." value="{{ request('search') }}">
                                    </div>
                                    <div class="col-md-2">
                                        <select name="payment_status" class="form-control">
                                            <option value="">جميع حالات الدفع</option>
                                            <option value="not_paid" {{ request('payment_status') == 'not_paid' ? 'selected' : '' }}>لم يدفع</option>
                                            <option value="partial" {{ request('payment_status') == 'partial' ? 'selected' : '' }}>دفع جزئي</option>
                                            <option value="fully_paid" {{ request('payment_status') == 'fully_paid' ? 'selected' : '' }}>مدفوع بالكامل</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select name="course_id" class="form-control">
                                            <option value="">جميع الدورات</option>
                                            @foreach($courses as $course)
                                                <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                                    {{ $course->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select name="status" class="form-control">
                                            <option value="">جميع الحالات</option>
                                            <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>غير مفعل</option>
                                            <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>مفعل</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-search"></i> بحث
                                        </button>
                                        <a href="{{ route('dashboard.simple-payment-tracking') }}" class="btn btn-default">
                                            <i class="fa fa-refresh"></i>
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول متابعة الدفعات -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">قائمة متابعة دفعات المشتركين</h3>
                        </div>
                        <div class="box-body table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الطالب</th>
                                        <th>الدورة</th>
                                        <th>سعر الدورة</th>
                                        <th>المدفوع</th>
                                        <th>المتبقي</th>
                                        <th>التقدم</th>
                                        <th>حالة الدفع</th>
                                        <th>حالة الطلب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($purchases as $purchase)
                                        @php
                                            $purchase->load('course');
                                            $coursePrice = $purchase->getCoursePrice();
                                            $paidAmount = $purchase->paid_amount;
                                            $remaining = $purchase->getRemainingAmount();
                                            $progress = $purchase->getPaymentProgress();
                                            $isFullyPaid = $purchase->isFullyPaid();
                                        @endphp
                                        <tr class="{{ $remaining > 0 && $purchase->created_at->diffInDays() > 7 ? 'warning' : '' }}">
                                            <td>{{ $purchase->id }}</td>
                                            <td>
                                                <strong>{{ $purchase->first_name }} {{ $purchase->last_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $purchase->email }}</small>
                                                <br>
                                                <small class="text-muted">{{ $purchase->phone }}</small>
                                            </td>
                                            <td>
                                                <span class="label label-info">
                                                    {{ $purchase->name_course ?: ($purchase->course ? $purchase->course->name : 'غير محدد') }}
                                                </span>
                                            </td>
                                            <td>{{ number_format($coursePrice, 0) }} دج</td>
                                            <td>{{ number_format($paidAmount, 0) }} دج</td>
                                            <td>{{ number_format($remaining, 0) }} دج</td>
                                            <td>
                                                <div class="progress progress-xs">
                                                    <div class="progress-bar progress-bar-{{ $progress >= 100 ? 'success' : ($progress >= 50 ? 'warning' : 'danger') }}" 
                                                         style="width: {{ $progress }}%"></div>
                                                </div>
                                                <span class="badge">{{ $progress }}%</span>
                                            </td>
                                            <td>
                                                @if($isFullyPaid)
                                                    <span class="label label-success">مدفوع بالكامل</span>
                                                @elseif($paidAmount > 0)
                                                    <span class="label label-warning">دفع جزئي</span>
                                                @else
                                                    <span class="label label-danger">لم يدفع</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($purchase->status == '1')
                                                    <span class="label label-success">مفعل</span>
                                                @else
                                                    <span class="label label-warning">غير مفعل</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown">
                                                        إجراءات <span class="caret"></span>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a href="{{ route('dashboard.purchases.show', $purchase) }}">
                                                                <i class="fa fa-eye"></i> عرض التفاصيل
                                                            </a>
                                                        </li>
                                                        @if(!$isFullyPaid)
                                                        <li>
                                                            <a href="{{ route('dashboard.add-payment-simple.show', $purchase) }}">
                                                                <i class="fa fa-plus"></i> إضافة دفعة
                                                            </a>
                                                        </li>
                                                        @endif
                                                        <li class="divider"></li>
                                                        <li>
                                                            <a href="{{ route('dashboard.purchases.edit', $purchase) }}">
                                                                <i class="fa fa-edit"></i> تعديل
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="10" class="text-center">لا توجد طلبات</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                        <div class="box-footer">
                            {{ $purchases->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
            </div>

        </section>
    </div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تحديث تلقائي للصفحة كل 5 دقائق
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
@endpush
