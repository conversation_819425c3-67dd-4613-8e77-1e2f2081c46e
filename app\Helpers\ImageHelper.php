<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class ImageHelper
{
    /**
     * Get image URL with fallback
     */
    public static function getImageUrl($path, $fallback = null)
    {
        if (!$path) {
            return $fallback ? asset($fallback) : asset('images/logo.png');
        }

        // إذا كان المسار يبدأ بـ http أو https
        if (substr($path, 0, 4) === 'http') {
            return $path;
        }

        // إذا كان المسار يبدأ بـ /
        if (substr($path, 0, 1) === '/') {
            $path = ltrim($path, '/');
        }

        // التحقق من وجود الملف
        if (File::exists(public_path($path))) {
            return asset($path);
        }

        // البحث في مجلدات مختلفة
        $searchPaths = [
            'images/' . $path,
            'uploads/' . $path,
            'storage/' . $path,
            'home_file/assets/images/' . $path,
        ];

        foreach ($searchPaths as $searchPath) {
            if (File::exists(public_path($searchPath))) {
                return asset($searchPath);
            }
        }

        // إرجاع الصورة الافتراضية
        return $fallback ? asset($fallback) : asset('images/logo.png');
    }

    /**
     * Get user image with fallback
     */
    public static function getUserImage($user)
    {
        if (!$user || !$user->image_path) {
            return asset('uploads/user_images/default.png');
        }

        return self::getImageUrl($user->image_path, 'uploads/user_images/default.png');
    }

    /**
     * Get course image with fallback
     */
    public static function getCourseImage($course)
    {
        if (!$course || !$course->image_path) {
            return asset('home_file/assets/images/courses/1.jpg');
        }

        return self::getImageUrl($course->image_path, 'home_file/assets/images/courses/1.jpg');
    }

    /**
     * Get coach image with fallback
     */
    public static function getCoachImage($coach)
    {
        if (!$coach || !$coach->image_path) {
            return asset('uploads/coache_images/default.jpg');
        }

        return self::getImageUrl($coach->image_path, 'uploads/coache_images/default.jpg');
    }

    /**
     * Get post image with fallback
     */
    public static function getPostImage($post)
    {
        if (!$post || !$post->image_path) {
            return asset('uploads/post_images/default.jpg');
        }

        return self::getImageUrl($post->image_path, 'uploads/post_images/default.jpg');
    }

    /**
     * Get founder image with fallback
     */
    public static function getFounderImage($founder)
    {
        if (!$founder || !$founder->image_path) {
            return asset('images/logo.png');
        }

        return self::getImageUrl($founder->image_path, 'images/logo.png');
    }

    /**
     * Get logo image
     */
    public static function getLogoImage($type = 'main')
    {
        $logos = [
            'main' => 'images/logo.png',
            'dark' => 'images/logo.png',
            'light' => 'images/logo.png',
            'small' => 'images/logo.png',
        ];

        $logoPath = $logos[$type] ?? $logos['main'];

        if (File::exists(public_path($logoPath))) {
            return asset($logoPath);
        }

        // البحث عن شعارات بديلة
        $fallbackLogos = [
            'home_file/assets/images/logo.png',
            'home_file/assets/images/logo-dark.png',
            'home_file/assets/images/Hidden-bits-logo.png',
        ];

        foreach ($fallbackLogos as $fallback) {
            if (File::exists(public_path($fallback))) {
                return asset($fallback);
            }
        }

        return asset('images/logo.png');
    }

    /**
     * Get banner/background image
     */
    public static function getBannerImage($page = 'home')
    {
        $banners = [
            'home' => 'home_file/assets/images/banner/home1.jpg',
            'about' => 'home_file/assets/images/breadcrumbs/1.jpg',
            'courses' => 'home_file/assets/images/breadcrumbs/2.jpg',
            'contact' => 'home_file/assets/images/breadcrumbs/4.jpg',
        ];

        $bannerPath = $banners[$page] ?? $banners['home'];

        return self::getImageUrl($bannerPath, 'home_file/assets/images/bg/main-home.jpg');
    }

    /**
     * Optimize image path for web
     */
    public static function optimizeImagePath($path)
    {
        if (!$path) {
            return null;
        }

        // إزالة المسارات المطلقة
        $path = str_replace([
            public_path(),
            storage_path(),
            base_path(),
        ], '', $path);

        // تنظيف المسار
        $path = str_replace('\\', '/', $path);
        $path = ltrim($path, '/');

        return $path;
    }

    /**
     * Check if image exists
     */
    public static function imageExists($path)
    {
        if (!$path) {
            return false;
        }

        // التحقق من المسار المباشر
        if (File::exists(public_path($path))) {
            return true;
        }

        // البحث في مجلدات مختلفة
        $searchPaths = [
            'images/' . $path,
            'uploads/' . $path,
            'storage/' . $path,
            'home_file/assets/images/' . $path,
        ];

        foreach ($searchPaths as $searchPath) {
            if (File::exists(public_path($searchPath))) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get image size info
     */
    public static function getImageInfo($path)
    {
        $fullPath = public_path($path);

        if (!File::exists($fullPath)) {
            return null;
        }

        $info = getimagesize($fullPath);

        if (!$info) {
            return null;
        }

        return [
            'width' => $info[0],
            'height' => $info[1],
            'type' => $info[2],
            'mime' => $info['mime'],
            'size' => File::size($fullPath),
            'url' => asset($path)
        ];
    }

    /**
     * Generate responsive image HTML
     */
    public static function responsiveImage($path, $alt = '', $class = '', $sizes = [])
    {
        $url = self::getImageUrl($path);

        $html = '<img src="' . $url . '"';

        if ($alt) {
            $html .= ' alt="' . htmlspecialchars($alt) . '"';
        }

        if ($class) {
            $html .= ' class="' . htmlspecialchars($class) . '"';
        }

        $html .= ' loading="lazy"';
        $html .= ' onerror="this.src=\'' . asset('images/logo.png') . '\'"';
        $html .= '>';

        return $html;
    }

    /**
     * Get placeholder image
     */
    public static function getPlaceholder($width = 300, $height = 200, $text = 'صورة')
    {
        return "https://via.placeholder.com/{$width}x{$height}/2563eb/ffffff?text=" . urlencode($text);
    }
}
