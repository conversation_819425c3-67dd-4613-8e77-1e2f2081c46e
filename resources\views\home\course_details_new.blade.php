@extends('layouts.home.app')

@section('content')
<div dir="rtl" style="direction: rtl; text-align: right; margin-top: 80px;">

    <!-- Hero Section -->
    <section class="hero-section" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); padding: 60px 0; color: white;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <div class="course-badge mb-3">
                            <span class="badge badge-light px-3 py-2" style="font-size: 14px; border-radius: 20px;">
                                <i class="fa fa-graduation-cap mr-2"></i>
                                دورة تدريبية متخصصة
                            </span>
                        </div>
                        <h1 class="hero-title mb-3" style="font-size: 2.5rem; font-weight: 700; line-height: 1.2;">
                            {{ $details_course->name }}
                        </h1>
                        <p class="hero-subtitle mb-4" style="font-size: 1.1rem; opacity: 0.9;">
                            {{ $details_course->Short_description }}
                        </p>

                        <!-- Course Meta Info -->
                        <div class="course-meta d-flex flex-wrap gap-4 mb-4">
                            <div class="meta-item d-flex align-items-center">
                                <i class="fa fa-clock text-warning mr-2"></i>
                                <span>{{ $details_course->time }} ساعة</span>
                            </div>
                            <div class="meta-item d-flex align-items-center">
                                <i class="fa fa-star text-warning mr-2"></i>
                                <span>{{ $details_course->rating }}/5</span>
                            </div>
                            <div class="meta-item d-flex align-items-center">
                                <i class="fa fa-users text-info mr-2"></i>
                                <span>{{ $details_course->studant_count ?? '0' }} طالب</span>
                            </div>
                            <div class="meta-item d-flex align-items-center">
                                <i class="fa fa-list text-success mr-2"></i>
                                <span>{{ $details_course->category->name ?? 'غير محدد' }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="hero-media">
                        <!-- Course Image -->
                        <div class="course-image-container mb-3" style="position: relative; border-radius: 15px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                            <img src="{{ $details_course->image_path }}"
                                 alt="{{ $details_course->name }}"
                                 class="course-main-image"
                                 style="width: 100%; height: 300px; object-fit: cover;">

                            <!-- Video Play Button -->
                            @if($details_course->demo_video)
                            <div class="video-play-overlay" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); cursor: pointer;" onclick="showVideoModal()">
                                <div class="play-button" style="width: 80px; height: 80px; background: rgba(231, 76, 60, 0.9); border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
                                    <i class="fa fa-play text-white" style="font-size: 30px; margin-left: 5px;"></i>
                                </div>
                            </div>
                            @endif
                        </div>

                        <!-- Price Card -->
                        <div class="price-card bg-white text-dark p-4 rounded-lg shadow-lg">
                            @if($details_course->show_price)
                            <div class="price-display text-center mb-3">
                                <span class="price-amount" style="font-size: 2.5rem; font-weight: 700; color: #e74c3c;">
                                    {{ number_format($details_course->price, 0) }}
                                </span>
                                <span class="price-currency" style="font-size: 1.2rem; color: #2c3e50; font-weight: 600;">دج</span>
                            </div>
                            @else
                            <div class="contact-for-price text-center mb-3">
                                <div style="font-size: 1.3rem; font-weight: 600; color: #e74c3c;">
                                    <i class="fa fa-phone mr-2"></i>
                                    اتصل للاستفسار عن السعر
                                </div>
                                <div class="contact-numbers mt-2" style="font-size: 14px; color: #2c3e50;">
                                    <div><i class="fa fa-phone mr-1 text-success"></i> 0774479525</div>
                                    <div><i class="fa fa-phone mr-1 text-success"></i> 0665657400</div>
                                </div>
                            </div>
                            @endif

                            <a href="{{ route('purchase.create', $details_course->id) }}"
                               class="btn btn-lg btn-block enrollment-btn"
                               style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); border: none; border-radius: 10px; padding: 15px; font-weight: 600; color: white; text-decoration: none;">
                                <i class="fa fa-shopping-cart mr-2"></i>
                                @if($details_course->show_price)
                                    اشترك الآن
                                @else
                                    استفسر عن الدورة
                                @endif
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="main-content py-5" style="background: #f8f9fa;">
        <div class="container">
            <div class="row">
                <!-- Content Area -->
                <div class="col-lg-8">
                    <!-- Row 1: Description and Objectives -->
                    <div class="row mb-4">
                        <!-- Course Description -->
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100" style="border: none; border-radius: 15px;">
                                <div class="card-header" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); border-radius: 15px 15px 0 0; color: white; text-align: right;">
                                    <h5 class="card-title mb-0" style="font-weight: 600;">
                                        <i class="fa fa-info-circle mr-2"></i>
                                        وصف الدورة
                                    </h5>
                                </div>
                                <div class="card-body p-4">
                                    <div class="description-content" style="line-height: 1.8; color: #2c3e50;">
                                        {{ $details_course->description }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Objectives -->
                        @if($details_course->course_objectives)
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100" style="border: none; border-radius: 15px;">
                                <div class="card-header" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%); border-radius: 15px 15px 0 0; color: white; text-align: right;">
                                    <h5 class="card-title mb-0" style="font-weight: 600;">
                                        <i class="fa fa-target mr-2"></i>
                                        أهداف الدورة
                                    </h5>
                                </div>
                                <div class="card-body p-4">
                                    <div class="objectives-content" style="line-height: 1.8; color: #2c3e50;">
                                        {!! nl2br(e($details_course->course_objectives)) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Row 2: Target Audience and Instructors -->
                    <div class="row mb-4">
                        <!-- Target Audience -->
                        @if($details_course->target_audience)
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100" style="border: none; border-radius: 15px;">
                                <div class="card-header" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); border-radius: 15px 15px 0 0; color: white; text-align: right;">
                                    <h5 class="card-title mb-0" style="font-weight: 600;">
                                        <i class="fa fa-users mr-2"></i>
                                        الفئة المستهدفة
                                    </h5>
                                </div>
                                <div class="card-body p-4">
                                    <div class="target-audience-content" style="line-height: 1.8; color: #2c3e50;">
                                        {!! nl2br(e($details_course->target_audience)) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Course Instructors -->
                        @if($instructors && $instructors->count() > 0)
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100" style="border: none; border-radius: 15px;">
                                <div class="card-header" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); border-radius: 15px 15px 0 0; color: white; text-align: right;">
                                    <h5 class="card-title mb-0" style="font-weight: 600;">
                                        <i class="fa fa-graduation-cap mr-2"></i>
                                        مدربي الدورة
                                    </h5>
                                </div>
                                <div class="card-body p-4">
                                    @foreach($instructors->take(3) as $instructor)
                                    <div class="instructor-item mb-3 p-3" style="background: #f8f9fa; border-radius: 10px; border-right: 4px solid #9b59b6;">
                                        <div class="d-flex align-items-center">
                                            <div class="instructor-avatar ml-3">
                                                @if($instructor->image)
                                                    <img src="{{ asset('uploads/user_images/' . $instructor->image) }}"
                                                         alt="{{ $instructor->name }}"
                                                         class="rounded-circle"
                                                         style="width: 50px; height: 50px; object-fit: cover;">
                                                @else
                                                    <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center"
                                                         style="width: 50px; height: 50px; background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); color: white; font-weight: 600;">
                                                        {{ substr($instructor->name ?? 'مدرب', 0, 1) }}
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="instructor-details">
                                                <h6 class="mb-1" style="color: #2c3e50; font-weight: 600;">
                                                    {{ $instructor->name ?? 'مدرب' }}
                                                </h6>
                                                <small style="color: #7f8c8d;">{{ $instructor->jobs ?? 'مدرب' }}</small>
                                                @if($instructor->rating)
                                                <div class="rating mt-1">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        @if($i <= $instructor->rating)
                                                            <i class="fa fa-star text-warning" style="font-size: 12px;"></i>
                                                        @else
                                                            <i class="fa fa-star-o text-muted" style="font-size: 12px;"></i>
                                                        @endif
                                                    @endfor
                                                    <span style="font-size: 12px; color: #666;">({{ $instructor->rating }}/5)</span>
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                    @if($instructors->count() > 3)
                                    <small class="text-muted">و {{ $instructors->count() - 3 }} مدربين آخرين</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Course Info Card -->
                    <div class="card shadow-sm mb-4" style="border: none; border-radius: 15px;">
                        <div class="card-header" style="background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); border-radius: 15px 15px 0 0; color: white; text-align: right;">
                            <h5 class="card-title mb-0" style="font-weight: 600;">
                                <i class="fa fa-info-circle mr-2"></i>
                                معلومات الدورة
                            </h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="course-info-list">
                                <!-- Course ID -->
                                <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
                                    <span class="info-label" style="font-weight: 600; color: #2c3e50;">
                                        <i class="fa fa-hashtag mr-2 text-primary"></i>رقم الدورة:
                                    </span>
                                    <span class="info-value" style="font-weight: 600; color: #2c3e50;">
                                        #{{ $details_course->id }}
                                    </span>
                                </div>

                                <!-- Price -->
                                @if($details_course->show_price)
                                <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
                                    <span class="info-label" style="font-weight: 600; color: #2c3e50;">
                                        <i class="fa fa-tag mr-2 text-danger"></i>السعر:
                                    </span>
                                    <span class="info-value" style="font-weight: 700; color: #e74c3c; font-size: 18px;">
                                        {{ number_format($details_course->price, 0) }} دج
                                    </span>
                                </div>
                                @else
                                <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
                                    <span class="info-label" style="font-weight: 600; color: #2c3e50;">
                                        <i class="fa fa-phone mr-2 text-success"></i>السعر:
                                    </span>
                                    <span class="info-value" style="font-weight: 600; color: #e74c3c; font-size: 14px;">
                                        اتصل للاستفسار
                                    </span>
                                </div>
                                @endif

                                <!-- Duration -->
                                <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
                                    <span class="info-label" style="font-weight: 600; color: #2c3e50;">
                                        <i class="fa fa-clock mr-2 text-success"></i>المدة:
                                    </span>
                                    <span class="info-value" style="font-weight: 600; color: #2c3e50;">
                                        {{ $details_course->time }} ساعة
                                    </span>
                                </div>

                                <!-- Rating -->
                                <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
                                    <span class="info-label" style="font-weight: 600; color: #2c3e50;">
                                        <i class="fa fa-star mr-2 text-warning"></i>التقييم:
                                    </span>
                                    <div class="rating-display">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= $details_course->rating)
                                                <i class="fa fa-star text-warning"></i>
                                            @else
                                                <i class="fa fa-star-o text-muted"></i>
                                            @endif
                                        @endfor
                                        <span class="rating-text mr-2" style="font-weight: 600; color: #2c3e50;">
                                            ({{ $details_course->rating }}/5)
                                        </span>
                                    </div>
                                </div>

                                <!-- Students Count -->
                                <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
                                    <span class="info-label" style="font-weight: 600; color: #2c3e50;">
                                        <i class="fa fa-users mr-2 text-info"></i>عدد الطلاب:
                                    </span>
                                    <span class="info-value" style="font-weight: 600; color: #2c3e50;">
                                        {{ $details_course->studant_count ?? '0' }} طالب
                                    </span>
                                </div>

                                <!-- Category -->
                                <div class="info-item d-flex justify-content-between align-items-center mb-3 pb-3" style="border-bottom: 1px solid #eee;">
                                    <span class="info-label" style="font-weight: 600; color: #2c3e50;">
                                        <i class="fa fa-list mr-2 text-primary"></i>القسم:
                                    </span>
                                    <span class="info-value" style="font-weight: 600; color: #2c3e50;">
                                        {{ $details_course->category->name ?? 'غير محدد' }}
                                    </span>
                                </div>

                                <!-- Created Date -->
                                <div class="info-item d-flex justify-content-between align-items-center mb-3">
                                    <span class="info-label" style="font-weight: 600; color: #2c3e50;">
                                        <i class="fa fa-calendar mr-2 text-secondary"></i>تاريخ الإنشاء:
                                    </span>
                                    <span class="info-value" style="font-weight: 600; color: #2c3e50;">
                                        {{ $details_course->created_at->format('Y/m/d') }}
                                    </span>
                                </div>
                            </div>

                            <!-- Enrollment Button -->
                            <div class="enrollment-section mt-4 pt-4" style="border-top: 2px solid #f8f9fa;">
                                <a href="{{ route('purchase.create', $details_course->id) }}"
                                   class="btn btn-lg btn-block enrollment-btn-sidebar"
                                   style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                                          border: none;
                                          border-radius: 10px;
                                          padding: 15px;
                                          font-weight: 600;
                                          color: white;
                                          text-decoration: none;
                                          transition: all 0.3s ease;">
                                    <i class="fa fa-shopping-cart mr-2"></i>
                                    @if($details_course->show_price)
                                        اشترك الآن
                                    @else
                                        استفسر عن الدورة
                                    @endif
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Video Preview Card -->
                    @if($details_course->demo_video)
                    <div class="card shadow-sm mb-4" style="border: none; border-radius: 15px;">
                        <div class="card-header" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); border-radius: 15px 15px 0 0; color: white; text-align: right;">
                            <h5 class="card-title mb-0" style="font-weight: 600;">
                                <i class="fa fa-play-circle mr-2"></i>
                                معاينة الدورة
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="video-container" style="position: relative; border-radius: 0 0 15px 15px; overflow: hidden;">
                                <video
                                    controls
                                    poster="{{ $details_course->image_path }}"
                                    style="width: 100%; height: 250px; object-fit: cover;"
                                    class="course-video">
                                    <source src="{{ asset('uploads/'.$details_course->demo_video) }}" type="video/mp4">
                                    <source src="{{ asset('uploads/'.$details_course->demo_video) }}" type="video/webm">
                                    متصفحك لا يدعم تشغيل الفيديو
                                </video>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Contact Card -->
                    <div class="card shadow-sm" style="border: none; border-radius: 15px;">
                        <div class="card-header" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); border-radius: 15px 15px 0 0; color: white; text-align: right;">
                            <h5 class="card-title mb-0" style="font-weight: 600;">
                                <i class="fa fa-phone mr-2"></i>
                                تحتاج مساعدة؟
                            </h5>
                        </div>
                        <div class="card-body p-4 text-center">
                            <p class="mb-3" style="color: #2c3e50; font-weight: 500;">تواصل معنا للحصول على استشارة مجانية</p>
                            <div class="contact-info">
                                <div class="contact-item mb-3 p-3" style="background: #f8f9fa; border-radius: 8px; border-right: 4px solid #27ae60;">
                                    <i class="fa fa-phone text-success mr-2"></i>
                                    <span style="color: #2c3e50; font-weight: 600;">0774479525</span>
                                </div>
                                <div class="contact-item mb-3 p-3" style="background: #f8f9fa; border-radius: 8px; border-right: 4px solid #27ae60;">
                                    <i class="fa fa-phone text-success mr-2"></i>
                                    <span style="color: #2c3e50; font-weight: 600;">0665657400</span>
                                </div>
                                <div class="contact-item p-3" style="background: #f8f9fa; border-radius: 8px; border-right: 4px solid #e74c3c;">
                                    <i class="fa fa-map-marker text-danger mr-2"></i>
                                    <span style="color: #2c3e50; font-weight: 600;">برج بوعريريج، الجزائر</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Modal -->
    @if($details_course->demo_video)
    <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content" style="border-radius: 15px; border: none;">
                <div class="modal-header" style="border-bottom: none; background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; border-radius: 15px 15px 0 0;">
                    <h5 class="modal-title" id="videoModalLabel">
                        <i class="fa fa-play-circle mr-2"></i>
                        معاينة الدورة - {{ $details_course->name }}
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-0">
                    <div class="video-container" style="position: relative; border-radius: 0 0 15px 15px; overflow: hidden;">
                        <video
                            id="courseVideo"
                            controls
                            style="width: 100%; height: 400px; object-fit: cover;"
                            class="course-video">
                            <source src="{{ asset('uploads/'.$details_course->demo_video) }}" type="video/mp4">
                            <source src="{{ asset('uploads/'.$details_course->demo_video) }}" type="video/webm">
                            متصفحك لا يدعم تشغيل الفيديو
                        </video>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

</div>
@endsection

@push('styles')
<style>
/* Hero Section */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="4"/></g></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-media {
    position: relative;
    z-index: 2;
}

/* Course Image and Video */
.course-image-container {
    transition: all 0.3s ease;
}

.course-image-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.4) !important;
}

.video-play-overlay {
    opacity: 0;
    transition: all 0.3s ease;
}

.course-image-container:hover .video-play-overlay {
    opacity: 1;
}

.play-button {
    transition: all 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
    background: rgba(231, 76, 60, 1) !important;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

/* Cards */
.card {
    transition: all 0.3s ease;
    border: none !important;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
}

.card-header {
    border-bottom: none !important;
}

/* Buttons */
.enrollment-btn,
.enrollment-btn-sidebar {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.enrollment-btn:hover,
.enrollment-btn-sidebar:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%) !important;
    color: white !important;
    text-decoration: none !important;
}

/* Info Items */
.info-item {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 8px;
    margin: -8px;
}

.info-item:hover {
    background: rgba(52, 73, 94, 0.05);
    transform: translateX(-5px);
}

/* Contact Items */
.contact-item {
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Instructor Items */
.instructor-item {
    transition: all 0.3s ease;
}

.instructor-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.15);
    background: #f0f0f0 !important;
}

/* Video */
.course-video {
    transition: all 0.3s ease;
}

.course-video:hover {
    transform: scale(1.02);
}

/* Rating Stars */
.rating-display i {
    transition: all 0.2s ease;
}

.rating-display:hover i {
    transform: scale(1.2);
}

/* Responsive */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem !important;
    }

    .hero-subtitle {
        font-size: 1rem !important;
    }

    .course-meta {
        flex-direction: column;
        gap: 10px !important;
    }

    .meta-item {
        justify-content: center;
    }

    .course-image-container {
        margin-bottom: 20px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-content {
    animation: fadeInUp 1s ease-out;
}

.hero-media {
    animation: fadeInRight 1s ease-out;
}

.card {
    animation: fadeInUp 1s ease-out;
}

/* Price Animation */
.price-amount {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
</style>
@endpush

@push('scripts')
<script>
// Function to show video modal
function showVideoModal() {
    $('#videoModal').modal('show');
}

$(document).ready(function() {
    // Pause video when modal is closed
    $('#videoModal').on('hidden.bs.modal', function () {
        var video = document.getElementById('courseVideo');
        if (video) {
            video.pause();
            video.currentTime = 0;
        }
    });

    // Smooth scroll for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Video loading animation
    $('.course-video').on('loadstart', function() {
        $(this).closest('.video-container').addClass('loading');
    });

    $('.course-video').on('loadeddata', function() {
        $(this).closest('.video-container').removeClass('loading');
    });

    // Counter animation for price
    $('.price-amount').each(function() {
        var $this = $(this);
        var countTo = $this.text().replace(/[^0-9]/g, '');

        if (countTo && !isNaN(countTo)) {
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    var formattedNumber = Math.floor(this.countNum).toLocaleString();
                    $this.text(formattedNumber);
                },
                complete: function() {
                    var formattedNumber = parseInt(countTo).toLocaleString();
                    $this.text(formattedNumber);
                }
            });
        }
    });
});
</script>
@endpush