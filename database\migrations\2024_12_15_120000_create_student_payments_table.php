<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStudentPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('student_payments', function (Blueprint $table) {
            $table->id();
            
            // معلومات الطالب
            $table->string('student_name')->comment('اسم الطالب');
            $table->string('student_email')->comment('بريد الطالب');
            $table->string('student_phone')->comment('هاتف الطالب');
            
            // معلومات الدورة
            $table->bigInteger('course_id')->unsigned();
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
            $table->string('course_name')->comment('اسم الدورة');
            
            // معلومات الدفع
            $table->decimal('total_amount', 10, 2)->comment('المبلغ الإجمالي');
            $table->decimal('paid_amount', 10, 2)->default(0)->comment('المبلغ المدفوع');
            $table->decimal('remaining_amount', 10, 2)->comment('المبلغ المتبقي');
            
            // حالة الدفع
            $table->enum('payment_status', [
                'pending',      // في الانتظار
                'partial',      // دفع جزئي
                'completed',    // مكتمل
                'cancelled',    // ملغي
                'refunded'      // مسترد
            ])->default('pending')->comment('حالة الدفع');
            
            // تواريخ مهمة
            $table->date('enrollment_date')->comment('تاريخ التسجيل');
            $table->date('due_date')->nullable()->comment('تاريخ الاستحقاق');
            $table->date('completion_date')->nullable()->comment('تاريخ إكمال الدفع');
            
            // معلومات إضافية
            $table->text('notes')->nullable()->comment('ملاحظات');
            $table->string('payment_method')->nullable()->comment('طريقة الدفع');
            $table->string('reference_number')->nullable()->comment('رقم المرجع');
            
            // ربط مع جدول المشتريات الأصلي
            $table->bigInteger('purchase_id')->unsigned()->nullable();
            $table->foreign('purchase_id')->references('id')->on('purchases')->onDelete('set null');
            
            $table->timestamps();
            
            // فهارس للبحث السريع
            $table->index(['payment_status']);
            $table->index(['course_id']);
            $table->index(['student_email']);
            $table->index(['enrollment_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_payments');
    }
}
