# نظام الدفعات باستخدام جدول payment_installments
## Payment Installments System Using Existing Table

---

## ✅ **تم تحديث النظام لاستخدام الجداول الموجودة!**

النظام الآن يستخدم الجداول الموجودة في قاعدة البيانات:
- `student_payments_tracking` - لتتبع إجمالي الدفعات
- `payment_installments` - لتخزين تفاصيل كل قسط

---

## 🗄️ **هيكل الجداول المستخدمة:**

### **جدول student_payments_tracking:**
```sql
- id (معرف التتبع)
- purchase_id (ربط مع جدول purchases)
- student_name, student_email, student_phone
- course_id, course_name, course_price
- total_paid, remaining_amount
- payment_status, enrollment_date
- is_active, created_at, updated_at
```

### **جدول payment_installments:**
```sql
- id (معرف القسط)
- student_payment_id (ربط مع student_payments_tracking)
- installment_number (رقم القسط)
- amount (مبلغ القسط)
- due_date (تاريخ الاستحقاق)
- paid_date (تاريخ الدفع الفعلي)
- status (حالة القسط: pending, paid, overdue)
- payment_method (طريقة الدفع)
- transaction_id (رقم المعاملة)
- receipt_image (صورة الإيصال)
- notes (ملاحظات)
- created_at, updated_at
```

---

## 🔄 **آلية العمل:**

### **عند إضافة دفعة جديدة:**
1. ✅ **البحث عن سجل في student_payments_tracking**
2. ✅ **إنشاء سجل جديد إذا لم يوجد**
3. ✅ **حساب رقم القسط التالي**
4. ✅ **إضافة القسط في payment_installments**
5. ✅ **تحديث paid_amount في purchases**
6. ✅ **تحديث حالة الطلب إذا اكتمل الدفع**

### **البيانات المحفوظة لكل قسط:**
- 🔢 **رقم القسط** - تسلسلي تلقائي
- 💰 **مبلغ القسط** - المبلغ المدفوع
- 📅 **تاريخ الدفع** - متى تم الدفع فعلياً
- 💳 **طريقة الدفع** - نقد، تحويل، بطاقة، إلخ
- 🧾 **رقم المعاملة** - للمرجعية
- 🖼️ **صورة الإيصال** - إثبات الدفع
- 📝 **ملاحظات** - تفاصيل إضافية

---

## 🌐 **الروابط والصفحات:**

### **صفحة إضافة دفعة:**
```
http://localhost:8000/dashboard/purchases/{ID}/add-payment
```

### **صفحة عرض دفعات الطالب:**
```
http://localhost:8000/dashboard/purchases/{ID}/payments
```

### **صفحة متابعة جميع الدفعات:**
```
http://localhost:8000/dashboard/simple-payment-tracking
```

---

## 📊 **المعلومات المعروضة:**

### **في صفحة دفعات الطالب:**
```
القسط رقم 1 - 3,000 دج - 2024-12-15 - نقداً - مدفوع
القسط رقم 2 - 2,000 دج - 2024-12-20 - تحويل بنكي - مدفوع
القسط رقم 3 - 5,000 دج - لم يدفع بعد - في الانتظار
```

### **تفاصيل كل قسط:**
- 🔢 **رقم القسط** - 1, 2, 3, ...
- 💰 **المبلغ** - بالدينار الجزائري
- 📅 **تاريخ الدفع** - أو "لم يدفع بعد"
- 💳 **طريقة الدفع** - أو "غير محدد"
- 🏷️ **الحالة** - مدفوع، في الانتظار، متأخر
- 📝 **الملاحظات** - مع رقم المعاملة وتاريخ الاستحقاق

---

## 🎯 **المميزات الجديدة:**

### **تتبع دقيق:**
- ✅ **كل قسط منفصل** مع تفاصيله
- ✅ **تواريخ دقيقة** للدفع والاستحقاق
- ✅ **أرقام معاملات** للمرجعية
- ✅ **صور إيصالات** كإثبات

### **حالات متقدمة:**
- 🟢 **مدفوع** - تم الدفع بنجاح
- 🟡 **في الانتظار** - لم يدفع بعد
- 🔴 **متأخر** - تجاوز تاريخ الاستحقاق

### **ربط ذكي:**
- ✅ **ربط مع purchases** عبر student_payments_tracking
- ✅ **تحديث تلقائي** لحقل paid_amount
- ✅ **تحديث حالة الطلب** عند اكتمال الدفع

---

## 🚀 **للاختبار:**

### **الخطوات:**
1. **اذهب إلى**: http://localhost:8000/dashboard/purchases
2. **اضغط على الزر الأخضر** 🟢 (إضافة دفعة)
3. **أدخل مبلغ القسط الأول** (مثال: 3000 دج)
4. **اختر طريقة الدفع** (نقداً)
5. **أضف رقم إيصال** (اختياري)
6. **اضغط "إضافة الدفعة"**

### **النتيجة المتوقعة:**
- ✅ **إنشاء سجل في student_payments_tracking**
- ✅ **إضافة القسط رقم 1 في payment_installments**
- ✅ **تحديث paid_amount في purchases**
- ✅ **رسالة نجاح مع رقم القسط**

### **عند إضافة القسط الثاني:**
- ✅ **القسط رقم 2** تلقائياً
- ✅ **تحديث إجمالي المدفوع**
- ✅ **حساب المتبقي**

### **عند إكمال الدفع:**
- 🎉 **تحديث status إلى 1** (مقبول)
- ✅ **رسالة "مدفوع بالكامل"**
- 🚫 **إخفاء زر "إضافة دفعة"**

---

## 📋 **عرض البيانات:**

### **في صفحة عرض الدفعات:**
```
معلومات الطالب:
- الاسم: أحمد محمد
- البريد: <EMAIL>
- الهاتف: 0123456789
- الدورة: دورة البرمجة

ملخص الدفعات:
- سعر الدورة: 10,000 دج
- المدفوع: 5,000 دج
- المتبقي: 5,000 دج
- التقدم: [█████░░░░░] 50%

تفاصيل الدفعات:
#1 - 3,000 دج - 2024-12-15 - نقداً - مدفوع - رقم المعاملة: REC001
#2 - 2,000 دج - 2024-12-20 - تحويل بنكي - مدفوع - رقم المعاملة: TRX002
```

---

## 🔧 **الإعدادات:**

### **حالات الأقساط:**
```php
'pending'  => 'في الانتظار'
'paid'     => 'مدفوع'
'overdue'  => 'متأخر'
```

### **طرق الدفع:**
```php
'cash'           => 'نقداً'
'bank_transfer'  => 'تحويل بنكي'
'credit_card'    => 'بطاقة ائتمان'
'mobile_payment' => 'دفع عبر الهاتف'
'check'          => 'شيك'
'other'          => 'أخرى'
```

---

## 🎨 **التصميم:**

### **الألوان:**
- 🟢 **أخضر** - مدفوع، مكتمل
- 🟡 **أصفر** - في الانتظار، جزئي
- 🔴 **أحمر** - متأخر، مرفوض

### **الأيقونات:**
- 💰 **نقود** - المبالغ والدفعات
- 📅 **تقويم** - التواريخ
- 🧾 **إيصال** - المعاملات
- 📊 **رسم بياني** - التقدم

---

## 🔍 **المراقبة والتتبع:**

### **في قاعدة البيانات:**
```sql
-- عرض جميع الأقساط لطالب معين
SELECT pi.installment_number, pi.amount, pi.paid_date, pi.status
FROM payment_installments pi
JOIN student_payments_tracking spt ON pi.student_payment_id = spt.id
WHERE spt.purchase_id = 2
ORDER BY pi.installment_number;

-- إحصائيات الدفعات
SELECT 
    COUNT(*) as total_installments,
    SUM(amount) as total_amount,
    COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count
FROM payment_installments;
```

---

**النظام الآن يعمل مع الجداول الموجودة ويحفظ تفاصيل كل قسط! 💾✨**

**المميزات:**
- ✅ **استخدام الجداول الموجودة** بدون تعديل
- ✅ **تتبع دقيق لكل قسط** مع تفاصيله
- ✅ **ربط ذكي** بين الجداول
- ✅ **تحديث تلقائي** للمبالغ والحالات
- ✅ **عرض شامل** للبيانات
- ✅ **سهولة الإدارة** والمتابعة
