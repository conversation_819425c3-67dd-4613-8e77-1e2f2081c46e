<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PaymentInstallment extends Model
{
    protected $fillable = [
        'purchase_id',
        'installment_number',
        'amount',
        'due_date',
        'paid_date',
        'status',
        'payment_method',
        'transaction_id',
        'receipt_image',
        'notes'
    ];

    protected $dates = [
        'due_date',
        'paid_date'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'due_date' => 'date',
        'paid_date' => 'date'
    ];

    // العلاقات
    public function studentPayment()
    {
        return $this->belongsTo(StudentPayment::class);
    }

    public function purchase()
    {
        return $this->belongsTo(Purchase::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', Carbon::now())
                    ->where('status', 'pending');
    }

    public function scopeDueToday($query)
    {
        return $query->whereDate('due_date', Carbon::today())
                    ->where('status', 'pending');
    }

    public function scopeDueThisWeek($query)
    {
        return $query->whereBetween('due_date', [
                        Carbon::now()->startOfWeek(),
                        Carbon::now()->endOfWeek()
                    ])
                    ->where('status', 'pending');
    }

    // Accessors
    public function getStatusArabicAttribute()
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'paid' => 'مدفوعة',
            'overdue' => 'متأخرة',
            'cancelled' => 'ملغية'
        ];

        return $statuses[$this->status] ?? 'غير محدد';
    }

    public function getIsOverdueAttribute()
    {
        return $this->due_date->isPast() && $this->status === 'pending';
    }

    public function getDaysOverdueAttribute()
    {
        if (!$this->is_overdue) return 0;
        return $this->due_date->diffInDays(Carbon::now());
    }

    public function getDaysUntilDueAttribute()
    {
        if ($this->status !== 'pending') return null;
        if ($this->due_date->isPast()) return 0;
        return Carbon::now()->diffInDays($this->due_date);
    }

    public function getReceiptImagePathAttribute()
    {
        if (!$this->receipt_image) return null;
        return asset('uploads/receipts/' . $this->receipt_image);
    }

    // Methods
    public function markAsPaid($paymentMethod = null, $transactionId = null, $receiptImage = null, $notes = null)
    {
        $this->status = 'paid';
        $this->paid_date = Carbon::now();
        $this->payment_method = $paymentMethod;
        $this->transaction_id = $transactionId;
        $this->receipt_image = $receiptImage;
        if ($notes) $this->notes = $notes;

        $this->save();

        // تحديث الدفع الرئيسي
        $this->studentPayment->addPayment($this->amount, $paymentMethod, $transactionId, $notes);

        return $this;
    }

    public function markAsOverdue()
    {
        if ($this->due_date->isPast() && $this->status === 'pending') {
            $this->status = 'overdue';
            $this->save();
        }

        return $this;
    }
}
