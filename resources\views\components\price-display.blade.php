{{-- عرض السعر بالدينار الجزائري --}}
@props(['price', 'originalPrice' => null, 'size' => 'normal', 'showCurrency' => true])

@php
    $institution = App\Models\Institution::first();
    $currencySymbol = $institution ? $institution->currency_symbol : 'د.ج';
    
    $sizeClasses = [
        'small' => 'text-sm',
        'normal' => 'text-base',
        'large' => 'text-lg',
        'xl' => 'text-xl'
    ];
    
    $sizeClass = $sizeClasses[$size] ?? $sizeClasses['normal'];
@endphp

<div class="price-display {{ $sizeClass }}" dir="ltr">
    @if($originalPrice && $originalPrice > $price)
        {{-- سعر مخفض --}}
        <span class="original-price text-muted" style="text-decoration: line-through;">
            {{ number_format($originalPrice, 2) }}
            @if($showCurrency)
                <span class="currency-symbol">{{ $currencySymbol }}</span>
            @endif
        </span>
        <span class="current-price text-success font-weight-bold mx-2">
            {{ number_format($price, 2) }}
            @if($showCurrency)
                <span class="currency-symbol">{{ $currencySymbol }}</span>
            @endif
        </span>
        <span class="discount-badge badge badge-danger">
            {{ round((($originalPrice - $price) / $originalPrice) * 100) }}% خصم
        </span>
    @else
        {{-- سعر عادي --}}
        <span class="current-price font-weight-bold">
            {{ number_format($price, 2) }}
            @if($showCurrency)
                <span class="currency-symbol">{{ $currencySymbol }}</span>
            @endif
        </span>
    @endif
</div>

<style>
.price-display {
    font-family: 'Cairo', 'Arial', sans-serif;
}

.currency-symbol {
    font-size: 0.9em;
    margin-right: 2px;
    color: #2563eb;
    font-weight: 600;
}

.original-price {
    font-size: 0.85em;
}

.discount-badge {
    font-size: 0.75em;
    padding: 2px 6px;
    border-radius: 10px;
}

.current-price {
    color: #059669;
}

.text-sm .current-price {
    font-size: 14px;
}

.text-base .current-price {
    font-size: 16px;
}

.text-lg .current-price {
    font-size: 18px;
}

.text-xl .current-price {
    font-size: 24px;
}
</style>
