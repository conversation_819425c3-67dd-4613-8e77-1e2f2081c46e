@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">

        <section class="content-header">
            <div class="row">
                <div class="col-md-8">
                    <h1 class="arabic-heading">
                        <i class="fa fa-graduation-cap text-primary"></i>
                        إدارة الدورات التدريبية
                    </h1>
                    <p class="arabic-text text-muted">
                        إدارة شاملة لجميع الدورات التدريبية في الأكاديمية
                    </p>
                </div>
                <div class="col-md-4 text-left">
                    <div class="header-stats">
                        <div class="stat-item">
                            <span class="stat-number">{{ $courses->count() }}</span>
                            <span class="stat-label arabic-text">إجمالي الدورات</span>
                        </div>
                    </div>
                </div>
            </div>

            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> لوحة التحكم</a></li>
                <li class="active">إدارة الدورات</li>
            </ol>
        </section>

        <section class="content">

            <!-- قسم الفلترة والبحث -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title arabic-heading">
                        <i class="fa fa-filter"></i>
                        البحث والفلترة
                    </h3>
                    <div class="box-tools pull-left">
                        <button type="button" class="btn btn-box-tool" data-widget="collapse">
                            <i class="fa fa-minus"></i>
                        </button>
                    </div>
                </div>

                <div class="box-body">
                    <form action="{{ route('dashboard.courses.index') }}" method="get" class="search-form">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="arabic-text">البحث في الدورات</label>
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control"
                                               placeholder="اسم الدورة، الوصف، أو المدرب..."
                                               value="{{ request()->search }}">
                                        <span class="input-group-addon">
                                            <i class="fa fa-search"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="arabic-text">التقييم</label>
                                    <select name="rating" class="form-control">
                                        <option value="">جميع التقييمات</option>
                                        <option value="5" {{ request()->rating == '5' ? 'selected' : '' }}>5 نجوم</option>
                                        <option value="4" {{ request()->rating == '4' ? 'selected' : '' }}>4 نجوم فأكثر</option>
                                        <option value="3" {{ request()->rating == '3' ? 'selected' : '' }}>3 نجوم فأكثر</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="arabic-text">المدة</label>
                                    <select name="duration" class="form-control">
                                        <option value="">جميع المدد</option>
                                        <option value="short" {{ request()->duration == 'short' ? 'selected' : '' }}>أقل من 5 ساعات</option>
                                        <option value="medium" {{ request()->duration == 'medium' ? 'selected' : '' }}>5-20 ساعة</option>
                                        <option value="long" {{ request()->duration == 'long' ? 'selected' : '' }}>أكثر من 20 ساعة</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="form-group">
                                    <label class="arabic-text">الترتيب</label>
                                    <select name="sort" class="form-control">
                                        <option value="newest" {{ request()->sort == 'newest' ? 'selected' : '' }}>الأحدث</option>
                                        <option value="oldest" {{ request()->sort == 'oldest' ? 'selected' : '' }}>الأقدم</option>
                                        <option value="rating" {{ request()->sort == 'rating' ? 'selected' : '' }}>الأعلى تقييماً</option>
                                        <option value="name" {{ request()->sort == 'name' ? 'selected' : '' }}>الاسم</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="arabic-text">&nbsp;</label>
                                    <div class="btn-group btn-group-justified">
                                        <div class="btn-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-search"></i> بحث
                                            </button>
                                        </div>
                                        <div class="btn-group">
                                            <a href="{{ route('dashboard.courses.index') }}" class="btn btn-default">
                                                <i class="fa fa-refresh"></i> إعادة تعيين
                                            </a>
                                        </div>
                                        <div class="btn-group">
                                            @if (auth()->user()->hasPermission('courses_create'))
                                                <a href="{{ route('dashboard.courses.create') }}" class="btn btn-success">
                                                    <i class="fa fa-plus"></i> إضافة دورة
                                                </a>
                                            @else
                                                <button class="btn btn-success disabled">
                                                    <i class="fa fa-plus"></i> إضافة دورة
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قسم عرض الدورات -->
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title arabic-heading">
                        <i class="fa fa-list"></i>
                        قائمة الدورات التدريبية
                        <small class="label label-primary">{{ $courses->count() }} دورة</small>
                    </h3>

                    <div class="box-tools pull-left">
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-default view-mode active" data-mode="table">
                                <i class="fa fa-table"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-default view-mode" data-mode="grid">
                                <i class="fa fa-th"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="box-body">
                    @if ($courses->count() > 0)

                        <!-- عرض الجدول -->
                        <div id="table-view" class="view-content active">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped courses-table">
                                    <thead class="bg-primary">
                                        <tr>
                                            <th class="text-center">#</th>
                                            <th class="arabic-text">معلومات الدورة</th>
                                            <th class="arabic-text text-center">التقييم</th>
                                            <th class="arabic-text text-center">المدة</th>
                                            <th class="arabic-text text-center">السعر</th>
                                            <th class="arabic-text text-center">الطلاب</th>
                                            <th class="arabic-text text-center">الحالة</th>
                                            <th class="arabic-text text-center">الإجراءات</th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                        @foreach ($courses as $index => $course)
                                            <tr class="course-row" data-course-id="{{ $course->id }}">
                                                <td class="text-center">
                                                    <span class="badge badge-primary">{{ $index + 1 }}</span>
                                                </td>

                                                <td>
                                                    <div class="course-info">
                                                        <div class="course-image">
                                                            <img src="{{ $course->image_path }}" alt="{{ $course->name }}"
                                                                 class="img-thumbnail" style="width: 60px; height: 40px; object-fit: cover;">
                                                        </div>
                                                        <div class="course-details">
                                                            <h5 class="course-title arabic-text">{{ $course->name }}</h5>
                                                            <p class="course-description arabic-text text-muted">
                                                                {{ Str::limit($course->Short_description, 50) }}
                                                            </p>
                                                            @if($course->instructors && $course->instructors->count() > 0)
                                                                <small class="text-success">
                                                                    <i class="fa fa-user"></i>
                                                                    {{ $course->instructors->pluck('name')->implode('، ') }}
                                                                </small>
                                                                <br>
                                                            @endif
                                                            @if($course->demo_video)
                                                                <small class="text-info">
                                                                    <i class="fa fa-video-camera"></i> يحتوي على فيديو تعريفي
                                                                </small>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>

                                                <td class="text-center">
                                                    <div class="rating-display">
                                                        @for ($i = 1; $i <= 5; $i++)
                                                            @if ($i <= $course->rating)
                                                                <i class="fa fa-star text-warning"></i>
                                                            @else
                                                                <i class="fa fa-star-o text-muted"></i>
                                                            @endif
                                                        @endfor
                                                        <br>
                                                        <small class="text-muted">{{ $course->rating }}/5</small>
                                                    </div>
                                                </td>

                                                <td class="text-center">
                                                    <span class="badge badge-info">{{ $course->time }} ساعة</span>
                                                </td>

                                                <td class="text-center">
                                                    <span class="price-display">
                                                        @if($course->price > 0)
                                                            <strong class="text-success">{{ $course->price }} دينار جزائري</strong>
                                                        @else
                                                            <strong class="text-primary">مجاني</strong>
                                                        @endif
                                                    </span>
                                                </td>

                                                <td class="text-center">
                                                    <span class="badge badge-secondary">
                                                        {{ $course->studant_count ?? 0 }} طالب
                                                    </span>
                                                </td>

                                                <td class="text-center">
                                                    @if($course->is_active ?? true)
                                                        <span class="label label-success">نشط</span>
                                                    @else
                                                        <span class="label label-warning">غير نشط</span>
                                                    @endif
                                                </td>

                                                <td class="text-center">
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-sm btn-info dropdown-toggle"
                                                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                            <i class="fa fa-cogs"></i> إجراءات
                                                            <span class="caret"></span>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-left">
                                                            <li>
                                                                <a href="{{ route('dashboard.courses.show', $course->id) }}">
                                                                    <i class="fa fa-eye"></i> عرض التفاصيل
                                                                </a>
                                                            </li>
                                                            @if (auth()->user()->hasPermission('courses_update'))
                                                                <li>
                                                                    <a href="{{ route('dashboard.courses.edit', $course->id) }}">
                                                                        <i class="fa fa-edit"></i> تعديل
                                                                    </a>
                                                                </li>
                                                            @endif
                                                            <li>
                                                                <a href="#" class="duplicate-course" data-course-id="{{ $course->id }}">
                                                                    <i class="fa fa-copy"></i> نسخ الدورة
                                                                </a>
                                                            </li>
                                                            <li class="divider"></li>
                                                            @if (auth()->user()->hasPermission('courses_delete'))
                                                                <li>
                                                                    <a href="#" class="delete-course text-danger" data-course-id="{{ $course->id }}">
                                                                        <i class="fa fa-trash"></i> حذف
                                                                    </a>
                                                                </li>
                                                            @endif
                                                        </ul>
                                                    </div>

                                                    <!-- نماذج الحذف المخفية -->
                                                    @if (auth()->user()->hasPermission('courses_delete'))
                                                        <form id="delete-form-{{ $course->id }}"
                                                              action="{{ route('dashboard.courses.destroy', $course->id) }}"
                                                              method="post" style="display: none;">
                                                            {{ csrf_field() }}
                                                            {{ method_field('delete') }}
                                                        </form>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <!-- التصفح -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="dataTables_info arabic-text">
                                        عرض {{ $courses->firstItem() ?? 0 }} إلى {{ $courses->lastItem() ?? 0 }}
                                        من أصل {{ $courses->total() ?? $courses->count() }} دورة
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="dataTables_paginate">
                                        {{ $courses->appends(request()->query())->links() }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- عرض الشبكة -->
                        <div id="grid-view" class="view-content">
                            <div class="row">
                                @foreach ($courses as $course)
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="course-card">
                                            <div class="course-card-image">
                                                <img src="{{ $course->image_path }}" alt="{{ $course->name }}" class="img-responsive">
                                                <div class="course-card-overlay">
                                                    <div class="course-card-actions">
                                                        @if (auth()->user()->hasPermission('courses_update'))
                                                            <a href="{{ route('dashboard.courses.edit', $course->id) }}"
                                                               class="btn btn-sm btn-primary">
                                                                <i class="fa fa-edit"></i>
                                                            </a>
                                                        @endif
                                                        @if (auth()->user()->hasPermission('courses_delete'))
                                                            <button class="btn btn-sm btn-danger delete-course"
                                                                    data-course-id="{{ $course->id }}">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="course-card-content">
                                                <h4 class="course-card-title arabic-text">{{ $course->name }}</h4>
                                                <p class="course-card-description arabic-text">
                                                    {{ Str::limit($course->Short_description, 80) }}
                                                </p>
                                                <div class="course-card-meta">
                                                    <div class="rating">
                                                        @for ($i = 1; $i <= 5; $i++)
                                                            @if ($i <= $course->rating)
                                                                <i class="fa fa-star text-warning"></i>
                                                            @else
                                                                <i class="fa fa-star-o text-muted"></i>
                                                            @endif
                                                        @endfor
                                                    </div>
                                                    <div class="duration">
                                                        <i class="fa fa-clock-o"></i> {{ $course->time }} ساعة
                                                    </div>
                                                    <div class="students">
                                                        <i class="fa fa-users"></i> {{ $course->studant_count ?? 0 }} طالب
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                    @else
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fa fa-graduation-cap fa-5x text-muted"></i>
                            </div>
                            <h3 class="empty-state-title arabic-text">لا توجد دورات</h3>
                            <p class="empty-state-description arabic-text text-muted">
                                لم يتم العثور على أي دورات تدريبية. يمكنك إضافة دورة جديدة للبدء.
                            </p>
                            @if (auth()->user()->hasPermission('courses_create'))
                                <a href="{{ route('dashboard.courses.create') }}" class="btn btn-primary btn-lg">
                                    <i class="fa fa-plus"></i> إضافة أول دورة
                                </a>
                            @endif
                        </div>
                    @endif
                </div><!-- end of box body -->


            </div><!-- end of box -->

        </section><!-- end of content -->

    </div><!-- end of content wrapper -->

@endsection

@push('scripts')
<script>
$(document).ready(function() {

    // تبديل طرق العرض
    $('.view-mode').on('click', function() {
        const mode = $(this).data('mode');

        $('.view-mode').removeClass('active');
        $(this).addClass('active');

        $('.view-content').removeClass('active');
        $('#' + mode + '-view').addClass('active');

        // حفظ تفضيل المستخدم
        localStorage.setItem('courses_view_mode', mode);
    });

    // استرجاع تفضيل المستخدم
    const savedMode = localStorage.getItem('courses_view_mode');
    if (savedMode) {
        $('.view-mode[data-mode="' + savedMode + '"]').click();
    }

    // تأكيد الحذف
    $('.delete-course').on('click', function(e) {
        e.preventDefault();
        const courseId = $(this).data('course-id');

        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'سيتم حذف الدورة نهائياً ولا يمكن التراجع عن هذا الإجراء',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف الدورة',
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $('#delete-form-' + courseId).submit();
            }
        });
    });



    // نسخ الدورة
    $('.duplicate-course').on('click', function(e) {
        e.preventDefault();
        const courseId = $(this).data('course-id');

        Swal.fire({
            title: 'نسخ الدورة',
            text: 'هل تريد إنشاء نسخة من هذه الدورة؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، انسخ الدورة',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                // يمكن إضافة وظيفة النسخ هنا
                Swal.fire('تم!', 'سيتم إضافة وظيفة النسخ قريباً', 'success');
            }
        });
    });

    // تحسين البحث المباشر
    let searchTimeout;
    $('input[name="search"]').on('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = $(this).val();

        searchTimeout = setTimeout(() => {
            if (searchTerm.length > 2 || searchTerm.length === 0) {
                // يمكن إضافة بحث AJAX هنا
                console.log('البحث عن:', searchTerm);
            }
        }, 500);
    });

    // تأثيرات hover للبطاقات
    $('.course-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );

    // تحسين الجدول
    $('.courses-table tbody tr').hover(
        function() {
            $(this).addClass('table-row-hover');
        },
        function() {
            $(this).removeClass('table-row-hover');
        }
    );



    console.log('✅ تم تحميل صفحة إدارة الدورات المحسنة');
});
</script>

<style>
/* أنماط مخصصة لصفحة الدورات */
.header-stats {
    text-align: center;
    padding: 15px;
    background: rgba(38, 199, 249, 0.1);
    border-radius: 8px;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #26c7f9;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

.view-content {
    display: none;
}

.view-content.active {
    display: block;
}

.view-mode.active {
    background-color: #26c7f9;
    color: white;
}

.course-info {
    display: flex;
    align-items: center;
}

.course-image {
    margin-left: 15px;
    flex-shrink: 0;
}

.course-details {
    flex: 1;
}

.course-title {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.course-description {
    margin: 0 0 5px 0;
    font-size: 12px;
    line-height: 1.4;
}

.rating-display {
    line-height: 1.2;
}

.price-display {
    font-size: 14px;
}

.table-row-hover {
    background-color: rgba(38, 199, 249, 0.05) !important;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

.course-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.course-card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-card:hover .course-card-image img {
    transform: scale(1.1);
}

.course-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(38, 199, 249, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.course-card:hover .course-card-overlay {
    opacity: 1;
}

.course-card-actions .btn {
    margin: 0 5px;
}

.course-card-content {
    padding: 20px;
}

.course-card-title {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.course-card-description {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.course-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #888;
}

.course-card-meta > div {
    display: flex;
    align-items: center;
}

.course-card-meta i {
    margin-left: 5px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-state-icon {
    margin-bottom: 20px;
}

.empty-state-title {
    margin-bottom: 10px;
    color: #666;
}

.empty-state-description {
    margin-bottom: 30px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.search-form .form-group {
    margin-bottom: 15px;
}

.search-form label {
    font-weight: 500;
    margin-bottom: 5px;
}



/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .course-info {
        flex-direction: column;
        text-align: center;
    }

    .course-image {
        margin-left: 0;
        margin-bottom: 10px;
    }

    .btn-group-justified .btn-group {
        width: 100%;
        margin-bottom: 5px;
    }

    .course-card-meta {
        flex-direction: column;
        align-items: flex-start;
    }

    .course-card-meta > div {
        margin-bottom: 5px;
    }

    .course-details-popup {
        width: 95% !important;
    }

    .course-actions .btn {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
}
</style>
@endpush
