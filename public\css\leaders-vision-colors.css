/* ===================================
   ألوان أكاديمية Leaders Vision
   Leaders Vision Academy Colors
   =================================== */

/* تحديث الألوان الأساسية - الأخضر والرمادي */
:root {
    /* ألوان Leaders Vision الجديدة - الأخضر والرمادي */
    --lv-green: #4ade80;          /* الأخضر الأساسي */
    --lv-green-dark: #22c55e;     /* الأخضر الداكن */
    --lv-green-light: #86efac;    /* الأخضر الفاتح */

    --lv-gray: #475569;           /* الرمادي الأساسي */
    --lv-gray-dark: #334155;      /* الرمادي الداكن */
    --lv-gray-light: #64748b;     /* الرمادي الفاتح */

    --lv-teal: #14b8a6;           /* التركوازي */
    --lv-emerald: #10b981;        /* الزمردي */
    --lv-dark: #1e293b;           /* الخلفية الداكنة */
    --lv-light: #f1f5f9;          /* الأبيض المزرق */

    /* تدرجات الألوان الجديدة */
    --lv-green-gradient: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    --lv-gray-gradient: linear-gradient(135deg, #475569 0%, #334155 100%);
    --lv-hero-gradient: linear-gradient(135deg, #1e293b 0%, #475569 30%, #4ade80 70%, #14b8a6 100%);
    --lv-mixed-gradient: linear-gradient(135deg, #4ade80 0%, #475569 50%, #1e293b 100%);

    /* ظلال الألوان الجديدة */
    --lv-green-shadow: rgba(74, 222, 128, 0.3);
    --lv-gray-shadow: rgba(71, 85, 105, 0.3);
    --lv-teal-shadow: rgba(20, 184, 166, 0.3);
    --lv-emerald-shadow: rgba(16, 185, 129, 0.3);
}

/* تحديث الألوان في العناصر الموجودة */

/* النصوص الأساسية */
.text-primary-custom {
    color: var(--lv-blue) !important;
}

.text-secondary-custom {
    color: var(--lv-red) !important;
}

.text-accent-custom {
    color: var(--lv-green) !important;
}

.text-gold-custom {
    color: var(--lv-gold) !important;
}

/* الخلفيات */
.bg-primary-custom {
    background: var(--lv-blue-gradient) !important;
}

.bg-secondary-custom {
    background: var(--lv-red-gradient) !important;
}

.bg-accent-custom {
    background: var(--lv-green-gradient) !important;
}

.bg-gold-custom {
    background: var(--lv-gold-gradient) !important;
}

/* الحدود */
.border-primary-custom {
    border-color: var(--lv-blue) !important;
}

.border-secondary-custom {
    border-color: var(--lv-red) !important;
}

.border-accent-custom {
    border-color: var(--lv-green) !important;
}

.border-gold-custom {
    border-color: var(--lv-gold) !important;
}

/* الأزرار المخصصة */
.btn-lv-primary {
    background: var(--lv-blue-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-lv-primary:hover {
    background: var(--lv-blue-gradient);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--lv-blue-shadow);
    color: white;
}

.btn-lv-secondary {
    background: var(--lv-red-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-lv-secondary:hover {
    background: var(--lv-red-gradient);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--lv-red-shadow);
    color: white;
}

.btn-lv-accent {
    background: var(--lv-green-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-lv-accent:hover {
    background: var(--lv-green-gradient);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--lv-green-shadow);
    color: white;
}

.btn-lv-gold {
    background: var(--lv-gold-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-lv-gold:hover {
    background: var(--lv-gold-gradient);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--lv-gold-shadow);
    color: white;
}

/* البطاقات المخصصة */
.card-lv-primary {
    border: 2px solid var(--lv-blue);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.card-lv-primary:hover {
    box-shadow: 0 10px 30px var(--lv-blue-shadow);
    transform: translateY(-5px);
}

.card-lv-secondary {
    border: 2px solid var(--lv-red);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.card-lv-secondary:hover {
    box-shadow: 0 10px 30px var(--lv-red-shadow);
    transform: translateY(-5px);
}

/* الأيقونات الملونة */
.icon-lv-blue {
    color: var(--lv-blue);
}

.icon-lv-red {
    color: var(--lv-red);
}

.icon-lv-green {
    color: var(--lv-green);
}

.icon-lv-gold {
    color: var(--lv-gold);
}

/* الشارات والتسميات */
.badge-lv-primary {
    background: var(--lv-blue);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.badge-lv-secondary {
    background: var(--lv-red);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.badge-lv-accent {
    background: var(--lv-green);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.badge-lv-gold {
    background: var(--lv-gold);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

/* تأثيرات الإضاءة */
.glow-lv-blue {
    box-shadow: 0 0 20px var(--lv-blue-shadow);
}

.glow-lv-red {
    box-shadow: 0 0 20px var(--lv-red-shadow);
}

.glow-lv-green {
    box-shadow: 0 0 20px var(--lv-green-shadow);
}

.glow-lv-gold {
    box-shadow: 0 0 20px var(--lv-gold-shadow);
}

/* خلفيات شفافة */
.bg-lv-blue-transparent {
    background: rgba(37, 99, 235, 0.1);
    backdrop-filter: blur(10px);
}

.bg-lv-red-transparent {
    background: rgba(220, 38, 38, 0.1);
    backdrop-filter: blur(10px);
}

.bg-lv-green-transparent {
    background: rgba(5, 150, 105, 0.1);
    backdrop-filter: blur(10px);
}

.bg-lv-gold-transparent {
    background: rgba(245, 158, 11, 0.1);
    backdrop-filter: blur(10px);
}

/* تحديث الخلفية الرئيسية */
.bg-hidden-bits {
    background: var(--lv-blue-gradient) !important;
}

/* تحديث ألوان النصوص في الأقسام المختلفة */
.sub-title {
    color: var(--lv-gold) !important;
}

/* تحديث ألوان الروابط */
a.text-primary-custom:hover {
    color: var(--lv-blue) !important;
    text-decoration: none;
}

a.text-secondary-custom:hover {
    color: var(--lv-red) !important;
    text-decoration: none;
}

/* تحديث ألوان الفورم */
.form-control:focus {
    border-color: var(--lv-blue);
    box-shadow: 0 0 0 0.2rem var(--lv-blue-shadow);
}

/* تحديث ألوان التنقل */
.navbar-brand {
    color: var(--lv-blue) !important;
}

.nav-link:hover {
    color: var(--lv-red) !important;
}

/* تحديث ألوان الفوتر */
.footer-link:hover {
    color: var(--lv-gold) !important;
}

/* تأثيرات خاصة للعناوين */
.heading-lv-gradient {
    background: var(--lv-blue-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .btn-lv-primary,
    .btn-lv-secondary,
    .btn-lv-accent,
    .btn-lv-gold {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .badge-lv-primary,
    .badge-lv-secondary,
    .badge-lv-accent,
    .badge-lv-gold {
        padding: 4px 10px;
        font-size: 0.8rem;
    }
}
