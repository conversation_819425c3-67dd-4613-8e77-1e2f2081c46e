# دليل التشغيل والتحسينات - أكاديمية Leaders Vision
## Installation and Enhancement Guide - Leaders Vision Academy

---

## 🚀 متطلبات النظام

### الحد الأدنى:
- **PHP**: 7.2.5 أو أحدث
- **Laravel**: 7.29
- **MySQL**: 5.7 أو أحدث
- **Composer**: 2.0 أو أحدث
- **Node.js**: 14.0 أو أحدث (للتطوير)

### المستحسن:
- **PHP**: 8.1 أو أحدث
- **Laravel**: 10.x (للترقية المستقبلية)
- **MySQL**: 8.0 أو أحدث
- **Redis**: للتخزين المؤقت
- **SSL Certificate**: للأمان

---

## 📦 خطوات التشغيل

### 1. تحضير البيئة

```bash
# استنساخ المشروع
git clone [repository-url] academy
cd academy

# تثبيت التبعيات
composer install

# نسخ ملف البيئة
cp .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate
```

### 2. إعداد قاعدة البيانات

```bash
# إنشاء قاعدة البيانات
mysql -u root -p
CREATE DATABASE academy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;

# تحديث ملف .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=academy
DB_USERNAME=root
DB_PASSWORD=your_password

# تشغيل الهجرات
php artisan migrate

# تشغيل البذور (البيانات الأولية)
php artisan db:seed
```

### 3. إعداد الملفات والصلاحيات

```bash
# إنشاء رابط التخزين
php artisan storage:link

# تعيين الصلاحيات (Linux/Mac)
chmod -R 755 storage
chmod -R 755 bootstrap/cache

# تعيين الصلاحيات (Windows)
# تأكد من أن مجلدات storage و bootstrap/cache قابلة للكتابة
```

### 4. تشغيل الخادم

```bash
# تشغيل خادم التطوير
php artisan serve

# أو استخدام XAMPP/WAMP/MAMP
# ضع المشروع في مجلد htdocs
```

---

## 🎨 تفعيل التحسينات

### التحسينات مفعلة تلقائياً:

✅ **الخطوط العربية الجديدة**
- خط Cairo للعناوين
- خط Tajawal للنصوص
- تحسين عرض RTL

✅ **تحسينات CSS**
- ألوان محسنة
- تأثيرات بصرية
- استجابة أفضل للشاشات

✅ **تحسينات JavaScript**
- تأثيرات تفاعلية
- تحميل تدريجي للصور
- تحسين النماذج

✅ **تحسينات لوحة التحكم**
- واجهة محسنة
- جداول تفاعلية
- إحصائيات متحركة

---

## ⚙️ إعدادات إضافية

### 1. تحسين الأداء (للإنتاج)

```bash
# تحسين التطبيق
php artisan config:cache
php artisan route:cache
php artisan view:cache

# تحسين Composer
composer install --optimize-autoloader --no-dev

# ضغط الملفات (إذا كان متاحاً)
php artisan optimize
```

### 2. إعداد Redis (اختياري)

```bash
# تثبيت Redis
# Ubuntu/Debian:
sudo apt install redis-server

# تحديث .env
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
```

### 3. إعداد البريد الإلكتروني

```env
# تحديث .env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="أكاديمية Leaders vision academy"
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ في الخطوط
```bash
# التأكد من تحميل الخطوط
# افتح Developer Tools في المتصفح
# تحقق من تحميل ملفات CSS
```

#### 2. مشاكل في قاعدة البيانات
```bash
# إعادة تشغيل الهجرات
php artisan migrate:fresh --seed

# التحقق من الاتصال
php artisan tinker
DB::connection()->getPdo();
```

#### 3. مشاكل في الصلاحيات
```bash
# Linux/Mac
sudo chown -R www-data:www-data storage
sudo chown -R www-data:www-data bootstrap/cache

# أو
sudo chmod -R 777 storage
sudo chmod -R 777 bootstrap/cache
```

#### 4. مشاكل في JavaScript
```javascript
// افتح Developer Console
// تحقق من الأخطاء
console.log('تحقق من تحميل الملفات');
```

---

## 📊 مراقبة الأداء

### أدوات المراقبة المدمجة:

1. **ضمان الجودة** (في بيئة التطوير فقط)
   - مراقبة تحميل الخطوط
   - فحص الصور
   - مراقبة الأخطاء

2. **تحليل الأداء**
   - وقت تحميل الصفحة
   - استخدام الذاكرة
   - أخطاء JavaScript

### عرض تقارير الأداء:
```javascript
// في Developer Console
console.log('تقرير الأداء متاح في Console');
```

---

## 🔄 التحديثات المستقبلية

### خطة الترقية:

#### المرحلة 1 (قريباً):
- ترقية Laravel إلى 10.x
- ترقية PHP إلى 8.1+
- تحسين الأمان

#### المرحلة 2:
- إضافة PWA features
- تطوير تطبيق موبايل
- نظام دفع إلكتروني

#### المرحلة 3:
- ذكاء اصطناعي للتوصيات
- تحليلات متقدمة
- نظام إشعارات متطور

---

## 📞 الدعم الفني

### في حالة وجود مشاكل:

1. **تحقق من ملف اللوج**
   ```bash
   tail -f storage/logs/laravel.log
   ```

2. **تفعيل وضع التطوير**
   ```env
   APP_DEBUG=true
   ```

3. **مسح التخزين المؤقت**
   ```bash
   php artisan cache:clear
   php artisan config:clear
   php artisan view:clear
   ```

4. **إعادة تشغيل الخادم**
   ```bash
   php artisan serve
   ```

---

## ✅ قائمة التحقق النهائية

- [ ] تم تثبيت جميع التبعيات
- [ ] قاعدة البيانات تعمل بشكل صحيح
- [ ] الخطوط تظهر بشكل صحيح
- [ ] التحسينات مفعلة
- [ ] لوحة التحكم تعمل
- [ ] النماذج تعمل بشكل صحيح
- [ ] الصور تحمل بشكل صحيح
- [ ] التنقل سلس
- [ ] الموقع متجاوب

---

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطية**: تأكد من عمل نسخ احتياطية منتظمة
2. **الأمان**: استخدم HTTPS في الإنتاج
3. **الأداء**: راقب الأداء باستمرار
4. **التحديثات**: حافظ على تحديث التبعيات

---

**تاريخ الإنشاء**: ديسمبر 2024
**الإصدار**: 2.0.0
**المطور**: Augment Agent

للمزيد من المساعدة، راجع ملف `IMPROVEMENTS.md` للتفاصيل الكاملة حول التحسينات.
