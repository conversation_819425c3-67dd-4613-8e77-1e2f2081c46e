<?php $__env->startSection('content'); ?>

    <div class="content-wrapper">

        <section class="content-header">
            <div class="header-content">
                <h1 style="color: #3c8dbc; font-weight: 600;">
                    <i class="fa fa-graduation-cap"></i> إضافة دورة تدريبية جديدة
                </h1>
                <p style="color: #666; margin-top: 5px;">قم بإنشاء دورة تدريبية احترافية مع جميع التفاصيل المطلوبة</p>
            </div>

            <ol class="breadcrumb">
                <li><a href="<?php echo e(route('dashboard.welcome')); ?>"><i class="fa fa-dashboard"></i> لوحة التحكم</a></li>
                <li><a href="<?php echo e(route('dashboard.courses.index')); ?>"><i class="fa fa-graduation-cap"></i> الدورات</a></li>
                <li class="active"><i class="fa fa-plus"></i> إضافة دورة</li>
            </ol>
        </section>

        <section class="content">

            <?php echo $__env->make('partials._errors', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <form action="<?php echo e(route('dashboard.courses.store')); ?>" method="post" enctype="multipart/form-data" id="courseForm">
                <?php echo e(csrf_field()); ?>

                <?php echo e(method_field('post')); ?>


                <div class="row">
                    <!-- القسم الأول: المعلومات الأساسية -->
                    <div class="col-md-8">
                        <div class="box box-primary">
                            <div class="box-header with-border">
                                <h3 class="box-title">
                                    <i class="fa fa-info-circle"></i> المعلومات الأساسية للدورة
                                </h3>
                            </div>
                            <div class="box-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="name">
                                                <i class="fa fa-graduation-cap text-primary"></i> اسم الدورة <span class="text-danger">*</span>
                                            </label>
                                            <input type="text" name="name" id="name" class="form-control"
                                                   value="<?php echo e(old('name')); ?>" placeholder="أدخل اسم الدورة التدريبية" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="url">
                                                <i class="fa fa-link text-info"></i> رابط الدورة
                                            </label>
                                            <input type="url" name="url" id="url" class="form-control"
                                                   value="<?php echo e(old('url')); ?>" placeholder="https://example.com/course">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="categories_id">
                                                <i class="fa fa-folder text-warning"></i> القسم <span class="text-danger">*</span>
                                            </label>
                                            <select name="categories_id" id="categories_id" class="form-control select2" required>
                                                <option value="">اختر القسم</option>
                                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categorie): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($categorie->id); ?>" <?php echo e(old('categories_id') == $categorie->id ? 'selected' : ''); ?>>
                                                        <?php echo e($categorie->name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="Short_description">
                                        <i class="fa fa-file-text-o text-success"></i> الوصف المختصر <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <textarea name="Short_description" id="Short_description" class="form-control" rows="2"
                                                  placeholder="وصف مختصر وجذاب للدورة (سيظهر في قائمة الدورات)" required><?php echo e(old('Short_description')); ?></textarea>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-info ai-generate-btn"
                                                    data-target="Short_description"
                                                    data-type="short-description"
                                                    title="توليد بالذكاء الاصطناعي">
                                                <i class="fa fa-magic"></i> AI
                                            </button>
                                        </span>
                                    </div>
                                    <small class="text-muted">يُنصح بألا يتجاوز 150 حرف</small>
                                </div>

                                <div class="form-group">
                                    <label for="description">
                                        <i class="fa fa-align-left text-primary"></i> الوصف التفصيلي <span class="text-danger">*</span>
                                    </label>
                                    <textarea name="description" id="description" class="form-control" rows="5"
                                              placeholder="وصف تفصيلي شامل للدورة ومحتواها" required><?php echo e(old('description')); ?></textarea>
                                </div>
                            </div>
                        </div>
                        <!-- القسم الثاني: الأهداف والفئة المستهدفة -->
                        <div class="box box-success">
                            <div class="box-header with-border">
                                <h3 class="box-title">
                                    <i class="fa fa-bullseye"></i> أهداف الدورة والفئة المستهدفة
                                </h3>
                                <div class="box-tools pull-right">
                                    <button type="button" class="btn btn-primary btn-sm ai-generate-all-btn"
                                            title="توليد جميع المحتويات بالذكاء الاصطناعي">
                                        <i class="fa fa-magic"></i> توليد الكل بـ AI
                                    </button>
                                </div>
                            </div>
                            <div class="box-body">
                                <div class="form-group">
                                    <label for="course_objectives">
                                        <i class="fa fa-check-circle text-success"></i> أهداف الدورة
                                    </label>
                                    <div class="input-group">
                                        <textarea name="course_objectives" id="course_objectives" class="form-control" rows="4"
                                                  placeholder="أدخل أهداف الدورة (مثال: ✅ إكساب المشاركين مهارات...)"><?php echo e(old('course_objectives')); ?></textarea>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-success ai-generate-btn"
                                                    data-target="course_objectives"
                                                    data-type="objectives"
                                                    title="توليد أهداف بالذكاء الاصطناعي">
                                                <i class="fa fa-magic"></i> AI
                                            </button>
                                        </span>
                                    </div>
                                    <small class="text-muted">استخدم رموز ✅ أو • لتنسيق الأهداف</small>
                                </div>

                                <div class="form-group">
                                    <label for="target_audience">
                                        <i class="fa fa-users text-info"></i> الفئة المستهدفة
                                    </label>
                                    <div class="input-group">
                                        <textarea name="target_audience" id="target_audience" class="form-control" rows="4"
                                                  placeholder="أدخل الفئة المستهدفة (مثال: ✔️ مدراء المشتريات...)"><?php echo e(old('target_audience')); ?></textarea>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-warning ai-generate-btn"
                                                    data-target="target_audience"
                                                    data-type="target-audience"
                                                    title="توليد الفئة المستهدفة بالذكاء الاصطناعي">
                                                <i class="fa fa-magic"></i> AI
                                            </button>
                                        </span>
                                    </div>
                                    <small class="text-muted">استخدم رموز ✔️ أو • لتنسيق الفئات</small>
                                </div>
                            </div>
                        </div>

                        <!-- القسم الثالث: المدربين -->
                        <div class="box box-warning">
                            <div class="box-header with-border">
                                <h3 class="box-title">
                                    <i class="fa fa-users"></i> مدربي الدورة
                                </h3>
                            </div>
                            <div class="box-body">
                                <div class="form-group">
                                    <label for="instructors">
                                        <i class="fa fa-user text-primary"></i> اختر المدربين
                                    </label>
                                    <select name="instructors[]" id="instructors" class="form-control select2" multiple>
                                        <?php $__currentLoopData = $instructors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $instructor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($instructor->id); ?>"
                                                <?php echo e(in_array($instructor->id, old('instructors', [])) ? 'selected' : ''); ?>>
                                                <?php echo e($instructor->name); ?>

                                                <?php if($instructor->rating): ?>
                                                    (⭐ <?php echo e($instructor->rating); ?>/5)
                                                <?php endif; ?>
                                                - <?php echo e($instructor->email); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <small class="text-muted">يمكنك اختيار مدرب واحد أو أكثر للدورة</small>
                                </div>

                                <?php if($instructors->count() == 0): ?>
                                    <div class="alert alert-warning">
                                        <i class="fa fa-exclamation-triangle"></i>
                                        لا يوجد مدربين في النظام.
                                        <a href="<?php echo e(route('dashboard.users.create')); ?>" target="_blank">إضافة مدرب جديد</a>
                                    </div>
                                <?php endif; ?>

                                <!-- معاينة المدربين المختارين -->
                                <div id="selected-instructors-preview" class="selected-instructors-preview" style="display: none;">
                                    <h5><i class="fa fa-eye"></i> المدربين المختارين:</h5>
                                    <div id="instructors-list" class="instructors-list"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الشريط الجانبي -->
                    <div class="col-md-4">
                        <!-- معلومات التسعير والتقييم -->
                        <div class="box box-warning">
                            <div class="box-header with-border">
                                <h3 class="box-title">
                                    <i class="fa fa-money"></i> التسعير والتقييم
                                </h3>
                            </div>
                            <div class="box-body">
                                <div class="form-group">
                                    <label for="price">
                                        <i class="fa fa-tag text-success"></i> السعر (دينار جزائري) <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" name="price" id="price" class="form-control"
                                               value="<?php echo e(old('price')); ?>" placeholder="0" min="0" step="0.01" required>
                                        <span class="input-group-addon">دج</span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="show_price">
                                        <i class="fa fa-eye text-info"></i> إظهار السعر
                                    </label>
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="show_price" id="show_price" value="1"
                                                   <?php echo e(old('show_price', true) ? 'checked' : ''); ?>>
                                            <span class="text-primary">إظهار السعر في الواجهة الأمامية</span>
                                        </label>
                                    </div>
                                    <small class="text-muted">إذا تم إلغاء التحديد، سيتم إخفاء السعر وإظهار "اتصل بنا" بدلاً منه</small>
                                </div>

                                <div class="form-group">
                                    <label for="time">
                                        <i class="fa fa-clock-o text-primary"></i> عدد الساعات <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" name="time" id="time" class="form-control"
                                               value="<?php echo e(old('time')); ?>" placeholder="0" min="1" required>
                                        <span class="input-group-addon">ساعة</span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="rating">
                                        <i class="fa fa-star text-warning"></i> التقييم الأولي
                                    </label>
                                    <select name="rating" id="rating" class="form-control">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <option value="<?php echo e($i); ?>" <?php echo e(old('rating') == $i ? 'selected' : ''); ?>>
                                                <?php echo e($i); ?> <?php echo e($i == 1 ? 'نجمة' : 'نجوم'); ?>

                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- الملفات والوسائط -->
                        <div class="box box-info">
                            <div class="box-header with-border">
                                <h3 class="box-title">
                                    <i class="fa fa-file-o"></i> الملفات والوسائط
                                </h3>
                            </div>
                            <div class="box-body">
                                <div class="form-group">
                                    <label for="image">
                                        <i class="fa fa-image text-success"></i> صورة الدورة <span class="text-danger">*</span>
                                    </label>
                                    <input type="file" name="image" id="image" class="form-control"
                                           accept="image/*" required>
                                    <small class="text-muted">الصيغ المدعومة: JPG, PNG, JPEG</small>
                                </div>

                                <div class="form-group">
                                    <label for="demo_video">
                                        <i class="fa fa-video-camera text-danger"></i> الفيديو التعريفي
                                    </label>
                                    <input type="file" name="demo_video" id="demo_video" class="form-control"
                                           accept="video/*">
                                    <small class="text-muted">الصيغ المدعومة: MP4, AVI, MOV</small>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="box box-solid">
                            <div class="box-body">
                                <button type="submit" class="btn btn-primary btn-lg btn-block">
                                    <i class="fa fa-save"></i> حفظ الدورة
                                </button>
                                <a href="<?php echo e(route('dashboard.courses.index')); ?>" class="btn btn-default btn-lg btn-block">
                                    <i class="fa fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

        </section><!-- end of content -->

    </div><!-- end of content wrapper -->

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.box-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
}

.box-title {
    font-weight: 600;
    font-size: 16px;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 8px;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #3c8dbc;
    box-shadow: 0 0 0 0.2rem rgba(60, 141, 188, 0.25);
}

.btn {
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.text-danger {
    color: #dc3545 !important;
}

.input-group-addon {
    background: #f8f9fa;
    border-color: #ddd;
    font-weight: 600;
}

.header-content h1 {
    margin-bottom: 5px;
}

.breadcrumb {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.box {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.select2-container .select2-selection--single {
    height: 34px;
    border-radius: 6px;
}

/* تحسين Select2 للمدربين */
.select2-container--default .select2-selection--multiple {
    border-radius: 6px;
    border: 1px solid #ddd;
    min-height: 34px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #3c8dbc;
    border: 1px solid #3c8dbc;
    color: white;
    border-radius: 4px;
    padding: 2px 8px;
    margin: 2px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-left: 5px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #ffcccc;
}

/* معاينة المدربين */
.selected-instructors-preview {
    margin-top: 15px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.instructor-item {
    transition: all 0.3s ease;
}

.instructor-item:hover {
    background: #e8f4fd !important;
    transform: translateX(5px);
}

/* تحسين box المدربين */
.box-warning .box-header {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.box-warning .box-title {
    color: white !important;
}

/* ===== تنسيق أزرار الذكاء الاصطناعي ===== */
.ai-generate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ai-generate-btn:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: white;
}

.ai-generate-btn:disabled {
    background: #6c757d;
    transform: none;
    box-shadow: none;
}

.ai-generate-all-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ai-generate-all-btn:hover {
    background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: white;
}

.ai-generate-all-btn:disabled {
    background: #6c757d;
    transform: none;
    box-shadow: none;
}

/* تأثير التمييز للحقول المولدة */
.highlight-field {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #f39c12;
    animation: highlightPulse 2s ease-in-out;
}

@keyframes  highlightPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(243, 156, 18, 0.7);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(243, 156, 18, 0.3);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(243, 156, 18, 0);
    }
}

/* تحسين input-group للـ AI */
.input-group .ai-generate-btn {
    border-radius: 0 4px 4px 0;
    min-width: 60px;
}

/* أيقونة السحر */
.fa-magic {
    animation: magicSparkle 2s infinite;
}

@keyframes  magicSparkle {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(-5deg) scale(1.1); }
    75% { transform: rotate(5deg) scale(1.1); }
}

.ai-generate-btn:hover .fa-magic {
    animation-duration: 0.5s;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {

    // ===== AI Content Generation =====

    // معالج الأزرار الفردية للذكاء الاصطناعي
    $('.ai-generate-btn').on('click', function() {
        const button = $(this);
        const target = button.data('target');
        const type = button.data('type');
        const targetField = $('#' + target);

        // التحقق من وجود اسم الدورة
        const courseName = $('#name').val().trim();
        if (!courseName) {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'يرجى إدخال اسم الدورة أولاً',
                confirmButtonText: 'حسناً'
            });
            return;
        }

        generateAIContent(button, targetField, type, courseName);
    });

    // معالج زر "توليد الكل"
    $('.ai-generate-all-btn').on('click', function() {
        const button = $(this);
        const courseName = $('#name').val().trim();

        if (!courseName) {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'يرجى إدخال اسم الدورة أولاً',
                confirmButtonText: 'حسناً'
            });
            return;
        }

        generateAllAIContent(button, courseName);
    });

    // دالة توليد محتوى واحد
    function generateAIContent(button, targetField, type, courseName) {
        const originalText = button.html();

        // تغيير حالة الزر
        button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> جاري التوليد...');

        // إعداد البيانات - نرسل اسم الدورة فقط
        const data = {
            course_name: courseName,
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        // تحديد الرابط حسب النوع
        let url = '';
        switch(type) {
            case 'short-description':
                url = '<?php echo e(route("dashboard.ai-content.generate-short-description")); ?>';
                break;
            case 'objectives':
                url = '<?php echo e(route("dashboard.ai-content.generate-objectives")); ?>';
                break;
            case 'target-audience':
                url = '<?php echo e(route("dashboard.ai-content.generate-target-audience")); ?>';
                break;
        }

        // إرسال الطلب
        $.ajax({
            url: url,
            method: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    // إدراج المحتوى المولد
                    if (type === 'short-description') {
                        targetField.val(response.data.short_description);
                    } else if (type === 'objectives') {
                        targetField.val(response.data.objectives);
                    } else if (type === 'target-audience') {
                        targetField.val(response.data.target_audience);
                    }

                    // إظهار رسالة نجاح
                    Swal.fire({
                        icon: 'success',
                        title: 'تم بنجاح!',
                        text: response.message,
                        timer: 2000,
                        showConfirmButton: false
                    });

                    // تأثير بصري للحقل
                    targetField.addClass('highlight-field');
                    setTimeout(() => {
                        targetField.removeClass('highlight-field');
                    }, 2000);
                } else {
                    showError('فشل في توليد المحتوى: ' + response.message);
                }
            },
            error: function(xhr) {
                let errorMessage = 'حدث خطأ في الاتصال بالخادم';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showError(errorMessage);
            },
            complete: function() {
                // إعادة تعيين الزر
                button.prop('disabled', false).html(originalText);
            }
        });
    }

    // دالة توليد جميع المحتويات
    function generateAllAIContent(button, courseName) {
        const originalText = button.html();

        // تغيير حالة الزر
        button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> جاري توليد جميع المحتويات...');

        // إعداد البيانات - نرسل اسم الدورة فقط
        const data = {
            course_name: courseName,
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        // إرسال الطلب
        $.ajax({
            url: '<?php echo e(route("dashboard.ai-content.generate-all")); ?>',
            method: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    // إدراج جميع المحتويات
                    $('#Short_description').val(response.data.short_description);
                    $('#course_objectives').val(response.data.objectives);
                    $('#target_audience').val(response.data.target_audience);

                    // إظهار رسالة نجاح
                    Swal.fire({
                        icon: 'success',
                        title: 'تم بنجاح!',
                        text: response.message,
                        timer: 3000,
                        showConfirmButton: false
                    });

                    // تأثير بصري لجميع الحقول
                    $('#Short_description, #course_objectives, #target_audience').addClass('highlight-field');
                    setTimeout(() => {
                        $('#Short_description, #course_objectives, #target_audience').removeClass('highlight-field');
                    }, 3000);
                } else {
                    showError('فشل في توليد المحتويات: ' + response.message);
                }
            },
            error: function(xhr) {
                let errorMessage = 'حدث خطأ في الاتصال بالخادم';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showError(errorMessage);
            },
            complete: function() {
                // إعادة تعيين الزر
                button.prop('disabled', false).html(originalText);
            }
        });
    }

    // دالة إظهار الأخطاء
    function showError(message) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: message,
            confirmButtonText: 'حسناً'
        });
    }
    // تفعيل Select2 للقوائم العادية
    $('.select2:not(#instructors)').select2({
        placeholder: "اختر من القائمة",
        allowClear: true
    });

    // تفعيل Select2 للمدربين مع إعدادات خاصة
    $('#instructors').select2({
        placeholder: "اختر المدربين للدورة",
        allowClear: true,
        closeOnSelect: false,
        dir: "rtl",
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });

    // معاينة المدربين المختارين
    $('#instructors').on('change', function() {
        var selectedInstructors = $(this).val();
        var previewDiv = $('#selected-instructors-preview');
        var listDiv = $('#instructors-list');

        if (selectedInstructors && selectedInstructors.length > 0) {
            var instructorsList = '';

            selectedInstructors.forEach(function(instructorId) {
                var option = $('#instructors option[value="' + instructorId + '"]');
                var instructorText = option.text();

                instructorsList += '<div class="instructor-item" style="background: #f0f8ff; padding: 8px; margin: 5px 0; border-radius: 5px; border-left: 3px solid #3c8dbc;">';
                instructorsList += '<i class="fa fa-user text-primary"></i> ' + instructorText;
                instructorsList += '</div>';
            });

            listDiv.html(instructorsList);
            previewDiv.show();
        } else {
            previewDiv.hide();
        }
    });

    // عداد الأحرف للوصف المختصر
    $('#Short_description').on('input', function() {
        var length = $(this).val().length;
        var maxLength = 150;
        var remaining = maxLength - length;

        if (remaining < 0) {
            $(this).addClass('text-danger');
        } else {
            $(this).removeClass('text-danger');
        }
    });

    // معاينة الصورة
    $('#image').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                // يمكن إضافة معاينة للصورة هنا
            };
            reader.readAsDataURL(file);
        }
    });

    // تحقق من النموذج قبل الإرسال
    $('#courseForm').on('submit', function(e) {
        var isValid = true;
        var errors = [];

        // التحقق من الحقول المطلوبة
        if (!$('#name').val().trim()) {
            errors.push('اسم الدورة مطلوب');
            isValid = false;
        }

        if (!$('#price').val() || $('#price').val() <= 0) {
            errors.push('السعر مطلوب ويجب أن يكون أكبر من صفر');
            isValid = false;
        }

        if (!$('#time').val() || $('#time').val() <= 0) {
            errors.push('عدد الساعات مطلوب ويجب أن يكون أكبر من صفر');
            isValid = false;
        }

        // التحقق من المدربين (اختياري ولكن يُنصح به)
        var selectedInstructors = $('#instructors').val();
        if (!selectedInstructors || selectedInstructors.length === 0) {
            if (!confirm('لم تختر أي مدربين للدورة. هل تريد المتابعة؟')) {
                isValid = false;
            }
        }

        if (!isValid) {
            e.preventDefault();
            alert('يرجى تصحيح الأخطاء التالية:\n' + errors.join('\n'));
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Academy\resources\views/dashboard/course/create.blade.php ENDPATH**/ ?>