/* ===================================
   تحسينات لوحة التحكم - أكاديمية Leaders Vision
   Dashboard Enhancements for Leaders Vision Academy
   =================================== */

/* تعريف المتغيرات */
:root {
    --dashboard-primary: #26c7f9;
    --dashboard-secondary: #18191A;
    --dashboard-success: #28a745;
    --dashboard-warning: #ffc107;
    --dashboard-danger: #dc3545;
    --dashboard-info: #17a2b8;
    --dashboard-light: #f8f9fa;
    --dashboard-dark: #343a40;

    --dashboard-font: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --dashboard-border-radius: 6px;
    --dashboard-transition: all 0.3s ease;
    --dashboard-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* الخط الأساسي للوحة التحكم */
body, .main-sidebar, .content-wrapper, .main-header {
    font-family: var(--dashboard-font) !important;
    direction: rtl;
    text-align: right;
}

/* تحسين العناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--dashboard-font) !important;
    font-weight: 600;
    color: var(--dashboard-dark);
}

/* تحسين الجداول */
.table {
    font-family: var(--dashboard-font) !important;
    border-radius: var(--dashboard-border-radius);
    overflow: hidden;
    box-shadow: var(--dashboard-shadow);
}

.table th {
    background-color: var(--dashboard-primary);
    color: white;
    font-weight: 600;
    border: none;
    padding: 15px;
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
}

.table tbody tr:hover {
    background-color: rgba(38, 199, 249, 0.05);
    transition: var(--dashboard-transition);
}

/* تحسين البطاقات */
.box, .card {
    border-radius: var(--dashboard-border-radius);
    box-shadow: var(--dashboard-shadow);
    border: none;
    transition: var(--dashboard-transition);
}

.box:hover, .card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.box-header {
    background: linear-gradient(135deg, var(--dashboard-primary), #1e9bc7);
    color: white;
    border-radius: var(--dashboard-border-radius) var(--dashboard-border-radius) 0 0;
    padding: 15px 20px;
}

.box-header h3 {
    color: white;
    margin: 0;
    font-weight: 600;
}

/* تحسين الأزرار */
.btn {
    font-family: var(--dashboard-font) !important;
    border-radius: var(--dashboard-border-radius);
    padding: 8px 16px;
    font-weight: 500;
    transition: var(--dashboard-transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--dashboard-primary), #1e9bc7);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e9bc7, var(--dashboard-primary));
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(38, 199, 249, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--dashboard-success), #218838);
}

.btn-warning {
    background: linear-gradient(135deg, var(--dashboard-warning), #e0a800);
}

.btn-danger {
    background: linear-gradient(135deg, var(--dashboard-danger), #c82333);
}

.btn-info {
    background: linear-gradient(135deg, var(--dashboard-info), #138496);
}

/* تحسين النماذج */
.form-control {
    font-family: var(--dashboard-font) !important;
    border-radius: var(--dashboard-border-radius);
    border: 2px solid #e9ecef;
    padding: 10px 15px;
    transition: var(--dashboard-transition);
}

.form-control:focus {
    border-color: var(--dashboard-primary);
    box-shadow: 0 0 0 0.2rem rgba(38, 199, 249, 0.25);
}

.form-group label {
    font-family: var(--dashboard-font) !important;
    font-weight: 500;
    color: var(--dashboard-dark);
    margin-bottom: 8px;
}

/* تحسين الشريط الجانبي */
.main-sidebar {
    background: linear-gradient(180deg, var(--dashboard-secondary), #2c3e50);
}

.sidebar-menu > li > a {
    font-family: var(--dashboard-font) !important;
    color: #b0bec5;
    transition: var(--dashboard-transition);
    border-radius: 0 25px 25px 0;
    margin: 2px 10px 2px 0;
}

.sidebar-menu > li > a:hover,
.sidebar-menu > li.active > a {
    background: linear-gradient(135deg, var(--dashboard-primary), #1e9bc7);
    color: white;
    transform: translateX(-5px);
}

.sidebar-menu > li > a > .fa {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* تحسين الهيدر */
.main-header {
    background: linear-gradient(135deg, white, #f8f9fa);
    box-shadow: var(--dashboard-shadow);
}

.navbar-brand {
    font-family: var(--dashboard-font) !important;
    font-weight: 700;
    color: var(--dashboard-primary) !important;
}

/* تحسين الإحصائيات */
.info-box {
    border-radius: var(--dashboard-border-radius);
    box-shadow: var(--dashboard-shadow);
    transition: var(--dashboard-transition);
    overflow: hidden;
}

.info-box:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.info-box-icon {
    border-radius: var(--dashboard-border-radius) 0 0 var(--dashboard-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-box-content {
    padding: 15px;
}

.info-box-text {
    font-family: var(--dashboard-font) !important;
    font-weight: 500;
    color: var(--dashboard-dark);
}

.info-box-number {
    font-family: var(--dashboard-font) !important;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--dashboard-primary);
}

/* تحسين التنبيهات */
.alert {
    font-family: var(--dashboard-font) !important;
    border-radius: var(--dashboard-border-radius);
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    color: var(--dashboard-success);
    border-right: 4px solid var(--dashboard-success);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    color: #856404;
    border-right: 4px solid var(--dashboard-warning);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    color: var(--dashboard-danger);
    border-right: 4px solid var(--dashboard-danger);
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
    color: var(--dashboard-info);
    border-right: 4px solid var(--dashboard-info);
}

/* تحسين التصفح */
.pagination {
    font-family: var(--dashboard-font) !important;
}

.pagination > li > a,
.pagination > li > span {
    border-radius: var(--dashboard-border-radius);
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: var(--dashboard-primary);
    transition: var(--dashboard-transition);
}

.pagination > li > a:hover {
    background-color: var(--dashboard-primary);
    color: white;
    transform: translateY(-1px);
}

.pagination > .active > a {
    background-color: var(--dashboard-primary);
    border-color: var(--dashboard-primary);
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    font-family: var(--dashboard-font) !important;
    border-radius: var(--dashboard-border-radius);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border: none;
}

.dropdown-item {
    transition: var(--dashboard-transition);
}

.dropdown-item:hover {
    background-color: rgba(38, 199, 249, 0.1);
    color: var(--dashboard-primary);
}

/* تحسين الأيقونات */
.fa, .fas, .far, .fab {
    transition: var(--dashboard-transition);
}

.btn:hover .fa,
.btn:hover .fas,
.btn:hover .far,
.btn:hover .fab {
    transform: scale(1.1);
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .content-wrapper {
        margin-right: 0;
    }

    .main-sidebar {
        transform: translateX(100%);
    }

    .sidebar-open .main-sidebar {
        transform: translateX(0);
    }

    .table-responsive {
        border-radius: var(--dashboard-border-radius);
    }
}

/* تحسين التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(38, 199, 249, 0.3);
    border-radius: 50%;
    border-top-color: var(--dashboard-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسين الطباعة */
@media print {
    .main-sidebar,
    .main-header,
    .btn,
    .pagination {
        display: none !important;
    }

    .content-wrapper {
        margin: 0 !important;
    }

    .box,
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
