-- إضافة حقل purchase_id إلى جدول payment_installments
-- نفذ هذه الأوامر في phpMyAdmin أو MySQL

-- 1. إضافة العمود الجديد
ALTER TABLE `payment_installments` 
ADD COLUMN `purchase_id` bigint(20) UNSIGNED NULL AFTER `student_payment_id`;

-- 2. إضافة فهرس للبحث السريع
ALTER TABLE `payment_installments` 
ADD INDEX `payment_installments_purchase_id_index` (`purchase_id`);

-- 3. إضافة foreign key constraint (اختياري - يمكن تخطيه إذا كان هناك مشاكل)
-- ALTER TABLE `payment_installments` 
-- ADD CONSTRAINT `payment_installments_purchase_id_foreign` 
-- FOREIGN KEY (`purchase_id`) REFERENCES `purchases` (`id`) ON DELETE CASCADE;

-- 4. التحقق من النتيجة
-- DESCRIBE payment_installments;

-- 5. اختبار إدراج بيانات تجريبية (اختياري)
-- INSERT INTO payment_installments (
--     purchase_id, 
--     installment_number, 
--     amount, 
--     due_date, 
--     paid_date, 
--     status, 
--     payment_method
-- ) VALUES (
--     2,              -- purchase_id
--     1,              -- installment_number  
--     1000.00,        -- amount
--     CURDATE(),      -- due_date
--     CURDATE(),      -- paid_date
--     'paid',         -- status
--     'نقداً'          -- payment_method
-- );

-- ملاحظات:
-- - تأكد من وجود جدول purchases قبل تشغيل الأوامر
-- - العمود student_payment_id سيبقى للتوافق مع النظام القديم
-- - يمكن جعل purchase_id NULL في البداية ثم تحديثه لاحقاً
-- - بعد إضافة العمود، النظام سيعمل مع الهيكل الجديد تلقائياً
