/**
 * إصلاح مشكلة الأيقونات - أكاديمية Leaders Vision
 * Icons Fix JavaScript - Leaders Vision Academy
 */

$(document).ready(function() {
    
    // إصلاح الأيقونات المفقودة
    fixMissingIcons();
    
    // إضافة أيقونات بديلة
    addFallbackIcons();
    
    // تحسين مظهر الأيقونات
    enhanceIconsAppearance();
    
    // مراقبة تحميل الخطوط
    monitorFontLoading();
});

/**
 * إصلاح الأيقونات المفقودة
 */
function fixMissingIcons() {
    // البحث عن الأيقونات التي لا تظهر
    $('.fa').each(function() {
        var $icon = $(this);
        var iconClass = getIconClass($icon);
        
        // إذا كانت الأيقونة لا تظهر، أضف كلاس بديل
        if (!isIconVisible($icon)) {
            $icon.addClass('icon-fallback');
            console.log('Fixed missing icon:', iconClass);
        }
    });
}

/**
 * إضافة أيقونات بديلة
 */
function addFallbackIcons() {
    var iconMap = {
        'fa-graduation-cap': '🎓',
        'fa-users': '👥',
        'fa-user-tie': '👔',
        'fa-eye': '👁',
        'fa-bullseye': '🎯',
        'fa-star': '⭐',
        'fa-lightbulb-o': '💡',
        'fa-handshake-o': '🤝',
        'fa-shield': '🛡',
        'fa-cogs': '⚙',
        'fa-rocket': '🚀',
        'fa-laptop': '💻',
        'fa-certificate': '📜',
        'fa-award': '🏆',
        'fa-building': '🏢',
        'fa-tag': '🏷',
        'fa-clock': '🕐',
        'fa-shopping-cart': '🛒',
        'fa-phone': '📞',
        'fa-envelope': '✉',
        'fa-map-marker': '📍',
        'fa-home': '🏠',
        'fa-book': '📚',
        'fa-gear': '⚙',
        'fa-university': '🏛',
        'fa-angle-up': '↑',
        'fa-angle-left': '←',
        'fa-facebook': 'f',
        'fa-twitter': 't',
        'fa-instagram': 'i',
        'fa-linkedin': 'in',
        'fa-youtube': 'yt',
        'fa-whatsapp': 'w'
    };
    
    // تطبيق الأيقونات البديلة
    Object.keys(iconMap).forEach(function(iconClass) {
        $('.' + iconClass + '.icon-fallback').each(function() {
            var $icon = $(this);
            if (!$icon.text().trim()) {
                $icon.text(iconMap[iconClass]);
                $icon.css({
                    'font-family': 'Arial, sans-serif',
                    'font-size': '1em',
                    'line-height': '1'
                });
            }
        });
    });
}

/**
 * تحسين مظهر الأيقونات
 */
function enhanceIconsAppearance() {
    // إضافة تأثيرات للأيقونات
    $('.fa').hover(
        function() {
            $(this).addClass('icon-hover');
        },
        function() {
            $(this).removeClass('icon-hover');
        }
    );
    
    // إضافة تأثيرات للأيقونات الكبيرة
    $('.fa-2x, .fa-3x').addClass('icon-large');
    
    // تحسين أيقونات الأزرار
    $('.btn .fa').addClass('btn-icon');
    
    // تحسين أيقونات القوائم
    $('.sidebar-menu .fa, .main-menu .fa').addClass('menu-icon');
}

/**
 * مراقبة تحميل الخطوط
 */
function monitorFontLoading() {
    // التحقق من تحميل Font Awesome
    if (document.fonts && document.fonts.ready) {
        document.fonts.ready.then(function() {
            console.log('Fonts loaded successfully');
            // إعادة فحص الأيقونات بعد تحميل الخطوط
            setTimeout(function() {
                fixMissingIcons();
            }, 500);
        });
    } else {
        // للمتصفحات القديمة
        setTimeout(function() {
            fixMissingIcons();
        }, 2000);
    }
}

/**
 * الحصول على كلاس الأيقونة
 */
function getIconClass($icon) {
    var classes = $icon.attr('class').split(' ');
    for (var i = 0; i < classes.length; i++) {
        if (classes[i].startsWith('fa-')) {
            return classes[i];
        }
    }
    return '';
}

/**
 * التحقق من ظهور الأيقونة
 */
function isIconVisible($icon) {
    // طريقة بسيطة للتحقق من ظهور الأيقونة
    var computedStyle = window.getComputedStyle($icon[0], ':before');
    var content = computedStyle.getPropertyValue('content');
    
    // إذا كان المحتوى فارغ أو "none"، فالأيقونة لا تظهر
    return content && content !== 'none' && content !== '""';
}

/**
 * إضافة أيقونات ديناميكية
 */
function addDynamicIcon(element, iconClass, fallbackText) {
    var $element = $(element);
    var $icon = $('<i>').addClass('fa ' + iconClass);
    
    // إضافة الأيقونة
    $element.prepend($icon);
    
    // التحقق من ظهورها
    setTimeout(function() {
        if (!isIconVisible($icon)) {
            $icon.addClass('icon-fallback').text(fallbackText);
        }
    }, 100);
}

/**
 * إصلاح أيقونات محددة
 */
function fixSpecificIcons() {
    // إصلاح أيقونة التمرير للأعلى
    $('#scrollUp .fa-angle-up').each(function() {
        if (!isIconVisible($(this))) {
            $(this).html('↑').css({
                'font-family': 'Arial, sans-serif',
                'font-weight': 'bold'
            });
        }
    });
    
    // إصلاح أيقونات القائمة
    $('.sidebar-menu .fa').each(function() {
        var $icon = $(this);
        if (!isIconVisible($icon)) {
            var iconClass = getIconClass($icon);
            switch(iconClass) {
                case 'fa-th':
                    $icon.text('■').css('font-family', 'Arial, sans-serif');
                    break;
                case 'fa-users':
                    $icon.text('👥').css('font-family', 'Arial, sans-serif');
                    break;
                case 'fa-gear':
                    $icon.text('⚙').css('font-family', 'Arial, sans-serif');
                    break;
                default:
                    $icon.text('•').css('font-family', 'Arial, sans-serif');
            }
        }
    });
}

/**
 * إضافة CSS ديناميكي للأيقونات
 */
function addDynamicIconStyles() {
    var styles = `
        <style id="dynamic-icon-styles">
            .icon-hover {
                transform: scale(1.1);
                transition: transform 0.3s ease;
            }
            
            .icon-large {
                text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }
            
            .btn-icon {
                margin-left: 5px;
            }
            
            .menu-icon {
                width: 16px;
                text-align: center;
                margin-left: 8px;
            }
            
            .icon-fallback {
                font-family: Arial, sans-serif !important;
                font-style: normal !important;
                font-weight: normal !important;
                text-decoration: none !important;
            }
            
            .fa.icon-fallback:before {
                content: none !important;
            }
        </style>
    `;
    
    if (!$('#dynamic-icon-styles').length) {
        $('head').append(styles);
    }
}

/**
 * تشغيل الإصلاحات عند تحميل الصفحة
 */
$(window).on('load', function() {
    // إضافة الأنماط الديناميكية
    addDynamicIconStyles();
    
    // إصلاح الأيقونات المحددة
    fixSpecificIcons();
    
    // إعادة فحص الأيقونات
    setTimeout(function() {
        fixMissingIcons();
        addFallbackIcons();
    }, 1000);
});

/**
 * إصلاح الأيقونات عند إضافة محتوى جديد
 */
$(document).on('DOMNodeInserted', function(e) {
    var $newContent = $(e.target);
    if ($newContent.find('.fa').length > 0) {
        setTimeout(function() {
            $newContent.find('.fa').each(function() {
                var $icon = $(this);
                if (!isIconVisible($icon)) {
                    $icon.addClass('icon-fallback');
                }
            });
            addFallbackIcons();
        }, 100);
    }
});

// تصدير الوظائف للاستخدام العام
window.IconsFix = {
    fix: fixMissingIcons,
    addFallback: addFallbackIcons,
    enhance: enhanceIconsAppearance,
    addDynamic: addDynamicIcon
};
