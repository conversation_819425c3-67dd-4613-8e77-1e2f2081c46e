@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">
        <section class="content-header">
            <h1>إضافة دفعة للمشترك</h1>
            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
                <li><a href="{{ route('dashboard.purchases.index') }}">طلبات الكورسات</a></li>
                <li class="active">إضافة دفعة</li>
            </ol>
        </section>

        <section class="content">

            <!-- معلومات المشترك -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات المشترك</h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>اسم الطالب:</strong>
                                    <p>{{ $purchase->first_name }} {{ $purchase->last_name }}</p>
                                </div>
                                <div class="col-md-3">
                                    <strong>البريد الإلكتروني:</strong>
                                    <p>{{ $purchase->email }}</p>
                                </div>
                                <div class="col-md-3">
                                    <strong>رقم الهاتف:</strong>
                                    <p>{{ $purchase->phone }}</p>
                                </div>
                                <div class="col-md-3">
                                    <strong>الدورة:</strong>
                                    <p><span class="label label-info">{{ $purchase->name_course }}</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الدفع -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات الدفع</h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="info-box bg-blue">
                                        <span class="info-box-icon"><i class="fa fa-money"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">سعر الدورة المتوقع</span>
                                            <span class="info-box-number">{{ number_format($coursePrice, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-green">
                                        <span class="info-box-icon"><i class="fa fa-check"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">المدفوع حالياً</span>
                                            <span class="info-box-number">{{ number_format($totalPaid, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-yellow">
                                        <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">المتبقي</span>
                                            <span class="info-box-number">{{ number_format($remaining, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-red">
                                        <span class="info-box-icon"><i class="fa fa-percent"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">التقدم</span>
                                            <span class="info-box-number">{{ $progress }}%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- شريط التقدم -->
                            <div class="progress progress-lg">
                                <div class="progress-bar progress-bar-{{ $progress >= 100 ? 'success' : ($progress >= 50 ? 'warning' : 'danger') }}"
                                     style="width: {{ $progress }}%">
                                    {{ $progress }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج إضافة دفعة -->
            <div class="row">
                <div class="col-md-8">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">إضافة دفعة جديدة</h3>
                        </div>
                        <form action="{{ route('dashboard.add-payment-simple.store', $purchase) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="box-body">

                                <!-- مبلغ الدفعة -->
                                <div class="form-group">
                                    <label for="amount">مبلغ الدفعة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number"
                                               class="form-control"
                                               id="amount"
                                               name="amount"
                                               step="0.01"
                                               min="0.01"
                                               max="{{ $remaining }}"
                                               value="{{ old('amount') }}"
                                               required>
                                        <span class="input-group-addon">دج</span>
                                    </div>
                                    <small class="text-muted">الحد الأقصى: {{ number_format($remaining, 2) }} دج</small>
                                    @error('amount')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- طريقة الدفع -->
                                <div class="form-group">
                                    <label for="payment_method">طريقة الدفع <span class="text-danger">*</span></label>
                                    <select class="form-control select2" id="payment_method" name="payment_method" required style="width: 100%;">
                                        <option value="">-- اختر طريقة الدفع --</option>
                                        <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>
                                            💵 نقداً
                                        </option>
                                        <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>
                                            🏦 تحويل بنكي
                                        </option>
                                        <option value="credit_card" {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>
                                            💳 بطاقة ائتمان
                                        </option>
                                        <option value="mobile_payment" {{ old('payment_method') == 'mobile_payment' ? 'selected' : '' }}>
                                            📱 دفع عبر الهاتف (CCP, Baridimob)
                                        </option>
                                        <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>
                                            📄 شيك
                                        </option>
                                        <option value="installment" {{ old('payment_method') == 'installment' ? 'selected' : '' }}>
                                            📅 دفع بالتقسيط
                                        </option>
                                        <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>
                                            ❓ أخرى
                                        </option>
                                    </select>
                                    @error('payment_method')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- رقم الإيصال -->
                                <div class="form-group">
                                    <label for="receipt_number">رقم الإيصال</label>
                                    <input type="text"
                                           class="form-control"
                                           id="receipt_number"
                                           name="receipt_number"
                                           value="{{ old('receipt_number') }}"
                                           placeholder="رقم الإيصال أو المرجع">
                                    @error('receipt_number')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- صورة الإيصال -->
                                <div class="form-group">
                                    <label for="receipt_image">صورة الإيصال</label>
                                    <input type="file"
                                           class="form-control"
                                           id="receipt_image"
                                           name="receipt_image"
                                           accept="image/*">
                                    <small class="text-muted">الصيغ المدعومة: JPG, PNG, JPEG - الحد الأقصى: 2MB</small>
                                    @error('receipt_image')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- ملاحظات -->
                                <div class="form-group">
                                    <label for="notes">ملاحظات</label>
                                    <textarea class="form-control"
                                              id="notes"
                                              name="notes"
                                              rows="3"
                                              placeholder="أي ملاحظات إضافية...">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                            </div>
                            <div class="box-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fa fa-save"></i> إضافة الدفعة
                                </button>
                                <a href="{{ route('dashboard.purchases.show', $purchase) }}" class="btn btn-default">
                                    <i class="fa fa-times"></i> إلغاء
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="col-md-4">
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات مهمة</h3>
                        </div>
                        <div class="box-body">
                            <div class="callout callout-info">
                                <h4><i class="fa fa-info"></i> ملاحظة:</h4>
                                <p>سيتم حفظ الدفعة وتحديث حالة المشترك تلقائياً.</p>
                            </div>

                            <div class="callout callout-warning">
                                <h4><i class="fa fa-warning"></i> تنبيه:</h4>
                                <p>المشترك سيُعتبر "مدفوع" فقط عند إكمال دفع المبلغ الكامل للدورة.</p>
                            </div>

                            <div class="callout callout-success">
                                <h4><i class="fa fa-check"></i> حالة الطلب:</h4>
                                <p>
                                    @if($purchase->status == '1')
                                        <span class="label label-success">مقبول</span>
                                    @elseif($purchase->status == '0')
                                        <span class="label label-warning">في الانتظار</span>
                                    @else
                                        <span class="label label-danger">مرفوض</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- الدفعات السابقة -->
                    @if(count($previousPayments) > 0)
                    <div class="box box-default">
                        <div class="box-header with-border">
                            <h3 class="box-title">الدفعات السابقة</h3>
                        </div>
                        <div class="box-body">
                            @foreach($previousPayments as $payment)
                                <div class="payment-item" style="border-bottom: 1px solid #f0f0f0; padding: 8px 0;">
                                    <div class="row">
                                        <div class="col-xs-8">
                                            <strong>{{ number_format($payment['amount'], 0) }} دج</strong>
                                            <br>
                                            <small class="text-muted">{{ $payment['date'] }}</small>
                                            <br>
                                            <small class="text-info">القسط رقم {{ $payment['installment_number'] }}</small>
                                        </div>
                                        <div class="col-xs-4">
                                            <span class="label label-success">مؤكد</span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>
            </div>

        </section>
    </div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // تفعيل Select2 لطريقة الدفع
    $('#payment_method').select2({
        placeholder: "-- اختر طريقة الدفع --",
        allowClear: true,
        dir: "rtl",
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });

    // حساب المبلغ المتبقي تلقائياً
    $('#amount').on('input', function() {
        var amount = parseFloat($(this).val()) || 0;
        var remaining = {{ $remaining }};

        if (amount > remaining) {
            $(this).val(remaining.toFixed(2));
            alert('المبلغ لا يمكن أن يكون أكبر من المبلغ المتبقي');
        }
    });

    // تحديث رقم الإيصال تلقائياً حسب طريقة الدفع
    $('#payment_method').on('change', function() {
        var method = $(this).val();
        var receiptField = $('#receipt_number');
        var currentDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');

        switch(method) {
            case 'cash':
                receiptField.attr('placeholder', 'مثال: CASH' + currentDate + '001');
                break;
            case 'bank_transfer':
                receiptField.attr('placeholder', 'مثال: BANK' + currentDate + '001');
                break;
            case 'credit_card':
                receiptField.attr('placeholder', 'مثال: CARD' + currentDate + '001');
                break;
            case 'mobile_payment':
                receiptField.attr('placeholder', 'مثال: CCP' + currentDate + '001');
                break;
            case 'check':
                receiptField.attr('placeholder', 'مثال: CHK' + currentDate + '001');
                break;
            default:
                receiptField.attr('placeholder', 'رقم الإيصال أو المرجع');
        }
    });
});
</script>
@endpush

@push('styles')
<style>
.info-box {
    margin-bottom: 15px;
}

.progress-lg {
    height: 30px;
    margin-bottom: 20px;
}

.progress-lg .progress-bar {
    font-size: 16px;
    line-height: 30px;
}

.payment-item:last-child {
    border-bottom: none !important;
}

.callout {
    margin-bottom: 15px;
}

/* تحسين مظهر Select2 */
.select2-container--default .select2-selection--single {
    height: 34px;
    border: 1px solid #d2d6de;
    border-radius: 4px;
    padding: 6px 12px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: 20px;
    padding-left: 0;
    padding-right: 20px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 32px;
    right: 10px;
}

.select2-dropdown {
    border: 1px solid #d2d6de;
    border-radius: 4px;
}

.select2-container--default .select2-results__option {
    padding: 8px 12px;
    font-size: 14px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #3c8dbc;
    color: white;
}

/* تحسين النص العربي في Select */
.select2-container {
    direction: rtl;
    text-align: right;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    text-align: right;
    direction: rtl;
}
</style>
@endpush
