<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Purchase extends Model
{
	public $guarded = [];

    protected $casts = [
        'paid_amount' => 'decimal:2'
    ];

    protected $appends = ['image_path'];

    public function getImagePathAttribute()
    {
        return asset('uploads/bill_image/' . $this->bill_image);

    }//end of get image path

    public function scopeWhenSearch($query , $search)
    {
        return $query->when($search, function ($q) use ($search) {

            return $q->where('email' , 'like', "%$search%")

            ->orWhere('first_name' , 'like', "%$search%")

            ->orWhere('last_name' , 'like', "%$search%")

            ->orWhere('phone' , 'like', "%$search%")

            ->orWhere('name_course' , 'like', "%$search%")

            ->orWhere('status' , 'like', "%$search%");

        });
    }//end ofscopeWhenSearch

    public function course()
    {
        return $this->belongsTo('App\Models\Course', 'course_id');
    }//belongsTo course

    public function paymentTracking()
    {
        return $this->hasOne(StudentPaymentTracking::class);
    }

    public function purchasePayments()
    {
        return $this->hasMany(PurchasePayment::class);
    }

    public function verifiedPayments()
    {
        return $this->hasMany(PurchasePayment::class)->where('status', 'verified');
    }

    public function paymentInstallments()
    {
        return $this->hasMany(PaymentInstallment::class);
    }

    public function paidInstallments()
    {
        return $this->hasMany(PaymentInstallment::class)->where('status', 'paid');
    }

    // دوال مساعدة للدفع
    public function getCoursePrice()
    {
        return $this->course ? $this->course->price : 0;
    }

    public function getRemainingAmount()
    {
        return $this->getCoursePrice() - $this->paid_amount;
    }

    public function getPaymentProgress()
    {
        $coursePrice = $this->getCoursePrice();
        return $coursePrice > 0 ? round(($this->paid_amount / $coursePrice) * 100, 2) : 0;
    }

    public function isFullyPaid()
    {
        return $this->getRemainingAmount() <= 0;
    }

    public function addPayment($amount)
    {
        $this->paid_amount += $amount;

        // تحديث الحالة إذا تم إكمال الدفع
        if ($this->isFullyPaid()) {
            $this->status = '1'; // مقبول ومدفوع
        }

        $this->save();
        return $this;
    }

    public function getTotalPaidFromInstallments()
    {
        return $this->paidInstallments()->sum('amount');
    }

    public function syncPaidAmount()
    {
        // تحديث paid_amount من مجموع الدفعات المؤكدة
        $totalFromInstallments = $this->getTotalPaidFromInstallments();
        $this->paid_amount = $totalFromInstallments;

        // تحديث الحالة إذا تم إكمال الدفع
        if ($this->isFullyPaid()) {
            $this->status = '1';
        }

        $this->save();
        return $this;
    }

    public function getNextInstallmentNumber()
    {
        $lastInstallment = $this->paymentInstallments()
                               ->orderBy('installment_number', 'desc')
                               ->first();

        return $lastInstallment ? $lastInstallment->installment_number + 1 : 1;
    }

}//end of models
