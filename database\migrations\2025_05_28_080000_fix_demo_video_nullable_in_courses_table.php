<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class FixDemoVideoNullableInCoursesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('courses', function (Blueprint $table) {
            // جعل حقل demo_video قابل للقيم الفارغة
            $table->string('demo_video')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('courses', function (Blueprint $table) {
            // إرجاع الحقل لحالته الأصلية (غير قابل للقيم الفارغة)
            $table->string('demo_video')->nullable(false)->change();
        });
    }
}
