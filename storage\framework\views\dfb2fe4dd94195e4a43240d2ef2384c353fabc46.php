

        <div class="full-width-header header-style2 modify1 header-home6">
            <!--Header Start-->
            <header class="rs-header" id="rs-header">
                <!-- Menu Start -->
                <div class="menu-area menu-sticky sticky" id="custom-nav">
                    <div class="container">
                        <div class="row y-middle d-flex flex-row-reverse">
                            <div class="col-lg-3">
                                <div class="logo-cat-wrap">
                                    <div class="logo-part pr-90">
                                        <a href="<?php echo e(route('home')); ?>">
                                             <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'components.safe-image','data' => ['src' => 'images/logo.png','alt' => 'أكاديمية Leaders Vision','style' => 'height: 60px; width: auto;','fallback' => 'home_file/assets/images/logo.png']]); ?>
<?php $component->withName('safe-image'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['src' => 'images/logo.png','alt' => 'أكاديمية Leaders Vision','style' => 'height: 60px; width: auto;','fallback' => 'home_file/assets/images/logo.png']); ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?> 
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-9 text-center d-flex flex-row-reverse">
                                <div class="rs-menu-area">
                                    <div class="main-menu pr-90 md-pr-15">
                                        <div class="mobile-menu">
                                            <a class="rs-menu-toggle">
                                                <i class="fa fa-bars">
                                                </i>
                                            </a>
                                        </div>
                                        <nav class="rs-menu">
                                            <ul class="nav-menu">

                                                <?php if(auth()->guard()->check()): ?>
                                                <li class="menu-item-has-children">
                                                    <a href="#">
                                                        <img src="<?php echo e(auth()->user()->image_path); ?>" alt="" class="rounded-circle" width="50px">
                                                    </a>
                                                    <ul class="sub-menu">
                                                        <?php if(auth()->user()->hasPermission('dashboard_read')): ?>
                                                        <li>
                                                            <a href="<?php echo e(route('dashboard.welcome')); ?>">
                                                                لوحه التحكم
                                                            </a>
                                                        </li>
                                                        <?php endif; ?>
                                                        <li>
                                                            <a href="<?php echo e(route('my_course',auth()->user()->id)); ?>">
                                                                دوراتي
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="<?php echo e(route('profile.index',auth()->user()->id)); ?>">
                                                                الملف الشخصي
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="<?php echo e(route('logout')); ?>"
                                                                onclick="event.preventDefault();
                                                                 document.getElementById('logout-form').submit();">
                                                                تسجيل الخروج
                                                            </a>
                                                            <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display: none;">
                                                                <?php echo csrf_field(); ?>
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </li>

                                                <?php else: ?>

                                                <li class="menu-item-has-children">
                                                    <a href="#">
                                                        تسجيل <i class="fa fa-user-plus"></i>
                                                    </a>
                                                    <ul class="sub-menu">
                                                        <li>
                                                            <a href="<?php echo e(route('Login_Client')); ?>">
                                                                تسجيل الدخول
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="<?php echo e(route('Register_Client')); ?>">
                                                                تسجيل
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </li>

                                                <?php endif; ?>

                                                <li class="menu-item-has-children">
                                                    <a href="#Contact">
                                                        اتصل بنا
                                                    </a>
                                                </li>
                                                <li class="menu-item-has-children">
                                                    <a href="<?php echo e(route('certificates')); ?>">
                                                        الشهادات
                                                    </a>
                                                </li>
                                                <li class="menu-item-has-children">
                                                    <a href="#">
                                                        عن المؤسسة <i class="fa fa-plus"></i>
                                                    </a>
                                                    <ul class="sub-menu">
                                                        <li>
                                                            <a href="<?php echo e(route('show_all_coache')); ?>">
                                                                المدربين
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="<?php echo e(route('founder',1)); ?>">
                                                                المؤسسين
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </li>
                                                <li class="menu-item-has-children">
                                                    <a href="#OurServices">
                                                        خدماتنا
                                                    </a>
                                                </li>
                                                <li class="menu-item-has-children">
                                                    <a href="#">
                                                         دوراتنا <i class="fa fa-plus"></i>
                                                    </a>
                                                    <ul class="sub-menu">
                                                        <?php if(isset($categorys) && $categorys->count() > 0): ?>
                                                            <?php $__currentLoopData = $categorys; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <li>
                                                                <a href="<?php echo e(route('Show_Course',$category->id)); ?>">
                                                                    <?php echo e($category->name); ?>

                                                                </a>
                                                            </li>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        <?php else: ?>
                                                            <li>
                                                                <a href="#">لا توجد فئات متاحة</a>
                                                            </li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </li>
                                                <li class="menu-item-has-children">
                                                    <a href="#LatestCourses">
                                                        أحدث الدورات
                                                    </a>
                                                </li>
                                                <li class="menu-item-has-children">
                                                    <a href="<?php echo e(route('home')); ?>">
                                                        الرئيسية
                                                    </a>
                                                </li>
                                            </ul>
                                            <!-- //.nav-menu -->
                                        </nav>
                                    </div>
                                    <!-- //.main-menu -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Menu End -->
            </header>
            <!--Header End-->
        </div><?php /**PATH C:\xampp\htdocs\Academy\resources\views/layouts/home/<USER>/_header.blade.php ENDPATH**/ ?>