<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Category;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Intervention\Image\Facades\Image;

class CourseController extends Controller
{
    public function __construct()
    {
        //create read update delete
        $this->middleware(['permission:courses_read'])->only('index');
        $this->middleware(['permission:courses_create'])->only('create');
        $this->middleware(['permission:courses_update'])->only('edit');
        $this->middleware(['permission:courses_delete'])->only('destroy');

    } //end of constructor

    public function index()
    {
        $courses = Course::with(['instructors', 'category'])
            ->whenSearch(request()->search)
            ->orderBy('id', 'DESC')
            ->paginate(10);

        return view('dashboard.course.index',compact('courses'));
    }//end of index


    public function create()
    {
        $categories = Category::all();
        // استخراج المدربين من حقل jobs
        $instructors = User::where('jobs', 'مدرب')->get();

        \Log::info('المدربين المتاحين في النظام', ['instructors_count' => $instructors->count(), 'instructors' => $instructors->pluck('name', 'id')]);

        return view('dashboard.course.create', compact('categories', 'instructors'));
    }//end of create


    public function store(Request $request)
    {

        $request->validate([
            'name'              => 'required|string|max:255',
            'url'               => 'nullable|url',
            'Short_description' => 'required|string|max:500',
            'description'       => 'required|string',
            'course_objectives' => 'nullable|string',
            'target_audience'   => 'nullable|string',
            'rating'            => 'required|integer|min:1|max:5',
            'categories_id'     => 'required|exists:categories,id',
            'image'             => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'price'             => 'required|numeric|min:0',
            'show_price'        => 'nullable|boolean',
            'time'              => 'required|integer|min:1',
            'demo_video'        => 'nullable|mimes:mp4,avi,mov,wmv|max:50000',
            'instructors'       => 'nullable|array',
            'instructors.*'     => 'exists:users,id',
        ]);

        try {
            // تسجيل بداية العملية
            \Log::info('بداية إنشاء دورة جديدة', [
                'user_id' => auth()->id(),
                'course_name' => $request->name,
                'request_data' => $request->except(['image', 'demo_video'])
            ]);

            $request_data = $request->except(['image','demo_video', 'instructors']);

            // معالجة حقل إظهار السعر
            $request_data['show_price'] = $request->has('show_price') ? true : false;

            \Log::info('بيانات الدورة بعد المعالجة', ['request_data' => $request_data]);

            // معالجة الصورة (اختيارية)
            if ($request->hasFile('image')) {
                \Log::info('معالجة صورة الدورة');

                try {
                    Image::make($request->image)
                        ->resize(300, null, function ($constraint) {
                            $constraint->aspectRatio();
                        })
                        ->save(public_path('uploads/course_images/' . $request->image->hashName()));

                    $request_data['image'] = $request->image->hashName();
                    \Log::info('تم حفظ الصورة بنجاح', ['image_name' => $request->image->hashName()]);
                } catch (\Exception $e) {
                    \Log::error('خطأ في معالجة الصورة', ['error' => $e->getMessage()]);
                    // لا نوقف العملية، نكمل بدون صورة
                }
            } else {
                \Log::info('لم يتم رفع صورة للدورة');
                // يمكن وضع صورة افتراضية
                $request_data['image'] = 'default-course.jpg';
            }

            // معالجة الفيديو التعريفي (اختياري)
            if ($request->hasFile('demo_video')) {
                \Log::info('معالجة فيديو الدورة');
                try {
                    $request_data['demo_video'] = $request->file('demo_video')->store('courses_video');
                    \Log::info('تم حفظ الفيديو بنجاح', ['video_path' => $request_data['demo_video']]);
                } catch (\Exception $e) {
                    \Log::error('خطأ في معالجة الفيديو', ['error' => $e->getMessage()]);
                    // لا نوقف العملية، نكمل بدون فيديو
                }
            } else {
                \Log::info('لم يتم رفع فيديو للدورة');
                // إضافة قيمة افتراضية للفيديو
                $request_data['demo_video'] = null;
            }

            // التأكد من وجود قيمة studant_count
            if (!isset($request_data['studant_count'])) {
                $request_data['studant_count'] = '0';
            }

            \Log::info('محاولة حفظ الدورة في قاعدة البيانات', ['final_data' => $request_data]);

            // إنشاء الدورة
            $course = Course::create($request_data);

            \Log::info('تم إنشاء الدورة بنجاح', ['course_id' => $course->id, 'course_name' => $course->name]);

            // ربط المدربين بالدورة
            if ($request->has('instructors') && is_array($request->instructors)) {
                \Log::info('ربط المدربين بالدورة', ['instructors' => $request->instructors]);

                // التحقق من أن المدربين المختارين هم فعلاً مدربين
                $validInstructors = User::whereIn('id', $request->instructors)
                    ->where('jobs', 'مدرب')
                    ->pluck('id')
                    ->toArray();

                \Log::info('المدربين الصالحين', ['valid_instructors' => $validInstructors]);

                if (!empty($validInstructors)) {
                    $course->instructors()->attach($validInstructors);
                    \Log::info('تم ربط المدربين بنجاح');
                } else {
                    \Log::warning('لا توجد مدربين صالحين للربط');
                }
            } else {
                \Log::info('لم يتم اختيار مدربين للدورة');
            }

            \Log::info('تم إنشاء الدورة بالكامل بنجاح', ['course_id' => $course->id]);

            session()->flash('success', __('home.added_successfully'));
            return redirect()->route('dashboard.courses.index');

         } catch (\Exception $e) {
            \Log::error('خطأ في إنشاء الدورة', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->withErrors(['error' => 'حدث خطأ في إنشاء الدورة: ' . $e->getMessage()]);

        }//end try

    }//endof store


    public function edit(Course $course)
    {
        $categories = Category::all();
        // استخراج المدربين من حقل jobs
        $instructors = User::where('jobs', 'مدرب')->get();

        return view('dashboard.course.edit', compact('course', 'categories', 'instructors'));
    }//end ofedit


    public function update(Request $request, Course $course)
    {
        $request->validate([
            'name'              => 'required|string|max:255',
            'url'               => 'nullable|url',
            'Short_description' => 'required|string|max:500',
            'description'       => 'required|string',
            'course_objectives' => 'nullable|string',
            'target_audience'   => 'nullable|string',
            'rating'            => 'required|integer|min:1|max:5',
            'categories_id'     => 'required|exists:categories,id',
            'image'             => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'price'             => 'required|numeric|min:0',
            'show_price'        => 'nullable|boolean',
            'time'              => 'required|integer|min:1',
            'demo_video'        => 'nullable|mimes:mp4,avi,mov,wmv|max:50000',
            'instructors'       => 'nullable|array',
            'instructors.*'     => 'exists:users,id',
        ]);

        try {

            $request_data = $request->except(['image','demo_video', 'instructors']);

            // معالجة حقل إظهار السعر
            $request_data['show_price'] = $request->has('show_price') ? true : false;

            if ($request->image) {

                if ($course->image != 'default.png') {

                    Storage::disk('public_uploads')->delete('/course_images/' . $course->image);

                }//end of if

                Image::make($request->image)
                    ->resize(300, null, function ($constraint) {
                        $constraint->aspectRatio();
                    })
                    ->save(public_path('uploads/course_images/' . $request->image->hashName()));

                $request_data['image'] = $request->image->hashName();

            }//end of if

            if ($request->demo_video) {

                Storage::disk('public_uploads')->delete($course->demo_video);

                $request_data['demo_video'] = $request->file('demo_video')->store('courses_video');

            }//end of if

            // تحديث بيانات الدورة
            $course->update($request_data);

            // تحديث المدربين
            if ($request->has('instructors')) {
                // التحقق من أن المدربين المختارين هم فعلاً مدربين
                $validInstructors = User::whereIn('id', $request->instructors)
                    ->whereHas('roles', function($query) {
                        $query->where('name', 'instructor');
                    })
                    ->pluck('id')
                    ->toArray();

                // مزامنة المدربين (إزالة القدامى وإضافة الجدد)
                $course->instructors()->sync($validInstructors);
            } else {
                // إذا لم يتم اختيار أي مدربين، إزالة جميع المدربين
                $course->instructors()->detach();
            }

            session()->flash('success', __('home.updated_successfully'));
            return redirect()->route('dashboard.courses.index');

         } catch (\Exception $e) {

            return redirect()->back()->withErrors(['error' => $e->getMessage()]);

        }//end try

    }//end of update


    public function destroy(Course $course)
    {
        if ($course->image != 'default.png') {

            Storage::disk('public_uploads')->delete('/course_images/' . $course->image);

        }//end of if

        Storage::disk('public_uploads')->delete($course->demo_video);

        $course->delete();
        session()->flash('success', __('home.deleted_successfully'));
        return redirect()->route('dashboard.courses.index');

    }//end of destroy

    /**
     * عرض تفاصيل الدورة للـ AJAX
     */
    public function details(Course $course)
    {
        try {
            // تحضير البيانات للعرض
            $courseData = [
                'id' => $course->id,
                'title' => $course->title,
                'description' => $course->description,
                'short_description' => $course->Short_description,
                'price' => $course->price,
                'rating' => $course->rating,
                'image' => $course->image ? asset('uploads/course_images/' . $course->image) : null,
                'demo_video' => $course->demo_video,
                'duration' => $course->duration ?? 'غير محدد',
                'level' => $course->level ?? 'جميع المستويات',
                'language' => $course->language ?? 'العربية',
                'category' => $course->category ? $course->category->name : 'غير محدد',
                'created_at' => $course->created_at->format('Y-m-d H:i'),
                'updated_at' => $course->updated_at->format('Y-m-d H:i'),
                'students_count' => $course->purchases ? $course->purchases->count() : 0,
                'status' => $course->status ?? 'نشط'
            ];

            return response()->json([
                'success' => true,
                'course' => $courseData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في تحميل تفاصيل الدورة: ' . $e->getMessage()
            ], 500);
        }
    }//end of details

    /**
     * عرض تفاصيل الدورة في صفحة منفصلة
     */
    public function show(Course $course)
    {
        try {
            // تمرير الدورة مباشرة للعرض
            return view('dashboard.course.show', compact('course'));

        } catch (\Exception $e) {
            session()->flash('error', 'حدث خطأ في تحميل تفاصيل الدورة: ' . $e->getMessage());
            return redirect()->route('dashboard.courses.index');
        }
    }//end of show

    /**
     * إدارة مدربي الدورة
     */
    public function manageInstructors(Course $course)
    {
        $availableInstructors = User::whereHas('roles', function($query) {
                $query->where('name', 'instructor');
            })
            ->whereNotIn('id', $course->instructors->pluck('id'))
            ->get();

        return view('dashboard.course.instructors', compact('course', 'availableInstructors'));
    }

    /**
     * إضافة مدرب للدورة
     */
    public function addInstructor(Request $request, Course $course)
    {
        $request->validate([
            'instructor_id' => 'required|exists:users,id'
        ]);

        $instructor = User::findOrFail($request->instructor_id);

        // التحقق من أن المستخدم مدرب
        if (!$instructor->hasRole('instructor')) {
            return redirect()->back()->with('error', 'المستخدم المحدد ليس مدرب');
        }

        // التحقق من عدم وجود المدرب مسبقاً
        if ($course->instructors()->where('user_id', $instructor->id)->exists()) {
            return redirect()->back()->with('error', 'هذا المدرب مضاف بالفعل للدورة');
        }

        // إضافة المدرب
        $course->instructors()->attach($instructor->id);

        return redirect()->back()->with('success', 'تم إضافة المدرب بنجاح');
    }

    /**
     * إزالة مدرب من الدورة
     */
    public function removeInstructor(Course $course, User $instructor)
    {
        // التحقق من وجود المدرب في الدورة
        if (!$course->instructors()->where('user_id', $instructor->id)->exists()) {
            return redirect()->back()->with('error', 'المدرب غير مضاف للدورة');
        }

        // إزالة المدرب
        $course->instructors()->detach($instructor->id);

        return redirect()->back()->with('success', 'تم إزالة المدرب بنجاح');
    }

}//end of CourseController
