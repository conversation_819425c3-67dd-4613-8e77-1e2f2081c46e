@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">
        <section class="content-header">
            <h1>إدارة اشتراكات الطلاب والدفعات</h1>
            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
                <li class="active">اشتراكات الطلاب</li>
            </ol>
        </section>

        <section class="content">

            <!-- إحصائيات سريعة -->
            <div class="row">
                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-aqua">
                        <div class="inner">
                            <h3>{{ \App\Models\Purchase::count() }}</h3>
                            <p>إجمالي الاشتراكات</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-users"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-green">
                        <div class="inner">
                            <h3>{{ number_format(\App\Models\Purchase::where('status', '1')->count() * 1000, 0) }}</h3>
                            <p>المبلغ المحصل (دج)</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-money"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-yellow">
                        <div class="inner">
                            <h3>{{ number_format(\App\Models\Purchase::where('status', '0')->count() * 1000, 0) }}</h3>
                            <p>المبلغ المتبقي (دج)</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-clock-o"></i>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-red">
                        <div class="inner">
                            <h3>{{ \App\Models\Purchase::where('status', '0')->where('created_at', '<', now()->subDays(7))->count() }}</h3>
                            <p>دفعات متأخرة</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-warning"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات السريعة -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">إجراءات سريعة</h3>
                        </div>
                        <div class="box-body">
                            <a href="{{ route('dashboard.purchases.create') }}" class="btn btn-primary">
                                <i class="fa fa-plus"></i> إضافة اشتراك جديد
                            </a>
                            <a href="{{ route('dashboard.simple-payment-tracking') }}" class="btn btn-info">
                                <i class="fa fa-dashboard"></i> متابعة الدفعات
                            </a>
                            <a href="{{ route('dashboard.purchases.index') }}" class="btn btn-success">
                                <i class="fa fa-list"></i> جميع الاشتراكات
                            </a>
                            <a href="{{ route('dashboard.courses.index') }}" class="btn btn-warning">
                                <i class="fa fa-graduation-cap"></i> إدارة الدورات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">البحث والفلترة</h3>
                        </div>
                        <div class="box-body">
                            <form method="GET" action="{{ route('dashboard.purchases.index') }}">
                                <div class="row">
                                    <div class="col-md-3">
                                        <input type="text" name="search" class="form-control"
                                               placeholder="البحث..." value="{{ request('search') }}">
                                    </div>
                                    <div class="col-md-2">
                                        <select name="status" class="form-control">
                                            <option value="">جميع الحالات</option>
                                            <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>في الانتظار</option>
                                            <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>مقبول</option>
                                            <option value="2" {{ request('status') == '2' ? 'selected' : '' }}>مرفوض</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select name="course_id" class="form-control">
                                            <option value="">جميع الدورات</option>
                                            @foreach(\App\Models\Course::all() as $course)
                                                <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                                    {{ $course->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الدفعات -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box">
                        <div class="box-header">
                            <h3 class="box-title">قائمة اشتراكات الطلاب</h3>
                        </div>
                        <div class="box-body table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الطالب</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الدورة</th>
                                        <th>المبلغ المتوقع</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                        $purchases = \App\Models\Purchase::with('course')->latest()->paginate(15);
                                    @endphp
                                    @forelse($purchases as $purchase)
                                        <tr class="{{ $purchase->status == '0' && $purchase->created_at->diffInDays() > 7 ? 'danger' : '' }}">
                                            <td>{{ $purchase->id }}</td>
                                            <td>
                                                <strong>{{ $purchase->first_name }} {{ $purchase->last_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $purchase->phone }}</small>
                                            </td>
                                            <td>{{ $purchase->email }}</td>
                                            <td>
                                                <span class="label label-info">{{ $purchase->name_course }}</span>
                                            </td>
                                            <td>1,000 دج</td>
                                            <td>
                                                @if($purchase->status == '1')
                                                    <span class="label label-success">مقبول</span>
                                                @elseif($purchase->status == '0')
                                                    <span class="label label-warning">في الانتظار</span>
                                                    @if($purchase->created_at->diffInDays() > 7)
                                                        <br><small class="text-danger">متأخر {{ $purchase->created_at->diffInDays() }} يوم</small>
                                                    @endif
                                                @else
                                                    <span class="label label-danger">مرفوض</span>
                                                @endif
                                            </td>
                                            <td>{{ $purchase->created_at->format('Y-m-d') }}</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown">
                                                        إجراءات <span class="caret"></span>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a href="{{ route('dashboard.purchases.show', $purchase) }}">
                                                                <i class="fa fa-eye"></i> عرض التفاصيل
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="{{ route('dashboard.purchases.edit', $purchase) }}">
                                                                <i class="fa fa-edit"></i> تعديل
                                                            </a>
                                                        </li>
                                                        @if($purchase->status == '0')
                                                        <li class="divider"></li>
                                                        <li>
                                                            <a href="#" onclick="updateStatus({{ $purchase->id }}, '1')" class="text-success">
                                                                <i class="fa fa-check"></i> قبول
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#" onclick="updateStatus({{ $purchase->id }}, '2')" class="text-danger">
                                                                <i class="fa fa-times"></i> رفض
                                                            </a>
                                                        </li>
                                                        @endif
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center">لا توجد اشتراكات</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                        <div class="box-footer">
                            {{ $purchases->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
            </div>

        </section>
    </div>

@endsection

@push('scripts')
<script>
function updateStatus(id, status) {
    if (confirm('هل أنت متأكد من تغيير حالة هذا الاشتراك؟')) {
        // يمكن إضافة AJAX request هنا لتحديث الحالة
        alert('سيتم تطوير هذه الميزة قريباً');
    }
}

$(document).ready(function() {
    // تحديث تلقائي للصفحة كل 5 دقائق
    setTimeout(function() {
        location.reload();
    }, 300000);
});
</script>
@endpush
