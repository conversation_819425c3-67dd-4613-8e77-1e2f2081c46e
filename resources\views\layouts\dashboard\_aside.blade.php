<aside class="main-sidebar">

    <section class="sidebar">

        <div class="user-panel">
            <div class="pull-left image">
                <img src="{{ auth()->user()->image_path }}" class="img-circle" alt="User Image">
            </div>
            <div class="pull-left info">
                <p>{{ auth()->user()->name }}</p>
            </div>
        </div>

        <ul class="sidebar-menu" data-widget="tree">
            <li><a href="{{ route('dashboard.welcome') }}"><i class="sidebar-icon icon-dashboard"></i><span>@lang('dashboard.dashboard')</span></a></li>

            @if (auth()->user()->hasPermission('users_read'))
                <li><a href="{{ route('dashboard.users.index') }}"><i class="sidebar-icon icon-users"></i><span>@lang('dashboard.users')</span></a></li>
            @endif

            @if (auth()->user()->hasPermission('categories_read'))
                <li><a href="{{ route('dashboard.categories.index') }}"><i class="sidebar-icon icon-categories"></i><span>@lang('dashboard.categories')</span></a></li>
            @endif

            @if (auth()->user()->hasPermission('courses_read'))
                <li><a href="{{ route('dashboard.courses.index') }}"><i class="sidebar-icon icon-courses"></i><span>الدورات</span></a></li>
            @endif

            @if (auth()->user()->hasPermission('certificates_read'))
                <li><a href="{{ route('dashboard.certificates.index') }}"><i class="sidebar-icon icon-certificates"></i><span>@lang('home.certificates')</span></a></li>
            @endif

            {{-- @if (auth()->user()->hasPermission('purchases_read')) --}}
                <li><a href="{{ route('dashboard.purchases.index') }}"><i class="sidebar-icon icon-purchases"></i><span>طلبات الدورات</span></a></li>
            {{-- @endif --}}

            {{-- روابط الدفعات والاشتراكات --}}
            <li><a href="{{ route('dashboard.payments.simple') }}"><i class="sidebar-icon icon-payments"></i><span>اشتراكات الطلاب</span></a></li>
            <li><a href="{{ route('dashboard.subscriptions') }}"><i class="sidebar-icon icon-subscriptions"></i><span>متابعة الاشتراكات</span></a></li>
            <li><a href="{{ route('dashboard.simple-payment-tracking') }}"><i class="sidebar-icon icon-payment-tracking"></i><span>متابعة دفعات المشتركين</span></a></li>

            @if (auth()->user()->hasPermission('coaches_read'))
                <li><a href="{{ route('dashboard.coaches.index') }}"><i class="sidebar-icon icon-coaches"></i><span>المدربين</span></a></li>
            @endif

            @if (auth()->user()->hasPermission('posts_read'))
                <li><a href="{{ route('dashboard.posts.index') }}"><i class="sidebar-icon icon-posts"></i><span> @lang('dashboard.posts')</span></a></li>
            @endif

            @if (auth()->user()->hasPermission('advisoryServices_read'))
                <li><a href="{{ route('dashboard.advisoryServices.index') }}"><i class="sidebar-icon icon-advisory"></i><span> @lang('dashboard.advisoryServices')</span></a></li>
            @endif

            @if (auth()->user()->hasPermission('settings_read'))
                <li class="treeview" style="height: auto;">
                  <a href="#">
                    <i class="sidebar-icon icon-settings"></i> <span>الاعدادات</span>
                    <span class="pull-right-container">
                      <i class="fa fa-angle-left pull-right"></i>
                    </span>
                  </a>
                  <ul class="treeview-menu" style="display: none;">
                    <li><a href="{{ route('dashboard.institution.index') }}"><i class="sidebar-icon icon-institution"></i> معلومات المؤسسة</a></li>
                    <li><a href="{{ route('dashboard.about_index') }}"><i class="fa fa-info-circle"></i> عن الاكادميه</a></li>
                    <li><a href="{{ route('dashboard.links_index') }}"><i class="fa fa-link"></i> روابط التواصل</a></li>
                    <li><a href="{{ route('dashboard.title_index') }}"><i class="fa fa-header"></i> العناوين</a></li>
                    <li><a href="{{ route('dashboard.founder',1) }}"><i class="sidebar-icon icon-founders"></i> المؤسس</a></li>
                  </ul>
                </li>
            @endif
        </ul>

    </section>

</aside>

