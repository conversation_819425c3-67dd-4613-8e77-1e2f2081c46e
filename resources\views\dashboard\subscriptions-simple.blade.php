@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">
        <section class="content-header">
            <h1>
                متابعة الاشتراكات - نسخة مبسطة
                <small>لوحة تحكم لمتابعة اشتراكات الطلاب</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
                <li class="active">متابعة الاشتراكات</li>
            </ol>
        </section>

        <section class="content">
            
            <!-- إحصائيات سريعة -->
            <div class="row">
                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-blue">
                        <div class="inner">
                            <h3>{{ \App\Models\Purchase::count() }}</h3>
                            <p>إجمالي الاشتراكات</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-users"></i>
                        </div>
                        <a href="{{ route('dashboard.purchases.index') }}" class="small-box-footer">
                            عرض التفاصيل <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-green">
                        <div class="inner">
                            <h3>{{ \App\Models\Purchase::where('status', '1')->count() }}</h3>
                            <p>اشتراكات مقبولة</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-check-circle"></i>
                        </div>
                        <a href="{{ route('dashboard.purchases.index') }}" class="small-box-footer">
                            عرض المقبولة <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-yellow">
                        <div class="inner">
                            <h3>{{ \App\Models\Purchase::where('status', '0')->count() }}</h3>
                            <p>في الانتظار</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-clock-o"></i>
                        </div>
                        <a href="{{ route('dashboard.purchases.index') }}" class="small-box-footer">
                            عرض المنتظرة <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-xs-6">
                    <div class="small-box bg-red">
                        <div class="inner">
                            <h3>{{ \App\Models\Purchase::where('status', '2')->count() }}</h3>
                            <p>مرفوضة</p>
                        </div>
                        <div class="icon">
                            <i class="fa fa-times-circle"></i>
                        </div>
                        <a href="{{ route('dashboard.purchases.index') }}" class="small-box-footer">
                            عرض المرفوضة <i class="fa fa-arrow-circle-left"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- الاشتراكات الحديثة -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-list"></i> الاشتراكات الحديثة
                            </h3>
                            <div class="box-tools pull-right">
                                <a href="{{ route('dashboard.purchases.index') }}" class="btn btn-primary btn-sm">
                                    عرض الكل
                                </a>
                            </div>
                        </div>
                        <div class="box-body table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الطالب</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الدورة</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach(\App\Models\Purchase::latest()->take(10)->get() as $purchase)
                                        <tr>
                                            <td>{{ $purchase->id }}</td>
                                            <td>
                                                <strong>{{ $purchase->first_name }} {{ $purchase->last_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $purchase->phone }}</small>
                                            </td>
                                            <td>{{ $purchase->email }}</td>
                                            <td>{{ $purchase->name_course }}</td>
                                            <td>
                                                @if($purchase->status == '1')
                                                    <span class="label label-success">مقبول</span>
                                                @elseif($purchase->status == '0')
                                                    <span class="label label-warning">في الانتظار</span>
                                                @else
                                                    <span class="label label-danger">مرفوض</span>
                                                @endif
                                            </td>
                                            <td>{{ $purchase->created_at->format('Y-m-d') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الاشتراكات حسب الدورة -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-graduation-cap"></i> الاشتراكات حسب الدورة
                            </h3>
                        </div>
                        <div class="box-body table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>الدورة</th>
                                        <th>إجمالي الاشتراكات</th>
                                        <th>مقبولة</th>
                                        <th>في الانتظار</th>
                                        <th>مرفوضة</th>
                                        <th>معدل القبول</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach(\App\Models\Course::withCount('purchases')->having('purchases_count', '>', 0)->get() as $course)
                                        @php
                                            $coursePurchases = \App\Models\Purchase::where('course_id', $course->id)->get();
                                            $accepted = $coursePurchases->where('status', '1')->count();
                                            $pending = $coursePurchases->where('status', '0')->count();
                                            $rejected = $coursePurchases->where('status', '2')->count();
                                            $total = $coursePurchases->count();
                                            $acceptanceRate = $total > 0 ? round(($accepted / $total) * 100, 1) : 0;
                                        @endphp
                                        <tr>
                                            <td>
                                                <strong>{{ $course->name }}</strong>
                                                <br>
                                                <small class="text-muted">#{{ $course->id }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-blue">{{ $total }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-green">{{ $accepted }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-yellow">{{ $pending }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-red">{{ $rejected }}</span>
                                            </td>
                                            <td>
                                                <div class="progress progress-xs">
                                                    <div class="progress-bar progress-bar-{{ $acceptanceRate >= 70 ? 'success' : ($acceptanceRate >= 40 ? 'warning' : 'danger') }}" 
                                                         style="width: {{ $acceptanceRate }}%"></div>
                                                </div>
                                                <span class="badge">{{ $acceptanceRate }}%</span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-rocket"></i> إجراءات سريعة
                            </h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="{{ route('dashboard.purchases.index') }}" class="btn btn-primary btn-block">
                                        <i class="fa fa-list"></i> جميع الاشتراكات
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ route('dashboard.courses.index') }}" class="btn btn-success btn-block">
                                        <i class="fa fa-graduation-cap"></i> إدارة الدورات
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ route('dashboard.users.index') }}" class="btn btn-info btn-block">
                                        <i class="fa fa-users"></i> إدارة المستخدمين
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ route('dashboard.welcome') }}" class="btn btn-default btn-block">
                                        <i class="fa fa-dashboard"></i> لوحة التحكم
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </section>
    </div>

@endsection

@push('styles')
<style>
.small-box .icon {
    font-size: 60px;
}

.progress-xs {
    height: 5px;
}

.badge {
    font-size: 11px;
}

.bg-blue {
    background-color: #3c8dbc !important;
}

.bg-green {
    background-color: #00a65a !important;
}

.bg-yellow {
    background-color: #f39c12 !important;
}

.bg-red {
    background-color: #dd4b39 !important;
}
</style>
@endpush
