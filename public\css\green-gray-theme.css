/* ===================================
   نموذج الأخضر والرمادي - أكاديمية Leaders Vision
   Green & Gray Theme - Leaders Vision Academy
   =================================== */

:root {
    /* الألوان الأساسية - الأخضر والرمادي */
    --primary-green: #4ade80;           /* أخضر فاتح */
    --primary-green-dark: #22c55e;      /* أخضر داكن */
    --primary-green-light: #86efac;     /* أخضر فاتح جداً */

    --primary-gray: #475569;            /* رمادي أساسي */
    --primary-gray-dark: #334155;       /* رمادي داكن */
    --primary-gray-light: #64748b;      /* رمادي فاتح */

    --background-dark: #1e293b;         /* خلفية داكنة */
    --background-light: #f1f5f9;        /* خلفية فاتحة */

    /* ألوان مساعدة */
    --accent-teal: #14b8a6;            /* تركوازي */
    --accent-emerald: #10b981;         /* زمردي */
    --text-white: #ffffff;
    --text-dark: #1e293b;

    /* تدرجات */
    --green-gradient: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
    --gray-gradient: linear-gradient(135deg, var(--primary-gray) 0%, var(--primary-gray-dark) 100%);
    --mixed-gradient: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-gray) 50%, var(--background-dark) 100%);
    --hero-gradient: linear-gradient(135deg, var(--background-dark) 0%, var(--primary-gray) 30%, var(--primary-green) 70%, var(--accent-teal) 100%);
}

/* ===================================
   الخلفيات الرئيسية
   =================================== */

/* خلفية البانر الرئيسي */
.rs-banner,
.banner-section,
.hero-section {
    background: var(--hero-gradient) !important;
    position: relative;
    overflow: hidden;
}

.rs-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagon" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,2 18,7 18,13 10,18 2,13 2,7" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagon)"/></svg>') repeat;
    opacity: 0.3;
    z-index: 1;
}

.rs-banner .container {
    position: relative;
    z-index: 2;
}

/* خلفية الأقسام */
.bg-leaders-vision {
    background: var(--mixed-gradient) !important;
    position: relative;
}

.bg-leaders-vision::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(74, 222, 128, 0.1) 50%, transparent 70%);
    z-index: 1;
}

/* ===================================
   الأزرار والعناصر التفاعلية
   =================================== */

.btn-primary,
.readon {
    background: var(--green-gradient) !important;
    border: none !important;
    color: var(--text-white) !important;
    padding: 12px 30px !important;
    border-radius: 25px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(74, 222, 128, 0.3) !important;
}

.btn-primary:hover,
.readon:hover {
    background: var(--primary-green-dark) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(74, 222, 128, 0.4) !important;
}

.btn-secondary {
    background: var(--gray-gradient) !important;
    border: 2px solid var(--primary-green) !important;
    color: var(--text-white) !important;
}

.btn-secondary:hover {
    background: var(--primary-green) !important;
    border-color: var(--primary-green-dark) !important;
}

/* ===================================
   البطاقات والصناديق
   =================================== */

.card,
.box,
.service-item,
.course-item {
    background: rgba(255, 255, 255, 0.95) !important;
    border: none !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
}

.card:hover,
.box:hover,
.service-item:hover,
.course-item:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 20px 40px rgba(74, 222, 128, 0.2) !important;
}

.card-header,
.box-header {
    background: var(--green-gradient) !important;
    color: var(--text-white) !important;
    border: none !important;
}

/* ===================================
   النصوص والعناوين
   =================================== */

.banner-title,
.main-title,
h1, h2, h3 {
    color: var(--text-white) !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.banner-subtitle,
.subtitle {
    color: var(--primary-green-light) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.text-primary {
    color: var(--primary-green) !important;
}

.text-secondary {
    color: var(--primary-gray) !important;
}

/* تحسين نص الخدمات */
.service-description p {
    color: #ffffff !important;
    font-weight: bold !important;
    font-size: 1.2rem !important;
    line-height: 1.8 !important;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5) !important;
    margin: 0 auto !important;
    max-width: 800px !important;
    padding: 20px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 15px !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(74, 222, 128, 0.2) !important;
}

.service-description p:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(74, 222, 128, 0.3) !important;
    transition: all 0.3s ease !important;
}

/* نص بني داكن عريض للعناوين المهمة */
.dark-brown-title {
    color: #8b4513 !important;
    font-weight: bold !important;
    font-size: 1.4rem !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1) !important;
    letter-spacing: 1px !important;
    margin-bottom: 15px !important;
}

.dark-brown-title:hover {
    color: #a0522d !important;
    transform: scale(1.02) !important;
    transition: all 0.3s ease !important;
}

/* ===================================
   الشريط العلوي والقوائم
   =================================== */

.header-area,
.main-header {
    background: rgba(30, 41, 59, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-bottom: 2px solid var(--primary-green) !important;
}

.main-menu ul li a {
    color: var(--text-white) !important;
    transition: all 0.3s ease !important;
}

.main-menu ul li a:hover,
.main-menu ul li.current-menu-item a {
    color: var(--primary-green) !important;
}

/* القائمة المنسدلة */
.main-menu ul li ul {
    background: var(--background-dark) !important;
    border: 1px solid var(--primary-green) !important;
    border-radius: 8px !important;
}

.main-menu ul li ul li a:hover {
    background: var(--primary-green) !important;
    color: var(--text-white) !important;
}

/* ===================================
   الفوتر
   =================================== */

.footer-area,
.rs-footer {
    background: var(--background-dark) !important;
    color: var(--text-white) !important;
}

.footer-title {
    color: var(--primary-green) !important;
    border-bottom: 2px solid var(--primary-green) !important;
    padding-bottom: 10px !important;
}

.footer-area a {
    color: var(--primary-gray-light) !important;
    transition: color 0.3s ease !important;
}

.footer-area a:hover {
    color: var(--primary-green) !important;
}

/* ===================================
   الإحصائيات والأرقام
   =================================== */

.counter-item,
.stats-item {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid var(--primary-green) !important;
    border-radius: 15px !important;
    padding: 30px !important;
    text-align: center !important;
    backdrop-filter: blur(10px) !important;
}

.counter-number,
.stats-number {
    color: var(--primary-green) !important;
    font-size: 3rem !important;
    font-weight: 700 !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.counter-title,
.stats-title {
    color: var(--text-white) !important;
    font-weight: 600 !important;
}

/* ===================================
   النماذج والمدخلات
   =================================== */

.form-control,
input,
textarea,
select {
    border: 2px solid var(--primary-gray-light) !important;
    border-radius: 8px !important;
    padding: 12px 15px !important;
    transition: all 0.3s ease !important;
}

.form-control:focus,
input:focus,
textarea:focus,
select:focus {
    border-color: var(--primary-green) !important;
    box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.2) !important;
    outline: none !important;
}

/* ===================================
   الأيقونات والرموز
   =================================== */

.icon,
.fa,
.service-icon {
    color: var(--primary-green) !important;
    transition: all 0.3s ease !important;
}

.icon:hover,
.fa:hover {
    color: var(--primary-green-dark) !important;
    transform: scale(1.1) !important;
}

/* ===================================
   الشعار والهوية
   =================================== */

.logo-image {
    filter: drop-shadow(0 4px 8px rgba(74, 222, 128, 0.3)) !important;
    transition: all 0.3s ease !important;
}

.logo-image:hover {
    filter: drop-shadow(0 6px 12px rgba(74, 222, 128, 0.5)) !important;
    transform: scale(1.05) !important;
}

/* ===================================
   التأثيرات والحركات
   =================================== */

@keyframes greenPulse {
    0% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(74, 222, 128, 0); }
    100% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0); }
}

.pulse-green {
    animation: greenPulse 2s infinite !important;
}

@keyframes floatGreen {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.float-animation {
    animation: floatGreen 3s ease-in-out infinite !important;
}

/* ===================================
   الخلفيات المتحركة
   =================================== */

.animated-bg {
    background: var(--hero-gradient) !important;
    background-size: 400% 400% !important;
    animation: gradientShift 15s ease infinite !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* ===================================
   التجاوب مع الشاشات
   =================================== */

@media (max-width: 768px) {
    .rs-banner {
        background: var(--mixed-gradient) !important;
    }

    .banner-title {
        font-size: 2rem !important;
    }

    .banner-subtitle {
        font-size: 1.2rem !important;
    }

    .counter-number {
        font-size: 2rem !important;
    }
}

/* ===================================
   تحسينات إضافية
   =================================== */

.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.green-shadow {
    box-shadow: 0 10px 30px rgba(74, 222, 128, 0.3) !important;
}

.gray-shadow {
    box-shadow: 0 10px 30px rgba(71, 85, 105, 0.3) !important;
}
