/**
 * تحسينات JavaScript للوحة التحكم
 * Dashboard JavaScript Enhancements
 * أكاديمية Leaders Vision - Leaders Vision Academy
 */

$(document).ready(function() {

    // تشغيل التحسينات
    initDashboardEnhancements();

    // تحسين الجداول
    enhanceTables();

    // تحسين النماذج
    enhanceForms();

    // تحسين الإحصائيات
    enhanceStatistics();

    // تحسين التنقل
    enhanceNavigation();

    // إضافة مؤشرات التحميل
    addLoadingIndicators();

    console.log('✅ تم تحميل تحسينات لوحة التحكم');
});

/**
 * تشغيل التحسينات الأساسية
 */
function initDashboardEnhancements() {

    // إضافة تأثيرات الحركة
    addAnimations();

    // تحسين الأزرار
    enhanceButtons();

    // تحسين التنبيهات
    enhanceAlerts();

    // تحسين القوائم المنسدلة
    enhanceDropdowns();
}

/**
 * إضافة تأثيرات الحركة
 */
function addAnimations() {

    // تأثير الظهور للبطاقات
    $('.box, .card').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateY(20px)'
        }).delay(index * 100).animate({
            'opacity': '1'
        }, 500).css('transform', 'translateY(0)');
    });

    // تأثير hover للصفوف
    $('.table tbody tr').hover(
        function() {
            $(this).addClass('table-hover-effect');
        },
        function() {
            $(this).removeClass('table-hover-effect');
        }
    );
}

/**
 * تحسين الأزرار
 */
function enhanceButtons() {

    // إضافة تأثير الموجة
    $('.btn').on('click', function(e) {
        const button = $(this);
        const ripple = $('<span class="btn-ripple"></span>');

        button.append(ripple);

        const x = e.pageX - button.offset().left;
        const y = e.pageY - button.offset().top;

        ripple.css({
            left: x,
            top: y
        }).addClass('animate');

        setTimeout(() => {
            ripple.remove();
        }, 600);
    });

    // تحسين أزرار الحذف
    $('.btn-danger').on('click', function() {
        $(this).html('<i class="fa fa-spinner fa-spin"></i> جاري الحذف...');
    });
}

/**
 * تحسين الجداول
 */
function enhanceTables() {

    // إضافة فرز للجداول
    $('.table').each(function() {
        const table = $(this);

        // إضافة أيقونات الفرز للعناوين
        table.find('th').each(function() {
            const th = $(this);
            if (!th.hasClass('no-sort')) {
                th.addClass('sortable').append(' <i class="fa fa-sort"></i>');

                th.on('click', function() {
                    sortTable(table, th.index());
                });
            }
        });
    });

    // إضافة البحث للجداول
    addTableSearch();

    // تحسين التصفح
    enhancePagination();
}

/**
 * فرز الجدول
 */
function sortTable(table, columnIndex) {
    const tbody = table.find('tbody');
    const rows = tbody.find('tr').toArray();

    rows.sort(function(a, b) {
        const aText = $(a).find('td').eq(columnIndex).text().trim();
        const bText = $(b).find('td').eq(columnIndex).text().trim();

        // التحقق من الأرقام
        if (!isNaN(aText) && !isNaN(bText)) {
            return parseFloat(aText) - parseFloat(bText);
        }

        return aText.localeCompare(bText, 'ar');
    });

    tbody.empty().append(rows);

    // تحديث أيقونة الفرز
    table.find('th .fa').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
    table.find('th').eq(columnIndex).find('.fa').removeClass('fa-sort').addClass('fa-sort-up');
}

/**
 * إضافة البحث للجداول
 */
function addTableSearch() {
    $('.table').each(function() {
        const table = $(this);
        const searchInput = $(`
            <div class="table-search mb-3">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="البحث في الجدول...">
                    <div class="input-group-append">
                        <span class="input-group-text"><i class="fa fa-search"></i></span>
                    </div>
                </div>
            </div>
        `);

        table.before(searchInput);

        searchInput.find('input').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();

            table.find('tbody tr').each(function() {
                const row = $(this);
                const text = row.text().toLowerCase();

                if (text.includes(searchTerm)) {
                    row.show();
                } else {
                    row.hide();
                }
            });
        });
    });
}

/**
 * تحسين النماذج
 */
function enhanceForms() {

    // إضافة التحقق المباشر
    $('input, textarea, select').on('blur', function() {
        validateField($(this));
    });

    // تحسين رفع الملفات
    enhanceFileUploads();

    // إضافة عدادات الأحرف
    addCharacterCounters();

    // تحسين التواريخ
    enhanceDatePickers();
}

/**
 * التحقق من الحقول
 */
function validateField(field) {
    const value = field.val().trim();
    const fieldType = field.attr('type') || field.prop('tagName').toLowerCase();

    // إزالة رسائل الخطأ السابقة
    field.removeClass('is-invalid is-valid');
    field.next('.invalid-feedback').remove();

    let isValid = true;
    let errorMessage = '';

    // التحقق من الحقول المطلوبة
    if (field.prop('required') && !value) {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب';
    }

    // التحقق حسب نوع الحقل
    if (value && isValid) {
        switch(fieldType) {
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'يرجى إدخال بريد إلكتروني صحيح';
                }
                break;

            case 'url':
                try {
                    new URL(value);
                } catch {
                    isValid = false;
                    errorMessage = 'يرجى إدخال رابط صحيح';
                }
                break;

            case 'number':
                if (isNaN(value)) {
                    isValid = false;
                    errorMessage = 'يرجى إدخال رقم صحيح';
                }
                break;
        }
    }

    // إضافة الفئات والرسائل المناسبة
    if (isValid && value) {
        field.addClass('is-valid');
    } else if (!isValid) {
        field.addClass('is-invalid');
        field.after(`<div class="invalid-feedback">${errorMessage}</div>`);
    }

    return isValid;
}

/**
 * تحسين رفع الملفات
 */
function enhanceFileUploads() {
    $('input[type="file"]').each(function() {
        const input = $(this);
        const wrapper = $(`
            <div class="file-upload-wrapper">
                <div class="file-upload-area">
                    <i class="fa fa-cloud-upload fa-3x"></i>
                    <p>اسحب الملفات هنا أو انقر للاختيار</p>
                    <div class="file-info"></div>
                </div>
            </div>
        `);

        input.wrap(wrapper);

        input.on('change', function() {
            const files = this.files;
            const fileInfo = input.siblings('.file-upload-area').find('.file-info');

            if (files.length > 0) {
                let info = '';
                for (let i = 0; i < files.length; i++) {
                    info += `<div class="file-item">${files[i].name}</div>`;
                }
                fileInfo.html(info);
            }
        });
    });
}

/**
 * إضافة عدادات الأحرف
 */
function addCharacterCounters() {
    $('textarea[maxlength]').each(function() {
        const textarea = $(this);
        const maxLength = textarea.attr('maxlength');
        const counter = $(`<div class="character-counter">0 / ${maxLength}</div>`);

        textarea.after(counter);

        textarea.on('input', function() {
            const currentLength = $(this).val().length;
            counter.text(`${currentLength} / ${maxLength}`);

            if (currentLength > maxLength * 0.9) {
                counter.addClass('text-warning');
            } else {
                counter.removeClass('text-warning');
            }
        });
    });
}

/**
 * تحسين الإحصائيات
 */
function enhanceStatistics() {

    // تأثير العد التصاعدي
    $('.info-box-number').each(function() {
        const element = $(this);
        const target = parseInt(element.text());

        if (!isNaN(target)) {
            animateCounter(element, target);
        }
    });

    // إضافة الرسوم البيانية
    addCharts();
}

/**
 * تأثير العد التصاعدي
 */
function animateCounter(element, target) {
    const increment = target / 100;
    let current = 0;

    const timer = setInterval(() => {
        current += increment;
        element.text(Math.floor(current));

        if (current >= target) {
            element.text(target);
            clearInterval(timer);
        }
    }, 20);
}

/**
 * تحسين التنقل
 */
function enhanceNavigation() {

    // تحسين القائمة الجانبية
    $('.sidebar-menu li').on('click', function() {
        $(this).addClass('menu-loading');

        setTimeout(() => {
            $(this).removeClass('menu-loading');
        }, 1000);
    });

    // إضافة breadcrumbs
    addBreadcrumbs();
}

/**
 * إضافة مؤشرات التحميل
 */
function addLoadingIndicators() {

    // مؤشر التحميل للنماذج
    $('form').on('submit', function() {
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');

        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fa fa-spinner fa-spin"></i> جاري المعالجة...');
    });

    // مؤشر التحميل للروابط
    $('a[href]:not([href^="#"])').on('click', function() {
        const link = $(this);
        if (!link.hasClass('no-loading')) {
            link.append(' <i class="fa fa-spinner fa-spin"></i>');
        }
    });
}

/**
 * تحسين التنبيهات
 */
function enhanceAlerts() {

    // إضافة إغلاق تلقائي للتنبيهات
    $('.alert').each(function() {
        const alert = $(this);

        if (!alert.hasClass('alert-permanent')) {
            setTimeout(() => {
                alert.fadeOut();
            }, 5000);
        }
    });
}

/**
 * تحسين القوائم المنسدلة
 */
function enhanceDropdowns() {

    // إضافة البحث للقوائم المنسدلة
    $('select').each(function() {
        const select = $(this);

        if (select.find('option').length > 5) {
            // يمكن إضافة مكتبة Select2 هنا
            console.log('قائمة منسدلة كبيرة تحتاج تحسين:', select);
        }
    });
}

/**
 * إضافة الرسوم البيانية
 */
function addCharts() {
    // يمكن إضافة Chart.js أو مكتبة رسوم بيانية أخرى هنا
    console.log('إضافة الرسوم البيانية');
}

/**
 * إضافة breadcrumbs
 */
function addBreadcrumbs() {
    const currentPath = window.location.pathname;
    const pathParts = currentPath.split('/').filter(part => part);

    if (pathParts.length > 1) {
        let breadcrumbs = '<ol class="breadcrumb">';
        breadcrumbs += '<li><a href="/dashboard"><i class="fa fa-dashboard"></i> الرئيسية</a></li>';

        for (let i = 1; i < pathParts.length; i++) {
            const part = pathParts[i];
            const isLast = i === pathParts.length - 1;

            if (isLast) {
                breadcrumbs += `<li class="active">${part}</li>`;
            } else {
                breadcrumbs += `<li><a href="/${pathParts.slice(0, i + 1).join('/')}">${part}</a></li>`;
            }
        }

        breadcrumbs += '</ol>';

        $('.content-header').prepend(breadcrumbs);
    }
}

// إضافة أنماط CSS مخصصة
const customStyles = `
<style>
.btn-ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.table-hover-effect {
    background-color: rgba(38, 199, 249, 0.1) !important;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

.file-upload-wrapper {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.file-upload-wrapper:hover {
    border-color: #26c7f9;
    background-color: rgba(38, 199, 249, 0.05);
}

.character-counter {
    font-size: 12px;
    color: #666;
    text-align: left;
    margin-top: 5px;
}

.menu-loading {
    opacity: 0.7;
}

.menu-loading:after {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #26c7f9;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
</style>
`;

// إضافة الأنماط المخصصة
$('head').append(customStyles);
