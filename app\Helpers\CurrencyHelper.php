<?php

namespace App\Helpers;

use App\Models\Institution;

class CurrencyHelper
{
    /**
     * Format price with Algerian Dinar
     */
    public static function formatPrice($amount, $showSymbol = true)
    {
        $institution = Institution::first();
        $symbol = $institution ? $institution->currency_symbol : 'د.ج';
        
        $formatted = number_format($amount, 2, '.', ',');
        
        return $showSymbol ? $formatted . ' ' . $symbol : $formatted;
    }

    /**
     * Get currency symbol
     */
    public static function getCurrencySymbol()
    {
        $institution = Institution::first();
        return $institution ? $institution->currency_symbol : 'د.ج';
    }

    /**
     * Get currency code
     */
    public static function getCurrencyCode()
    {
        $institution = Institution::first();
        return $institution ? $institution->currency : 'DZD';
    }

    /**
     * Get currency name in Arabic
     */
    public static function getCurrencyNameAr()
    {
        $institution = Institution::first();
        return $institution ? $institution->currency_name_ar : 'دينار جزائري';
    }

    /**
     * Get currency name in English
     */
    public static function getCurrencyNameEn()
    {
        $institution = Institution::first();
        return $institution ? $institution->currency_name_en : 'Algerian Dinar';
    }

    /**
     * Convert amount to words in Arabic
     */
    public static function amountToWordsAr($amount)
    {
        $ones = [
            '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
            'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر',
            'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
        ];

        $tens = [
            '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
        ];

        $hundreds = [
            '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
        ];

        if ($amount == 0) return 'صفر';

        $result = '';
        
        // Handle thousands
        if ($amount >= 1000) {
            $thousands = intval($amount / 1000);
            if ($thousands == 1) {
                $result .= 'ألف ';
            } elseif ($thousands == 2) {
                $result .= 'ألفان ';
            } elseif ($thousands <= 10) {
                $result .= $ones[$thousands] . ' آلاف ';
            } else {
                $result .= self::convertHundreds($thousands) . ' ألف ';
            }
            $amount = $amount % 1000;
        }

        // Handle hundreds, tens, and ones
        if ($amount > 0) {
            $result .= self::convertHundreds($amount);
        }

        return trim($result) . ' ' . self::getCurrencyNameAr();
    }

    private static function convertHundreds($number)
    {
        $ones = [
            '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
            'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر',
            'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
        ];

        $tens = [
            '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
        ];

        $hundreds = [
            '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
        ];

        $result = '';

        if ($number >= 100) {
            $result .= $hundreds[intval($number / 100)] . ' ';
            $number = $number % 100;
        }

        if ($number >= 20) {
            $result .= $tens[intval($number / 10)];
            if ($number % 10 > 0) {
                $result .= ' ' . $ones[$number % 10];
            }
        } elseif ($number > 0) {
            $result .= $ones[$number];
        }

        return trim($result);
    }

    /**
     * Get price ranges for filters
     */
    public static function getPriceRanges()
    {
        return [
            ['min' => 0, 'max' => 5000, 'label' => 'أقل من 5,000 د.ج'],
            ['min' => 5000, 'max' => 10000, 'label' => '5,000 - 10,000 د.ج'],
            ['min' => 10000, 'max' => 20000, 'label' => '10,000 - 20,000 د.ج'],
            ['min' => 20000, 'max' => 50000, 'label' => '20,000 - 50,000 د.ج'],
            ['min' => 50000, 'max' => 100000, 'label' => '50,000 - 100,000 د.ج'],
            ['min' => 100000, 'max' => null, 'label' => 'أكثر من 100,000 د.ج'],
        ];
    }
}
