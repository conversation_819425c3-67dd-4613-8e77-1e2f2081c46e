# نظام متابعة اشتراكات الطلاب والدفعات
## Student Payments and Subscriptions Tracking System

---

## 🎯 **نظرة عامة**

تم إنشاء نظام شامل لمتابعة اشتراكات الطلاب والدفعات يتضمن:
- **متابعة الدفعات الرئيسية** للطلاب
- **نظام الدفعات المتعددة** (التقسيط)
- **تقارير مفصلة** للدفعات المتأخرة والمستحقة
- **إحصائيات شاملة** للإيرادات والمدفوعات

---

## 📊 **هيكل قاعدة البيانات**

### **1. جدول الدفعات الرئيسي (student_payments):**

#### **معلو<PERSON><PERSON><PERSON> الطالب:**
- ✅ `student_name` - ا<PERSON><PERSON> الطالب
- ✅ `student_email` - بريد الطالب
- ✅ `student_phone` - هاتف الطالب

#### **معلومات الدورة:**
- ✅ `course_id` - معرف الدورة
- ✅ `course_name` - اسم الدورة

#### **معلومات الدفع:**
- ✅ `total_amount` - المبلغ الإجمالي
- ✅ `paid_amount` - المبلغ المدفوع
- ✅ `remaining_amount` - المبلغ المتبقي

#### **حالة الدفع:**
- ✅ `payment_status` - (pending, partial, completed, cancelled, refunded)

#### **تواريخ مهمة:**
- ✅ `enrollment_date` - تاريخ التسجيل
- ✅ `due_date` - تاريخ الاستحقاق
- ✅ `completion_date` - تاريخ إكمال الدفع

#### **معلومات إضافية:**
- ✅ `notes` - ملاحظات
- ✅ `payment_method` - طريقة الدفع
- ✅ `reference_number` - رقم المرجع
- ✅ `purchase_id` - ربط مع جدول المشتريات الأصلي

### **2. جدول الدفعات المتعددة (payment_installments):**

#### **معلومات الدفعة:**
- ✅ `student_payment_id` - ربط مع الدفع الرئيسي
- ✅ `installment_number` - رقم الدفعة
- ✅ `amount` - مبلغ الدفعة
- ✅ `due_date` - تاريخ الاستحقاق
- ✅ `paid_date` - تاريخ الدفع الفعلي

#### **حالة الدفعة:**
- ✅ `status` - (pending, paid, overdue, cancelled)

#### **معلومات الدفع:**
- ✅ `payment_method` - طريقة الدفع
- ✅ `transaction_id` - رقم المعاملة
- ✅ `receipt_image` - صورة الإيصال
- ✅ `notes` - ملاحظات

---

## 🛠️ **الملفات المنشأة**

### **1. Migrations:**
- ✅ `2024_12_15_120000_create_student_payments_table.php`
- ✅ `2024_12_15_120001_create_payment_installments_table.php`

### **2. Models:**
- ✅ `app/Models/StudentPayment.php`
- ✅ `app/Models/PaymentInstallment.php`
- ✅ تحديث `app/Models/Course.php` (إضافة العلاقات)

### **3. Controller:**
- ✅ `app/Http/Controllers/Dashboard/StudentPaymentController.php`

### **4. Routes:**
- ✅ تحديث `routes/dashboard/web.php`

### **5. Views:**
- ✅ `resources/views/dashboard/payments/index.blade.php`

---

## 🎯 **المميزات الرئيسية**

### **1. إدارة الدفعات:**
- ✅ **إنشاء اشتراك جديد** للطالب
- ✅ **متابعة حالة الدفع** (في الانتظار، جزئي، مكتمل)
- ✅ **حساب المبلغ المتبقي** تلقائياً
- ✅ **تسجيل الدفعات** مع تحديث الحالة

### **2. نظام التقسيط:**
- ✅ **إنشاء خطة دفعات** متعددة
- ✅ **متابعة كل دفعة** على حدة
- ✅ **تسجيل دفع الأقساط** مع الإيصالات
- ✅ **تحديث الدفع الرئيسي** تلقائياً

### **3. التقارير والإحصائيات:**
- ✅ **الدفعات المتأخرة** مع عدد الأيام
- ✅ **الدفعات المستحقة اليوم**
- ✅ **إحصائيات الإيرادات** الشهرية والإجمالية
- ✅ **معدل إكمال الدفعات**

### **4. البحث والفلترة:**
- ✅ **البحث بالاسم** أو البريد أو الهاتف
- ✅ **فلترة حسب الحالة** (pending, partial, completed)
- ✅ **فلترة حسب الدورة**
- ✅ **عرض المتأخرة فقط**

---

## 📋 **الصفحات والوظائف**

### **1. الصفحة الرئيسية (/dashboard/payments):**
- ✅ **إحصائيات سريعة** - إجمالي الاشتراكات، المحصل، المتبقي، المتأخر
- ✅ **أزرار إجراءات سريعة** - إضافة، متأخرة، مستحقة اليوم، إحصائيات
- ✅ **فلاتر البحث** - نص، حالة، دورة، متأخرة فقط
- ✅ **جدول الدفعات** - مع شريط التقدم وحالة كل دفعة

### **2. صفحة التفاصيل (/dashboard/payments/{id}):**
- ✅ **معلومات الطالب** كاملة
- ✅ **تفاصيل الدورة** والمبالغ
- ✅ **قائمة الدفعات** (إذا كان هناك تقسيط)
- ✅ **تسجيل دفعة جديدة**

### **3. صفحة الإنشاء (/dashboard/payments/create):**
- ✅ **معلومات الطالب** (اسم، بريد، هاتف)
- ✅ **اختيار الدورة** من القائمة
- ✅ **تحديد المبلغ** وطريقة الدفع
- ✅ **إنشاء خطة تقسيط** (اختياري)

### **4. تقرير المتأخرة (/dashboard/payments-overdue):**
- ✅ **قائمة الدفعات المتأخرة** مع عدد الأيام
- ✅ **قائمة الأقساط المتأخرة**
- ✅ **إجراءات سريعة** للمتابعة

### **5. تقرير المستحقة اليوم (/dashboard/payments-due-today):**
- ✅ **الأقساط المستحقة اليوم**
- ✅ **تذكيرات للمتابعة**

### **6. صفحة الإحصائيات (/dashboard/payments-statistics):**
- ✅ **إحصائيات شاملة** للإيرادات
- ✅ **توزيع حسب الحالة**
- ✅ **توزيع حسب الدورة**
- ✅ **الإيرادات الشهرية**

---

## 🔧 **كيفية الاستخدام**

### **1. إنشاء اشتراك جديد:**
```
1. اذهب إلى /dashboard/payments
2. اضغط "إضافة اشتراك جديد"
3. أدخل معلومات الطالب
4. اختر الدورة
5. حدد المبلغ الإجمالي
6. اختر عدد الأقساط (اختياري)
7. احفظ
```

### **2. تسجيل دفعة:**
```
1. اذهب إلى تفاصيل الاشتراك
2. اختر القسط المراد دفعه
3. اضغط "تسجيل دفعة"
4. أدخل طريقة الدفع ورقم المعاملة
5. ارفع صورة الإيصال (اختياري)
6. احفظ
```

### **3. متابعة المتأخرة:**
```
1. اذهب إلى "الدفعات المتأخرة"
2. راجع القائمة
3. تواصل مع الطلاب
4. سجل الدفعات عند الاستلام
```

---

## 📊 **الإحصائيات المتاحة**

### **إحصائيات عامة:**
- ✅ **إجمالي الطلاب** المسجلين
- ✅ **إجمالي الاشتراكات**
- ✅ **إجمالي الإيرادات** المحصلة
- ✅ **المبلغ المتبقي** للتحصيل
- ✅ **معدل إكمال الدفعات**

### **إحصائيات زمنية:**
- ✅ **الإيرادات الشهرية**
- ✅ **عدد الاشتراكات الجديدة**
- ✅ **معدل التحصيل**

### **إحصائيات حسب الحالة:**
- ✅ **في الانتظار** - عدد ونسبة
- ✅ **دفع جزئي** - عدد ونسبة
- ✅ **مكتمل** - عدد ونسبة
- ✅ **ملغي** - عدد ونسبة

### **إحصائيات حسب الدورة:**
- ✅ **عدد المشتركين** لكل دورة
- ✅ **الإيرادات** لكل دورة
- ✅ **معدل إكمال الدفع** لكل دورة

---

## 🚀 **للبدء**

### **1. تشغيل Migrations:**
```bash
php artisan migrate
```

### **2. إضافة الصلاحيات:**
```php
// في seeder الصلاحيات
'payments_read' => 'قراءة الدفعات',
'payments_create' => 'إنشاء دفعة',
'payments_update' => 'تحديث الدفعات',
'payments_delete' => 'حذف الدفعات'
```

### **3. إضافة رابط في القائمة:**
```html
<li>
    <a href="{{ route('dashboard.payments.index') }}">
        <i class="fa fa-money"></i> اشتراكات الطلاب
    </a>
</li>
```

### **4. الوصول للنظام:**
```
http://localhost:8000/dashboard/payments
```

---

**تم إنشاء نظام شامل لمتابعة اشتراكات الطلاب والدفعات! 💰✨**

**المميزات:**
- ✅ **نظام دفعات متكامل** مع التقسيط
- ✅ **تقارير مفصلة** للمتأخرة والمستحقة
- ✅ **إحصائيات شاملة** للإيرادات
- ✅ **واجهة سهلة الاستخدام** للإدارة
- ✅ **ربط مع النظام الحالي** (جدول purchases)
