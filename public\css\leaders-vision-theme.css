/* ===================================
   نموذج أكاديمية Leaders Vision
   Leaders Vision Academy Theme
   أكاديمية Leaders Vision - Leaders Vision Academy
   =================================== */

/* متغيرات الألوان - أكاديمية Leaders Vision */
:root {
    --leaders-primary: #2563eb;       /* أزرق Leaders Vision */
    --leaders-secondary: #dc2626;     /* أحمر Leaders Vision */
    --leaders-accent: #059669;        /* أخضر Leaders Vision */
    --leaders-gold: #f59e0b;          /* ذهبي للتمييز */
    --leaders-dark: #1f2937;          /* رمادي داكن */
    --leaders-light: #f8fafc;         /* أبيض مزرق */
    --leaders-gradient: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    --leaders-red-gradient: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    --leaders-accent-gradient: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

/* تحسينات البانر الرئيسي */
.rs-banner {
    background: var(--leaders-gradient);
    position: relative;
    overflow: hidden;
}

.rs-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.academy-logo {
    position: relative;
    z-index: 2;
}

.logo-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.logo-image:hover {
    transform: scale(1.1) rotate(5deg);
    border-color: var(--leaders-secondary);
}

.banner-title {
    font-size: 3.5rem;
    font-weight: 800;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 1rem;
}

.banner-subtitle {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--leaders-secondary) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    font-style: italic;
}

/* إحصائيات البانر */
.banner-stats {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--leaders-gold);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

/* شهادات الاعتماد */
.certifications {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cert-logos {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
}

.cert-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.cert-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.cert-item i {
    margin-left: 8px;
    font-size: 1.2rem;
}

/* بطاقات الرؤية والرسالة */
.vision-mission-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.vision-mission-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.vision-mission-card .card-icon {
    text-align: center;
    margin-bottom: 20px;
}

.vision-mission-card .card-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--leaders-secondary) !important;
}

.vision-mission-card .card-desc {
    font-size: 1.1rem;
    line-height: 1.8;
    opacity: 0.9;
}

/* القيم الأساسية */
.value-item {
    display: flex;
    align-items: flex-start;
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.value-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-5px);
}

.value-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    margin-left: 15px;
    flex-shrink: 0;
}

.value-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--leaders-gold) !important;
}

.value-content p {
    font-size: 0.95rem;
    opacity: 0.8;
    margin: 0;
}

/* الأهداف الاستراتيجية */
.goal-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    height: 100%;
}

.goal-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.goal-icon {
    margin-bottom: 20px;
}

.goal-card h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--leaders-secondary) !important;
}

.goal-card p {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.6;
}

/* فئات البرامج */
.category-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    height: 100%;
}

.category-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.category-icon {
    margin-bottom: 20px;
}

.category-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--leaders-gold) !important;
}

.category-desc {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.6;
}

/* تحسين الأزرار */
.btn-primary-custom {
    background: var(--leaders-red-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 15px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary-custom:hover {
    background: var(--leaders-red-gradient);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(220, 38, 38, 0.4);
}

.btn-primary-custom:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn-primary-custom:hover:before {
    left: 100%;
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.5);
    color: white;
    background: transparent;
    font-weight: 600;
    padding: 15px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    color: white;
    transform: translateY(-3px);
}

.btn-warning {
    background: var(--leaders-accent-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 15px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-warning:hover {
    background: var(--leaders-accent-gradient);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(5, 150, 105, 0.4);
    color: white;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .banner-title {
        font-size: 2.5rem;
    }

    .banner-subtitle {
        font-size: 1.4rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .banner-stats {
        padding: 20px;
    }

    .vision-mission-card,
    .goal-card,
    .category-card {
        margin-bottom: 20px;
    }

    .cert-logos {
        flex-direction: column;
        align-items: center;
    }

    .value-item {
        flex-direction: column;
        text-align: center;
    }

    .value-icon {
        margin-left: 0;
        margin-bottom: 15px;
    }
}

/* تأثيرات إضافية */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.floating {
    animation: float 3s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.pulsing {
    animation: pulse 2s ease-in-out infinite;
}

/* تحسين النصوص العربية */
.arabic-text {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    line-height: 1.8;
    letter-spacing: 0.5px;
}

.arabic-heading {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    font-weight: 700;
    line-height: 1.4;
}

/* تحسين الخلفيات */
.bg-leaders-vision {
    background: var(--leaders-gradient);
    position: relative;
}

.bg-leaders-vision::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

/* إحصائيات المدربين */
.trainers-stats {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    height: 100%;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.stat-card .stat-icon {
    margin-bottom: 15px;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--leaders-gold);
    margin-bottom: 10px;
}

.stat-card .stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

/* بطاقات الشراكات */
.partner-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 2px solid transparent;
}

.partner-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--leaders-primary);
}

.partner-logo {
    margin-bottom: 20px;
}

.partner-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--leaders-dark);
    margin-bottom: 10px;
}

.partner-desc {
    font-size: 0.95rem;
    color: #6b7280;
    margin: 0;
}

/* شارات الشهادات */
.cert-badge {
    background: white;
    border-radius: 10px;
    padding: 20px 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.cert-badge:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cert-text {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--leaders-dark);
    margin: 10px 0 0 0;
}

/* تحسينات إضافية للشراكات */
.rs-partners {
    position: relative;
    overflow: hidden;
}

.rs-partners::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagon" width="50" height="50" patternUnits="userSpaceOnUse"><polygon points="25,5 45,15 45,35 25,45 5,35 5,15" fill="none" stroke="rgba(30,58,138,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagon)"/></svg>');
    opacity: 0.3;
}

.government-partners,
.tech-partners,
.certifications-section {
    position: relative;
    z-index: 2;
}

/* تأثيرات خاصة للشهادات */
.text-purple {
    color: #7c3aed !important;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .partner-card {
        margin-bottom: 20px;
    }

    .cert-badge {
        margin-bottom: 15px;
    }

    .stat-card {
        margin-bottom: 20px;
    }

    .trainers-stats {
        padding: 20px;
    }

    .partner-name {
        font-size: 1rem;
    }

    .partner-desc {
        font-size: 0.9rem;
    }
}

/* ===================================
   صفحة المؤسس - Founder Page
   =================================== */

/* محتوى المؤسس */
.founder-content {
    padding: 20px 0;
}

.founder-position {
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    color: var(--leaders-gold) !important;
}

.founder-description {
    font-size: 1.1rem;
    line-height: 1.8;
}

/* قائمة الإنجازات */
.achievement-list {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.achievement-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    transition: all 0.3s ease;
}

.achievement-item:hover {
    transform: translateX(-5px);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 8px 15px;
}

/* معلومات الاتصال */
.contact-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.contact-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.contact-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    margin-left: 15px;
    flex-shrink: 0;
}

.contact-info {
    display: flex;
    flex-direction: column;
}

.contact-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 3px;
}

.contact-value {
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.contact-value:hover {
    color: var(--leaders-secondary) !important;
    text-decoration: none;
}

/* قسم صورة المؤسس */
.founder-image-section {
    position: relative;
    padding: 20px;
}

.founder-image-container {
    position: relative;
    text-align: center;
    z-index: 2;
}

.founder-image {
    width: 350px;
    height: 350px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.founder-image:hover {
    transform: scale(1.05);
    border-color: var(--leaders-secondary);
}

.founder-placeholder {
    width: 350px;
    height: 350px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 5px solid rgba(255, 255, 255, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

/* التأثيرات البصرية للخلفية */
.founder-bg-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
}

.circle-effect {
    position: absolute;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

/* الاقتباس الملهم */
.founder-quote {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 30px;
}

.quote-text {
    margin: 0;
    position: relative;
}

.quote-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    font-style: italic;
    margin-bottom: 20px;
}

.quote-author {
    font-size: 1rem;
    font-weight: 600;
}

/* حالة عدم وجود بيانات */
.no-founder-data {
    padding: 60px 20px;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .founder-image,
    .founder-placeholder {
        width: 250px;
        height: 250px;
    }

    .founder-content {
        margin-bottom: 40px;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-icon {
        margin-left: 0;
        margin-bottom: 10px;
    }

    .achievement-item {
        flex-direction: column;
        text-align: center;
        padding: 15px;
    }

    .achievement-item i {
        margin-left: 0;
        margin-bottom: 10px;
    }

    .founder-quote {
        padding: 20px;
        margin-top: 20px;
    }

    .circle-effect {
        display: none;
    }
}
