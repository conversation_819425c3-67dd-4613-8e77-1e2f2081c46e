{{-- مكون عرض الصور مع معالجة الأخطاء --}}
@php
    // الحصول على المتغيرات من attributes أو قيم افتراضية
    $src = $src ?? '';
    $alt = $alt ?? 'صورة أكاديمية Leaders Vision';
    $class = $class ?? '';
    $width = $width ?? null;
    $height = $height ?? null;
    $fallback = $fallback ?? 'images/logo.png';
    $lazy = $lazy ?? true;
    $responsive = $responsive ?? false;

    // استخدام ImageHelper إذا كان متوفراً
    if (class_exists('App\Helpers\ImageHelper')) {
        $imageUrl = App\Helpers\ImageHelper::getImageUrl($src, $fallback);
    } else {
        $imageUrl = $src ? asset($src) : asset($fallback);
    }

    $fallbackUrl = asset($fallback);

    // إعداد الخصائص
    $imgAttributes = '';

    if ($width) {
        $imgAttributes .= ' width="' . $width . '"';
    }

    if ($height) {
        $imgAttributes .= ' height="' . $height . '"';
    }

    if ($lazy) {
        $imgAttributes .= ' loading="lazy"';
    }

    if ($responsive) {
        $class .= ' img-responsive';
    }

    if ($class) {
        $imgAttributes .= ' class="' . $class . '"';
    }
@endphp

<img
    src="{{ $imageUrl }}"
    alt="{{ $alt }}"
    {!! $imgAttributes !!}
    onerror="this.src='{{ $fallbackUrl }}'; this.onerror=null;"
    style="transition: opacity 0.3s ease;"
    onload="this.style.opacity='1'"
>

<style>
.img-responsive {
    max-width: 100%;
    height: auto;
}

.image-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.image-error {
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 14px;
    min-height: 100px;
}
</style>
