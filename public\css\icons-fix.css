/* ===================================
   إصلاح مشكلة الأيقونات - أكاديمية Leaders Vision
   Icons Fix - Leaders Vision Academy
   =================================== */

/* إصلاح مسارات Font Awesome */
@font-face {
    font-family: 'FontAwesome';
    src: url('../dashboard_files/fonts/fontawesome-webfont.eot?v=4.7.0');
    src: url('../dashboard_files/fonts/fontawesome-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'),
         url('../dashboard_files/fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'),
         url('../dashboard_files/fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'),
         url('../dashboard_files/fonts/fontawesome-webfont.ttf?v=4.7.0') format('truetype'),
         url('../dashboard_files/fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* تأكيد تطبيق Font Awesome */
.fa,
[class^="fa-"],
[class*=" fa-"] {
    font-family: 'FontAwesome' !important;
    font-style: normal !important;
    font-weight: normal !important;
    line-height: 1 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    text-rendering: optimizeLegibility !important;
    speak: none !important;
    font-variant: normal !important;
    text-transform: none !important;
}

/* إصلاح أيقونات محددة */
.fa-graduation-cap:before { content: "\f19d"; }
.fa-users:before { content: "\f0c0"; }
.fa-user-tie:before { content: "\f508"; }
.fa-eye:before { content: "\f06e"; }
.fa-bullseye:before { content: "\f140"; }
.fa-star:before { content: "\f005"; }
.fa-star-o:before { content: "\f006"; }
.fa-lightbulb-o:before { content: "\f0eb"; }
.fa-handshake-o:before { content: "\f2b5"; }
.fa-shield:before { content: "\f132"; }
.fa-cogs:before { content: "\f085"; }
.fa-rocket:before { content: "\f135"; }
.fa-laptop:before { content: "\f109"; }
.fa-certificate:before { content: "\f0a3"; }
.fa-award:before { content: "\f559"; }
.fa-building:before { content: "\f1ad"; }
.fa-tag:before { content: "\f02b"; }
.fa-clock:before { content: "\f017"; }
.fa-shopping-cart:before { content: "\f07a"; }
.fa-angle-up:before { content: "\f106"; }
.fa-angle-left:before { content: "\f104"; }
.fa-circle-o:before { content: "\f10c"; }
.fa-gear:before { content: "\f013"; }
.fa-university:before { content: "\f19c"; }

/* أيقونات بديلة بـ CSS فقط */
.icon-fallback {
    display: inline-block;
    width: 1em;
    height: 1em;
    text-align: center;
    line-height: 1;
    font-weight: bold;
    border-radius: 50%;
    background: var(--primary-green, #4ade80);
    color: white;
    font-size: 0.8em;
}

/* أيقونات بديلة محددة */
.fa-graduation-cap.icon-fallback:before { content: "🎓"; background: none; }
.fa-users.icon-fallback:before { content: "👥"; background: none; }
.fa-user-tie.icon-fallback:before { content: "👔"; background: none; }
.fa-eye.icon-fallback:before { content: "👁"; background: none; }
.fa-bullseye.icon-fallback:before { content: "🎯"; background: none; }
.fa-star.icon-fallback:before { content: "⭐"; background: none; }
.fa-lightbulb-o.icon-fallback:before { content: "💡"; background: none; }
.fa-handshake-o.icon-fallback:before { content: "🤝"; background: none; }
.fa-shield.icon-fallback:before { content: "🛡"; background: none; }
.fa-cogs.icon-fallback:before { content: "⚙"; background: none; }
.fa-rocket.icon-fallback:before { content: "🚀"; background: none; }
.fa-laptop.icon-fallback:before { content: "💻"; background: none; }
.fa-certificate.icon-fallback:before { content: "📜"; background: none; }
.fa-award.icon-fallback:before { content: "🏆"; background: none; }
.fa-building.icon-fallback:before { content: "🏢"; background: none; }
.fa-tag.icon-fallback:before { content: "🏷"; background: none; }
.fa-clock.icon-fallback:before { content: "🕐"; background: none; }
.fa-shopping-cart.icon-fallback:before { content: "🛒"; background: none; }

/* تحسين مظهر الأيقونات */
.fa {
    color: inherit;
    transition: all 0.3s ease;
}

.fa:hover {
    transform: scale(1.1);
}

/* أيقونات ملونة حسب السياق */
.text-primary-custom .fa,
.fa.text-primary-custom {
    color: var(--primary-green, #4ade80) !important;
}

.text-warning .fa,
.fa.text-warning {
    color: #f59e0b !important;
}

.text-success .fa,
.fa.text-success {
    color: #10b981 !important;
}

.text-info .fa,
.fa.text-info {
    color: #14b8a6 !important;
}

.text-danger .fa,
.fa.text-danger {
    color: #ef4444 !important;
}

.text-muted .fa,
.fa.text-muted {
    color: #6b7280 !important;
}

/* أيقونات كبيرة */
.fa-2x {
    font-size: 2em !important;
}

.fa-3x {
    font-size: 3em !important;
}

/* تأثيرات خاصة للأيقونات */
.icon-glow {
    text-shadow: 0 0 10px currentColor;
}

.icon-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(359deg); }
}

.icon-pulse {
    animation: fa-pulse 1s infinite;
}

@keyframes fa-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* إصلاح أيقونات القائمة */
.sidebar-menu .fa,
.main-menu .fa {
    margin-left: 8px;
    width: 16px;
    text-align: center;
}

/* إصلاح أيقونات الأزرار */
.btn .fa {
    margin-left: 5px;
}

.btn .fa:first-child {
    margin-left: 0;
    margin-right: 5px;
}

/* إصلاح أيقونات البطاقات */
.card .fa,
.service-item .fa,
.counter-item .fa {
    display: block;
    margin-bottom: 10px;
}

/* إصلاح للشاشات الصغيرة */
@media (max-width: 768px) {
    .fa-3x {
        font-size: 2em !important;
    }
    
    .fa-2x {
        font-size: 1.5em !important;
    }
}

/* تحميل احتياطي من CDN */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css');

/* إصلاح مشكلة عدم التحميل */
.fa-fallback {
    font-family: 'FontAwesome', Arial, sans-serif !important;
}

/* إصلاح أيقونات الشبكات الاجتماعية */
.fa-facebook:before { content: "\f09a"; }
.fa-twitter:before { content: "\f099"; }
.fa-instagram:before { content: "\f16d"; }
.fa-linkedin:before { content: "\f0e1"; }
.fa-youtube:before { content: "\f167"; }
.fa-whatsapp:before { content: "\f232"; }

/* إصلاح أيقونات التنقل */
.fa-home:before { content: "\f015"; }
.fa-book:before { content: "\f02d"; }
.fa-phone:before { content: "\f095"; }
.fa-envelope:before { content: "\f0e0"; }
.fa-map-marker:before { content: "\f041"; }

/* تحسين الأداء */
.fa {
    will-change: transform;
    backface-visibility: hidden;
}

/* إصلاح مشكلة الترميز */
.fa {
    -webkit-font-feature-settings: normal;
    font-feature-settings: normal;
    font-variant-ligatures: normal;
}
