@extends('layouts.dashboard.app')

@section('content')

    <div class="content-wrapper">
        <section class="content-header">
            <h1>إضافة دفعة للمشترك</h1>
            <ol class="breadcrumb">
                <li><a href="{{ route('dashboard.welcome') }}"><i class="fa fa-dashboard"></i> الرئيسية</a></li>
                <li><a href="{{ route('dashboard.payment-tracking.index') }}">متابعة الدفعات</a></li>
                <li class="active">إضافة دفعة</li>
            </ol>
        </section>

        <section class="content">
            
            <!-- معلومات المشترك -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات المشترك</h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>اسم الطالب:</strong>
                                    <p>{{ $paymentTracking->student_name }}</p>
                                </div>
                                <div class="col-md-3">
                                    <strong>البريد الإلكتروني:</strong>
                                    <p>{{ $paymentTracking->student_email }}</p>
                                </div>
                                <div class="col-md-3">
                                    <strong>رقم الهاتف:</strong>
                                    <p>{{ $paymentTracking->student_phone }}</p>
                                </div>
                                <div class="col-md-3">
                                    <strong>الدورة:</strong>
                                    <p><span class="label label-info">{{ $paymentTracking->course_name }}</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الدفع -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-info">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات الدفع</h3>
                        </div>
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="info-box bg-blue">
                                        <span class="info-box-icon"><i class="fa fa-money"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">سعر الدورة</span>
                                            <span class="info-box-number">{{ number_format($paymentTracking->course_price, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-green">
                                        <span class="info-box-icon"><i class="fa fa-check"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">المدفوع</span>
                                            <span class="info-box-number">{{ number_format($paymentTracking->total_paid, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-yellow">
                                        <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">المتبقي</span>
                                            <span class="info-box-number">{{ number_format($paymentTracking->remaining_amount, 0) }} دج</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box bg-red">
                                        <span class="info-box-icon"><i class="fa fa-percent"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">التقدم</span>
                                            <span class="info-box-number">{{ $paymentTracking->payment_progress }}%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- شريط التقدم -->
                            <div class="progress progress-lg">
                                <div class="progress-bar progress-bar-{{ $paymentTracking->payment_progress >= 100 ? 'success' : ($paymentTracking->payment_progress >= 50 ? 'warning' : 'danger') }}" 
                                     style="width: {{ $paymentTracking->payment_progress }}%">
                                    {{ $paymentTracking->payment_progress }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج إضافة دفعة -->
            <div class="row">
                <div class="col-md-8">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">إضافة دفعة جديدة</h3>
                        </div>
                        <form action="{{ route('dashboard.payment-tracking.add-payment', $paymentTracking) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="box-body">
                                
                                <!-- مبلغ الدفعة -->
                                <div class="form-group">
                                    <label for="amount">مبلغ الدفعة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" 
                                               class="form-control" 
                                               id="amount" 
                                               name="amount" 
                                               step="0.01" 
                                               min="0.01" 
                                               max="{{ $paymentTracking->remaining_amount }}"
                                               value="{{ old('amount') }}" 
                                               required>
                                        <span class="input-group-addon">دج</span>
                                    </div>
                                    <small class="text-muted">الحد الأقصى: {{ number_format($paymentTracking->remaining_amount, 2) }} دج</small>
                                    @error('amount')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- طريقة الدفع -->
                                <div class="form-group">
                                    <label for="payment_method">طريقة الدفع <span class="text-danger">*</span></label>
                                    <select class="form-control" id="payment_method" name="payment_method" required>
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>نقداً</option>
                                        <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي</option>
                                        <option value="credit_card" {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                        <option value="mobile_payment" {{ old('payment_method') == 'mobile_payment' ? 'selected' : '' }}>دفع عبر الهاتف</option>
                                        <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>شيك</option>
                                        <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>أخرى</option>
                                    </select>
                                    @error('payment_method')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- رقم الإيصال -->
                                <div class="form-group">
                                    <label for="receipt_number">رقم الإيصال</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="receipt_number" 
                                           name="receipt_number" 
                                           value="{{ old('receipt_number') }}" 
                                           placeholder="رقم الإيصال أو المرجع">
                                    @error('receipt_number')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- صورة الإيصال -->
                                <div class="form-group">
                                    <label for="receipt_image">صورة الإيصال</label>
                                    <input type="file" 
                                           class="form-control" 
                                           id="receipt_image" 
                                           name="receipt_image" 
                                           accept="image/*">
                                    <small class="text-muted">الصيغ المدعومة: JPG, PNG, JPEG - الحد الأقصى: 2MB</small>
                                    @error('receipt_image')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- ملاحظات -->
                                <div class="form-group">
                                    <label for="notes">ملاحظات</label>
                                    <textarea class="form-control" 
                                              id="notes" 
                                              name="notes" 
                                              rows="3" 
                                              placeholder="أي ملاحظات إضافية...">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                            </div>
                            <div class="box-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fa fa-save"></i> إضافة الدفعة
                                </button>
                                <a href="{{ route('dashboard.payment-tracking.show', $paymentTracking) }}" class="btn btn-default">
                                    <i class="fa fa-times"></i> إلغاء
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="col-md-4">
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title">معلومات مهمة</h3>
                        </div>
                        <div class="box-body">
                            <div class="callout callout-info">
                                <h4><i class="fa fa-info"></i> ملاحظة:</h4>
                                <p>الدفعة ستكون في حالة "في الانتظار" حتى يتم التحقق منها من قبل الإدارة.</p>
                            </div>
                            
                            <div class="callout callout-warning">
                                <h4><i class="fa fa-warning"></i> تنبيه:</h4>
                                <p>المشترك سيُعتبر "مدفوع" فقط عند إكمال دفع المبلغ الكامل للدورة.</p>
                            </div>

                            @if($paymentTracking->payment_deadline)
                            <div class="callout callout-{{ $paymentTracking->is_overdue ? 'danger' : 'success' }}">
                                <h4><i class="fa fa-calendar"></i> الموعد النهائي:</h4>
                                <p>{{ $paymentTracking->payment_deadline->format('Y-m-d') }}</p>
                                @if($paymentTracking->is_overdue)
                                    <p class="text-danger">متأخر {{ $paymentTracking->days_overdue }} يوم</p>
                                @else
                                    <p class="text-success">{{ $paymentTracking->days_until_deadline }} يوم متبقي</p>
                                @endif
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- الدفعات السابقة -->
                    @if($paymentTracking->individualPayments->count() > 0)
                    <div class="box box-default">
                        <div class="box-header with-border">
                            <h3 class="box-title">الدفعات السابقة</h3>
                        </div>
                        <div class="box-body">
                            @foreach($paymentTracking->individualPayments->take(5) as $payment)
                                <div class="payment-item" style="border-bottom: 1px solid #f0f0f0; padding: 8px 0;">
                                    <div class="row">
                                        <div class="col-xs-8">
                                            <strong>{{ number_format($payment->amount, 0) }} دج</strong>
                                            <br>
                                            <small class="text-muted">{{ $payment->payment_date->format('Y-m-d') }}</small>
                                        </div>
                                        <div class="col-xs-4">
                                            <span class="label label-{{ $payment->status == 'verified' ? 'success' : ($payment->status == 'pending' ? 'warning' : 'danger') }}">
                                                {{ $payment->status_arabic }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                            @if($paymentTracking->individualPayments->count() > 5)
                                <p class="text-center" style="margin-top: 10px;">
                                    <a href="{{ route('dashboard.payment-tracking.show', $paymentTracking) }}">
                                        عرض جميع الدفعات ({{ $paymentTracking->individualPayments->count() }})
                                    </a>
                                </p>
                            @endif
                        </div>
                    </div>
                    @endif
                </div>
            </div>

        </section>
    </div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // حساب المبلغ المتبقي تلقائياً
    $('#amount').on('input', function() {
        var amount = parseFloat($(this).val()) || 0;
        var remaining = {{ $paymentTracking->remaining_amount }};
        
        if (amount > remaining) {
            $(this).val(remaining.toFixed(2));
            alert('المبلغ لا يمكن أن يكون أكبر من المبلغ المتبقي');
        }
    });

    // معاينة الصورة
    $('#receipt_image').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                // يمكن إضافة معاينة الصورة هنا
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@endpush

@push('styles')
<style>
.info-box {
    margin-bottom: 15px;
}

.progress-lg {
    height: 30px;
    margin-bottom: 20px;
}

.progress-lg .progress-bar {
    font-size: 16px;
    line-height: 30px;
}

.payment-item:last-child {
    border-bottom: none !important;
}

.callout {
    margin-bottom: 15px;
}
</style>
@endpush
