<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Purchase;
use App\Models\PaymentInstallment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SimplePaymentController extends Controller
{
    /**
     * عرض صفحة إضافة دفعة
     */
    public function showAddPayment(Purchase $purchase)
    {
        // تحميل العلاقة مع الدورة
        $purchase->load('course');

        // جلب المعلومات من النموذج
        $coursePrice = $purchase->getCoursePrice();
        $totalPaid = $purchase->paid_amount;
        $remaining = $purchase->getRemainingAmount();
        $progress = $purchase->getPaymentProgress();

        // الدفعات السابقة من جدول payment_installments مباشرة
        $previousPayments = $purchase->paidInstallments()
                                    ->orderBy('paid_date', 'desc')
                                    ->get()
                                    ->map(function($payment) {
                                        return [
                                            'amount' => $payment->amount,
                                            'date' => $payment->paid_date ? $payment->paid_date->format('Y-m-d') : 'غير محدد',
                                            'method' => $payment->payment_method ?: 'غير محدد',
                                            'installment_number' => $payment->installment_number,
                                            'transaction_id' => $payment->transaction_id,
                                            'status' => $payment->status
                                        ];
                                    })
                                    ->toArray();

        return view('dashboard.add-payment-simple', compact(
            'purchase',
            'coursePrice',
            'totalPaid',
            'remaining',
            'progress',
            'previousPayments'
        ));
    }

    /**
     * حفظ دفعة جديدة
     */
    public function storePayment(Request $request, Purchase $purchase)
    {
        \Log::info('Payment attempt started', [
            'purchase_id' => $purchase->id,
            'amount' => $request->amount,
            'payment_method' => $request->payment_method
        ]);

        // التحقق من صحة البيانات
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|string',
            'receipt_number' => 'nullable|string|max:100',
            'receipt_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'notes' => 'nullable|string'
        ]);

        \Log::info('Validation passed');

        try {
            // تحميل العلاقة مع الدورة
            $purchase->load('course');
            $coursePrice = $purchase->getCoursePrice();

            // التحقق من أن المبلغ لا يتجاوز المتبقي
            $remaining = $purchase->getRemainingAmount();
            if ($request->amount > $remaining) {
                return redirect()->back()
                               ->withErrors(['amount' => 'المبلغ لا يمكن أن يكون أكبر من المبلغ المتبقي: ' . number_format($remaining, 0) . ' دج'])
                               ->withInput();
            }

            // رفع صورة الإيصال
            $receiptImage = null;
            if ($request->hasFile('receipt_image')) {
                $receiptImage = time() . '_' . $request->file('receipt_image')->getClientOriginalName();
                $request->file('receipt_image')->move(public_path('uploads/receipts'), $receiptImage);
            }

            // حساب رقم القسط التالي
            $nextInstallmentNumber = $purchase->getNextInstallmentNumber();
            \Log::info('Next installment number: ' . $nextInstallmentNumber);

            // إضافة الدفعة إلى جدول payment_installments مباشرة
            \Log::info('Creating payment installment', [
                'purchase_id' => $purchase->id,
                'installment_number' => $nextInstallmentNumber,
                'amount' => $request->amount
            ]);

            $payment = PaymentInstallment::create([
                'purchase_id' => $purchase->id,
                // نحذف student_payment_id لتجنب مشكلة foreign key
                'installment_number' => $nextInstallmentNumber,
                'amount' => $request->amount,
                'due_date' => now(),
                'paid_date' => now(),
                'status' => 'paid',
                'payment_method' => $request->payment_method,
                'transaction_id' => $request->receipt_number,
                'receipt_image' => $receiptImage,
                'notes' => $request->notes
            ]);

            \Log::info('Payment installment created', ['payment_id' => $payment->id]);

            // تحديث المبلغ المدفوع في جدول purchases
            $purchase->addPayment($request->amount);
            \Log::info('Purchase updated', ['new_paid_amount' => $purchase->paid_amount]);

            $installmentNumber = $payment->installment_number;

            // رسالة النجاح
            if ($purchase->isFullyPaid()) {
                session()->flash('success', 'تم إضافة القسط رقم ' . $installmentNumber . ' بنجاح! المشترك الآن مدفوع بالكامل وتم قبول طلبه.');
            } else {
                $newRemaining = $purchase->getRemainingAmount();
                session()->flash('success', 'تم إضافة القسط رقم ' . $installmentNumber . ' بنجاح. المبلغ المتبقي: ' . number_format($newRemaining, 0) . ' دج');
            }

            return redirect()->route('dashboard.purchases.show', $purchase);

        } catch (\Exception $e) {
            \Log::error('Payment Error: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());

            return redirect()->back()
                           ->withErrors(['error' => 'حدث خطأ: ' . $e->getMessage()])
                           ->withInput();
        }
    }

    /**
     * صفحة متابعة الدفعات البسيطة
     */
    public function simplePaymentTracking(Request $request)
    {
        $query = Purchase::with('course');

        // البحث
        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('first_name', 'like', "%{$request->search}%")
                  ->orWhere('last_name', 'like', "%{$request->search}%")
                  ->orWhere('email', 'like', "%{$request->search}%")
                  ->orWhere('phone', 'like', "%{$request->search}%")
                  ->orWhere('name_course', 'like', "%{$request->search}%");
            });
        }

        // فلترة حسب حالة الدفع
        if ($request->payment_status) {
            switch ($request->payment_status) {
                case 'not_paid':
                    $query->where('paid_amount', 0);
                    break;
                case 'partial':
                    $query->where('paid_amount', '>', 0)
                          ->whereRaw('paid_amount < (SELECT price FROM courses WHERE courses.id = purchases.course_id)');
                    break;
                case 'fully_paid':
                    $query->whereRaw('paid_amount >= (SELECT price FROM courses WHERE courses.id = purchases.course_id)');
                    break;
            }
        }

        // فلترة حسب الدورة
        if ($request->course_id) {
            $query->where('course_id', $request->course_id);
        }

        // فلترة حسب الحالة
        if ($request->status !== null && $request->status !== '') {
            $query->where('status', $request->status);
        }

        $purchases = $query->orderBy('created_at', 'desc')->paginate(15);

        // إحصائيات
        $stats = [
            'total_subscribers' => Purchase::count(),
            'total_revenue' => Purchase::sum('paid_amount'),
            'pending_revenue' => DB::select("
                SELECT SUM(courses.price - purchases.paid_amount) as pending
                FROM purchases
                LEFT JOIN courses ON purchases.course_id = courses.id
                WHERE purchases.paid_amount < courses.price
            ")[0]->pending ?? 0,
            'fully_paid' => DB::select("
                SELECT COUNT(*) as count
                FROM purchases
                LEFT JOIN courses ON purchases.course_id = courses.id
                WHERE purchases.paid_amount >= courses.price
            ")[0]->count ?? 0
        ];

        $courses = \App\Models\Course::all();

        return view('dashboard.simple-payment-tracking', compact('purchases', 'stats', 'courses'));
    }

    /**
     * عرض تقرير الدفعات
     */
    public function paymentsReport()
    {
        // التحقق من وجود الجدول
        if (!DB::getSchemaBuilder()->hasTable('payment_installments')) {
            $payments = collect([]);
            $stats = [
                'total_payments' => 0,
                'total_amount' => 0,
                'verified_payments' => 0,
                'pending_payments' => 0
            ];
        } else {
            $payments = DB::table('payment_installments')
                         ->join('purchases', 'payment_installments.purchase_id', '=', 'purchases.id')
                         ->select(
                             'payment_installments.*',
                             'purchases.first_name',
                             'purchases.last_name',
                             'purchases.email',
                             'purchases.name_course'
                         )
                         ->orderBy('payment_installments.payment_date', 'desc')
                         ->paginate(20);

            $stats = [
                'total_payments' => DB::table('payment_installments')->count(),
                'total_amount' => DB::table('payment_installments')->where('status', 'verified')->sum('amount'),
                'verified_payments' => DB::table('payment_installments')->where('status', 'verified')->count(),
                'pending_payments' => DB::table('payment_installments')->where('status', 'pending')->count()
            ];
        }

        return view('dashboard.payments-report', compact('payments', 'stats'));
    }

    /**
     * عرض دفعات مشترك محدد
     */
    public function showStudentPayments(Purchase $purchase)
    {
        // تحميل العلاقة مع الدورة
        $purchase->load('course');

        // جلب المعلومات من النموذج
        $coursePrice = $purchase->getCoursePrice();
        $totalPaid = $purchase->paid_amount;
        $remaining = $purchase->getRemainingAmount();
        $progress = $purchase->getPaymentProgress();
        $isFullyPaid = $purchase->isFullyPaid();

        // جلب جميع الدفعات من جدول payment_installments مباشرة
        $payments = $purchase->paymentInstallments()
                            ->orderBy('installment_number')
                            ->get();

        return view('dashboard.student-payments', compact(
            'purchase',
            'payments',
            'coursePrice',
            'totalPaid',
            'remaining',
            'progress',
            'isFullyPaid'
        ));
    }
}
