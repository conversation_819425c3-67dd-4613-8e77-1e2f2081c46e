/* ===================================
   تخصيص لوحة التحكم - أكاديمية Leaders Vision
   Dashboard Customization - Leaders Vision Academy
   =================================== */

/* تخصيص الشعار */
.main-header .logo {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    border-bottom: 3px solid #1e40af;
}

.main-header .logo:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%) !important;
}

.logo-lg {
    font-family: 'Cairo', 'Arial', sans-serif !important;
    font-weight: 700 !important;
    color: white !important;
}

.logo-mini {
    font-family: 'Cairo', 'Arial', sans-serif !important;
    font-weight: 700 !important;
    color: white !important;
}

/* تخصيص الشريط الجانبي */
.main-sidebar {
    background: #1f2937 !important;
}

.sidebar-menu > li > a {
    color: #d1d5db !important;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.sidebar-menu > li > a:hover,
.sidebar-menu > li.active > a {
    background: #374151 !important;
    border-left: 3px solid #2563eb !important;
    color: #ffffff !important;
}

.sidebar-menu > li > a > .fa {
    color: #9ca3af;
    transition: color 0.3s ease;
}

.sidebar-menu > li > a:hover > .fa,
.sidebar-menu > li.active > a > .fa {
    color: #2563eb !important;
}

/* تخصيص بطاقات الإحصائيات */
.info-box {
    border-radius: 12px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    border: none !important;
    overflow: hidden;
    transition: all 0.3s ease;
}

.info-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.info-box-icon {
    border-radius: 12px 0 0 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.info-box-content {
    padding: 15px !important;
}

.info-box-text {
    font-family: 'Cairo', 'Arial', sans-serif !important;
    font-weight: 600 !important;
    font-size: 14px !important;
}

.info-box-number {
    font-family: 'Cairo', 'Arial', sans-serif !important;
    font-weight: 700 !important;
    font-size: 24px !important;
}

/* ألوان مخصصة للبطاقات */
.bg-aqua {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
}

.bg-green {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.bg-red {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
}

.bg-yellow {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
}

/* تخصيص الصناديق */
.box {
    border-radius: 12px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    border: none !important;
    overflow: hidden;
}

.box-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-bottom: 1px solid #e2e8f0 !important;
    padding: 15px 20px !important;
}

.box-title {
    font-family: 'Cairo', 'Arial', sans-serif !important;
    font-weight: 600 !important;
    color: #1e293b !important;
}

.box-body {
    padding: 20px !important;
}

/* تخصيص الأزرار */
.btn {
    border-radius: 8px !important;
    font-family: 'Cairo', 'Arial', sans-serif !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.btn-primary {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    border: none !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%) !important;
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    border: none !important;
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
    border: none !important;
}

/* تخصيص النصوص العربية */
.arabic-heading {
    font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif !important;
    font-weight: 700 !important;
    color: #1e293b !important;
}

.arabic-text {
    font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif !important;
    font-weight: 500 !important;
    direction: rtl !important;
    text-align: right !important;
}

/* تخصيص شريط التنقل العلوي */
.main-header .navbar {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
}

.navbar-nav > li > a {
    color: white !important;
}

.navbar-nav > li > a:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}

/* تخصيص القوائم المنسدلة */
.dropdown-menu {
    border-radius: 8px !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    border: none !important;
}

/* تخصيص مؤشرات التقدم */
.progress {
    height: 4px !important;
    border-radius: 2px !important;
    background: rgba(0, 0, 0, 0.1) !important;
}

.progress-bar {
    border-radius: 2px !important;
}

/* تخصيص الجداول */
.table {
    border-radius: 8px !important;
    overflow: hidden !important;
}

.table thead th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border: none !important;
    font-family: 'Cairo', 'Arial', sans-serif !important;
    font-weight: 600 !important;
    color: #1e293b !important;
}

/* تخصيص النماذج */
.form-control {
    border-radius: 8px !important;
    border: 2px solid #e2e8f0 !important;
    font-family: 'Cairo', 'Arial', sans-serif !important;
    transition: all 0.3s ease !important;
}

.form-control:focus {
    border-color: #2563eb !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

/* تخصيص الإشعارات */
.alert {
    border-radius: 8px !important;
    border: none !important;
    font-family: 'Cairo', 'Arial', sans-serif !important;
}

/* تحسينات إضافية */
.content-wrapper {
    background: #f8fafc !important;
}

.content-header {
    background: white !important;
    border-radius: 12px !important;
    margin: 15px !important;
    padding: 20px !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.info-box {
    animation: fadeInUp 0.6s ease-out;
}

.box {
    animation: fadeInUp 0.6s ease-out;
}

/* تخصيص الروابط السريعة */
.quick-link {
    background: white !important;
    border: 2px solid #e2e8f0 !important;
    transition: all 0.3s ease !important;
}

.quick-link:hover {
    border-color: #2563eb !important;
    background: #2563eb !important;
    color: white !important;
}

/* تخصيص responsive */
@media (max-width: 768px) {
    .content-header {
        margin: 10px !important;
        padding: 15px !important;
    }
    
    .info-box {
        margin-bottom: 15px !important;
    }
    
    .arabic-heading {
        font-size: 18px !important;
    }
}
