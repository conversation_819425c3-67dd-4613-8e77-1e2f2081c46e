{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2.5|^8.0", "anlutro/l4-settings": "^1.0", "coreui/coreui": "3.2.0", "fideloper/proxy": "^4.4", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^6.3.1|^7.0.1", "intervention/image": "^2.5", "laravel/framework": "^7.29", "laravel/tinker": "^2.5", "laravel/ui": "^2.4", "mcamara/laravel-localization": "^1.6", "santigarcor/laratrust": "^6.3", "spatie/laravel-translatable": "^4.6"}, "require-dev": {"facade/ignition": "^2.0", "fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^4.3", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}