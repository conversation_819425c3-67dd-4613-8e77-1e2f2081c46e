<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\StudentPaymentTracking;
use App\Models\IndividualPayment;
use App\Models\Purchase;
use App\Models\Course;
use Illuminate\Http\Request;
use Carbon\Carbon;

class PaymentTrackingController extends Controller
{
    /**
     * عرض قائمة متابعة الدفعات
     */
    public function index(Request $request)
    {
        $query = StudentPaymentTracking::with(['course', 'individualPayments']);

        // البحث
        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('student_name', 'like', "%{$request->search}%")
                  ->orWhere('student_email', 'like', "%{$request->search}%")
                  ->orWhere('student_phone', 'like', "%{$request->search}%")
                  ->orWhere('course_name', 'like', "%{$request->search}%");
            });
        }

        // فلترة حسب الحالة
        if ($request->status) {
            $query->where('payment_status', $request->status);
        }

        // فلترة حسب الدورة
        if ($request->course_id) {
            $query->where('course_id', $request->course_id);
        }

        // فلترة المتأخرة
        if ($request->overdue) {
            $query->overdue();
        }

        $payments = $query->active()->orderBy('created_at', 'desc')->paginate(15);

        // إحصائيات سريعة
        $stats = [
            'total' => StudentPaymentTracking::active()->count(),
            'not_started' => StudentPaymentTracking::active()->notStarted()->count(),
            'partial' => StudentPaymentTracking::active()->partial()->count(),
            'completed' => StudentPaymentTracking::active()->completed()->count(),
            'overdue' => StudentPaymentTracking::active()->overdue()->count(),
            'total_revenue' => StudentPaymentTracking::active()->sum('total_paid'),
            'pending_revenue' => StudentPaymentTracking::active()->sum('remaining_amount'),
            'pending_payments' => IndividualPayment::pending()->count()
        ];

        $courses = Course::all();

        return view('dashboard.payment-tracking.index', compact('payments', 'stats', 'courses'));
    }

    /**
     * عرض تفاصيل متابعة دفعات طالب
     */
    public function show(StudentPaymentTracking $paymentTracking)
    {
        $paymentTracking->load(['course', 'purchase', 'individualPayments' => function($query) {
            $query->orderBy('payment_date', 'desc');
        }]);

        return view('dashboard.payment-tracking.show', compact('paymentTracking'));
    }

    /**
     * عرض صفحة إضافة دفعة
     */
    public function showAddPayment(StudentPaymentTracking $paymentTracking)
    {
        // التحقق من أن الدفع غير مكتمل
        if ($paymentTracking->payment_status == 'completed') {
            session()->flash('error', 'هذا المشترك قد أكمل دفع المبلغ بالكامل');
            return redirect()->route('dashboard.payment-tracking.show', $paymentTracking);
        }

        $paymentTracking->load(['course', 'individualPayments' => function($query) {
            $query->orderBy('payment_date', 'desc');
        }]);

        return view('dashboard.payment-tracking.add-payment', compact('paymentTracking'));
    }

    /**
     * إنشاء متابعة دفعات من طلب شراء
     */
    public function createFromPurchase(Request $request, Purchase $purchase)
    {
        $request->validate([
            'course_price' => 'required|numeric|min:0',
            'payment_deadline' => 'nullable|date|after:today'
        ]);

        // التحقق من عدم وجود متابعة دفعات مسبقاً
        $existing = StudentPaymentTracking::where('purchase_id', $purchase->id)->first();
        if ($existing) {
            return redirect()->back()->withErrors(['error' => 'يوجد متابعة دفعات لهذا الطلب مسبقاً']);
        }

        $paymentTracking = StudentPaymentTracking::createFromPurchase(
            $purchase,
            $request->course_price,
            $request->payment_deadline
        );

        session()->flash('success', 'تم إنشاء متابعة الدفعات بنجاح');
        return redirect()->route('dashboard.payment-tracking.show', $paymentTracking);
    }

    /**
     * إضافة دفعة جديدة
     */
    public function addPayment(Request $request, StudentPaymentTracking $paymentTracking)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|string',
            'receipt_number' => 'nullable|string|max:100',
            'receipt_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'notes' => 'nullable|string'
        ]);

        try {
            $receiptImage = null;

            if ($request->hasFile('receipt_image')) {
                $receiptImage = time() . '_' . $request->file('receipt_image')->getClientOriginalName();
                $request->file('receipt_image')->move(public_path('uploads/receipts'), $receiptImage);
            }

            $payment = $paymentTracking->addPayment(
                $request->amount,
                $request->payment_method,
                $request->receipt_number,
                $receiptImage,
                $request->notes
            );

            session()->flash('success', 'تم إضافة الدفعة بنجاح وهي في انتظار التحقق');
            return redirect()->back();

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withErrors(['error' => 'حدث خطأ: ' . $e->getMessage()])
                           ->withInput();
        }
    }

    /**
     * التحقق من دفعة
     */
    public function verifyPayment(Request $request, IndividualPayment $payment)
    {
        try {
            $payment->verify(auth()->user()->name);

            session()->flash('success', 'تم التحقق من الدفعة بنجاح');
            return redirect()->back();

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withErrors(['error' => 'حدث خطأ: ' . $e->getMessage()]);
        }
    }

    /**
     * رفض دفعة
     */
    public function rejectPayment(Request $request, IndividualPayment $payment)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        try {
            $payment->reject($request->reason);

            session()->flash('success', 'تم رفض الدفعة');
            return redirect()->back();

        } catch (\Exception $e) {
            return redirect()->back()
                           ->withErrors(['error' => 'حدث خطأ: ' . $e->getMessage()]);
        }
    }

    /**
     * الدفعات المعلقة (تحتاج تحقق)
     */
    public function pendingPayments()
    {
        $pendingPayments = IndividualPayment::with(['paymentTracking.course'])
                                          ->pending()
                                          ->orderBy('payment_date', 'desc')
                                          ->paginate(20);

        return view('dashboard.payment-tracking.pending', compact('pendingPayments'));
    }

    /**
     * تقرير الدفعات المتأخرة
     */
    public function overdueReport()
    {
        $overduePayments = StudentPaymentTracking::with(['course', 'individualPayments'])
                                                ->overdue()
                                                ->orderBy('payment_deadline')
                                                ->get();

        return view('dashboard.payment-tracking.overdue', compact('overduePayments'));
    }

    /**
     * إحصائيات الدفعات
     */
    public function statistics()
    {
        $stats = [
            // إحصائيات عامة
            'total_students' => StudentPaymentTracking::active()->count(),
            'total_revenue' => StudentPaymentTracking::active()->sum('total_paid'),
            'pending_revenue' => StudentPaymentTracking::active()->sum('remaining_amount'),
            'completion_rate' => StudentPaymentTracking::active()->completed()->count() / max(StudentPaymentTracking::active()->count(), 1) * 100,

            // إحصائيات حسب الحالة
            'by_status' => [
                'not_started' => StudentPaymentTracking::active()->notStarted()->count(),
                'partial' => StudentPaymentTracking::active()->partial()->count(),
                'completed' => StudentPaymentTracking::active()->completed()->count(),
                'overdue' => StudentPaymentTracking::active()->overdue()->count(),
            ],

            // إحصائيات الدفعات
            'payments_today' => IndividualPayment::verified()->today()->sum('amount'),
            'payments_this_week' => IndividualPayment::verified()->thisWeek()->sum('amount'),
            'payments_this_month' => IndividualPayment::verified()->thisMonth()->sum('amount'),
            'pending_payments_count' => IndividualPayment::pending()->count(),

            // إحصائيات حسب الدورة
            'by_course' => Course::withCount([
                'studentPaymentTrackings',
                'studentPaymentTrackings as completed_count' => function($query) {
                    $query->where('payment_status', 'completed');
                }
            ])->withSum('studentPaymentTrackings', 'total_paid')
              ->having('student_payment_trackings_count', '>', 0)
              ->get()
        ];

        return view('dashboard.payment-tracking.statistics', compact('stats'));
    }

    /**
     * تحديث حالة المتأخرة تلقائياً
     */
    public function updateOverdueStatus()
    {
        $updated = StudentPaymentTracking::where('payment_deadline', '<', Carbon::now())
                                       ->whereIn('payment_status', ['not_started', 'partial'])
                                       ->update(['payment_status' => 'overdue']);

        return response()->json(['updated' => $updated]);
    }
}
