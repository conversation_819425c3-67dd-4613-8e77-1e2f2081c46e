# إصلاحات سريعة - أكاديمية Leaders Vision
## Quick Fixes - Leaders Vision Academy

---

## 🚨 **المشكلة المحلولة: HasFactory Trait**

### **الخطأ:**
```
Trait 'Illuminate\Database\Eloquent\Factories\HasFactory' not found
```

### **السبب:**
- Laravel 7.30.4 لا يدعم `HasFactory`
- هذه الميزة متوفرة في Laravel 8+

### **الحل المطبق:**
✅ إزالة `HasFactory` من Model Institution
✅ استبدال `str_starts_with()` بـ `substr()` للتوافق مع PHP 7
✅ تحديث autoload

---

## 🔧 **الأوامر السريعة للإصلاح**

### **إذا واجهت مشاكل مشابهة:**

```bash
# مسح الكاش
php artisan config:clear
php artisan cache:clear
php artisan view:clear

# تحديث autoload
composer dump-autoload

# إعادة تشغيل الخادم
php artisan serve --host=0.0.0.0 --port=8000
```

---

## 📊 **إدخال بيانات المؤسسة**

### **الطريقة الأولى: SQL مباشر**
```sql
-- في phpMyAdmin
INSERT INTO `institutions` (
    `name_ar`, `name_en`, `description_ar`, `slogan_ar`, `slogan_en`,
    `phone_1`, `phone_2`, `email`, `website`,
    `address_ar`, `city`, `state`, `country`,
    `currency`, `currency_symbol`, `currency_name_ar`,
    `logo_path`, `institution_type`, `specialization`,
    `services`, `working_hours`,
    `students_count`, `courses_count`, `trainers_count`, `graduates_count`,
    `is_active`, `is_featured`, `created_at`, `updated_at`
) VALUES (
    'أكاديمية Leaders Vision',
    'Leaders Vision Academy',
    '🎯 أكاديمية متخصصة في التكوينات المهنية والدراسات الاقتصادية 🎯\n✔ تكوينات مهنية متخصصة | ورشات تطبيقية 🎓\n✔ دراسات الجدوى 📊 | مرافقة المشاريع 🚀',
    'نصنع قادة المستقبل بالعلم والتقنية',
    'Building Future Leaders with Science and Technology',
    '0774479525',
    '0665657400',
    '<EMAIL>',
    'www.leadersvision.academy',
    'عوين زريقة، شارع ب رقم 16',
    'برج بوعريريج',
    'برج بوعريريج',
    'الجزائر',
    'DZD',
    'د.ج',
    'دينار جزائري',
    'images/logo.png',
    'مدرسة خاصة',
    'التكوينات المهنية والدراسات الاقتصادية',
    '["تكوينات مهنية متخصصة", "ورشات تطبيقية", "دراسات الجدوى", "مرافقة المشاريع"]',
    '{"السبت": "08:00-17:00", "الأحد": "08:00-17:00", "الاثنين": "08:00-17:00", "الثلاثاء": "08:00-17:00", "الأربعاء": "08:00-17:00", "الخميس": "08:00-17:00", "الجمعة": "مغلق"}',
    250, 35, 15, 500,
    1, 1,
    NOW(), NOW()
);
```

### **الطريقة الثانية: Tinker**
```bash
php artisan tinker

# في tinker:
App\Models\Institution::create([
    'name_ar' => 'أكاديمية Leaders Vision',
    'name_en' => 'Leaders Vision Academy',
    'description_ar' => '🎯 أكاديمية متخصصة في التكوينات المهنية والدراسات الاقتصادية',
    'slogan_ar' => 'نصنع قادة المستقبل بالعلم والتقنية',
    'phone_1' => '0774479525',
    'phone_2' => '0665657400',
    'email' => '<EMAIL>',
    'address_ar' => 'عوين زريقة، شارع ب رقم 16',
    'city' => 'برج بوعريريج',
    'state' => 'برج بوعريريج',
    'country' => 'الجزائر',
    'currency' => 'DZD',
    'currency_symbol' => 'د.ج',
    'currency_name_ar' => 'دينار جزائري'
]);

exit
```

---

## 🎯 **التحقق من نجاح الإصلاح**

### **1. اختبار الموقع:**
- ✅ الصفحة الرئيسية تعمل: http://localhost:8000
- ✅ لوحة التحكم تعمل: http://localhost:8000/dashboard
- ✅ لا توجد أخطاء PHP

### **2. اختبار قاعدة البيانات:**
```sql
-- التحقق من وجود جدول المؤسسة
SELECT * FROM institutions;

-- التحقق من البيانات
SELECT name_ar, phone_1, currency_symbol FROM institutions;
```

### **3. اختبار الوظائف:**
```php
// في tinker
$institution = App\Models\Institution::first();
echo $institution->name_ar;
echo $institution->formatted_phones;
```

---

## 🔄 **إعدادات إضافية**

### **تحديث العملة في النظام:**
```sql
-- تحديث إعدادات العملة
UPDATE settings SET value = 'د.ج' WHERE `key` = 'currency_symbol';
UPDATE settings SET value = 'DZD' WHERE `key` = 'currency_code';

-- إضافة إعدادات جديدة
INSERT INTO settings (`key`, `value`, `created_at`, `updated_at`) VALUES
('institution_phone', '0774479525-0665657400', NOW(), NOW()),
('institution_address', 'عوين زريقة، شارع ب رقم 16، برج بوعريريج', NOW(), NOW());
```

### **تحديث معلومات المؤسس:**
```sql
UPDATE founders SET 
    name = 'مؤسس أكاديمية Leaders Vision',
    description = 'رائد أعمال ومؤسس أكاديمية Leaders Vision المتخصصة في التكوينات المهنية والدراسات الاقتصادية',
    email = '<EMAIL>',
    phone = '0774479525'
WHERE id = 1;
```

---

## 📱 **الروابط المحدثة**

### **الواجهة الأمامية:**
- **الرئيسية**: http://localhost:8000
- **الدورات**: http://localhost:8000/Show_Course/{id}
- **المدربين**: http://localhost:8000/show_all_coache

### **لوحة التحكم:**
- **الرئيسية**: http://localhost:8000/dashboard
- **المؤسسة**: http://localhost:8000/dashboard/institution
- **المستخدمين**: http://localhost:8000/dashboard/users

### **بيانات الدخول:**
- **البريد**: <EMAIL>
- **كلمة المرور**: 123123123

---

## 🛠️ **نصائح للصيانة**

### **1. النسخ الاحتياطي:**
```bash
# نسخة احتياطية من قاعدة البيانات
mysqldump -u root -p academy > academy_backup.sql
```

### **2. مراقبة الأخطاء:**
```bash
# عرض آخر الأخطاء
tail -f storage/logs/laravel.log
```

### **3. تحديث دوري:**
```bash
# تحديث الإحصائيات
php artisan tinker
App\Models\Institution::first()->updateStatistics();
exit
```

---

## 🎉 **حالة النظام**

### **✅ تم إصلاحه:**
- خطأ HasFactory
- مشكلة str_starts_with
- تحديث autoload
- إعادة تشغيل الخادم

### **✅ جاهز للاستخدام:**
- جدول المؤسسة
- Model Institution
- Helper للمؤسسة
- صفحات الإدارة

### **📋 المطلوب:**
- إدخال بيانات المؤسسة
- اختبار الوظائف
- تخصيص المحتوى

---

**النظام جاهز الآن! يمكنك إدخال بيانات المؤسسة والبدء في الاستخدام 🚀**
