<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    public $guarded = [];

    protected $appends = ['image_path'];

    public function scopeWhenSearch($query , $search)
    {
    	return $query->when($search, function ($q) use ($search) {

    		return $q->where('name' , 'like', "%$search%")

            ->orWhere('time' , 'like', "%$search%")

            ->orWhere('description' , 'like', "%$search%")

            ->orWhere('Short_description' , 'like', "%$search%");

    	});
    }//end ofscopeWhenSearch

    public function getImagePathAttribute()
    {
        return asset('uploads/course_images/' . $this->image);

    }//end of get image path

    // public function getDemoVideoPathAttribute()
    // {
    //     $this->demo_video;

    // }//end of get image path

    // العلاقات
    public function category()
    {
        return $this->belongsTo(Category::class, 'categories_id');
    }

    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    public function studentPayments()
    {
        return $this->hasMany(StudentPayment::class);
    }

    public function studentPaymentTrackings()
    {
        return $this->hasMany(StudentPaymentTracking::class);
    }

    // علاقة مع المدربين
    public function courseInstructors()
    {
        return $this->hasMany(CourseInstructor::class);
    }

    public function instructors()
    {
        return $this->belongsToMany(User::class, 'course_instructors', 'course_id', 'user_id');
    }

    // دالة للحصول على أسماء المدربين
    public function getInstructorNamesAttribute()
    {
        return $this->instructors->pluck('name')->implode('، ');
    }

    // دالة للحصول على المدرب الأول
    public function getPrimaryInstructorAttribute()
    {
        return $this->instructors->first();
    }

}//end of models
