<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentInstallmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_installments', function (Blueprint $table) {
            $table->id();
            
            // ربط مع جدول الدفعات الرئيسي
            $table->bigInteger('student_payment_id')->unsigned();
            $table->foreign('student_payment_id')->references('id')->on('student_payments')->onDelete('cascade');
            
            // معلومات الدفعة
            $table->integer('installment_number')->comment('رقم الدفعة');
            $table->decimal('amount', 10, 2)->comment('مبلغ الدفعة');
            $table->date('due_date')->comment('تاريخ الاستحقاق');
            $table->date('paid_date')->nullable()->comment('تاريخ الدفع الفعلي');
            
            // حالة الدفعة
            $table->enum('status', [
                'pending',      // في الانتظار
                'paid',         // مدفوعة
                'overdue',      // متأخرة
                'cancelled'     // ملغية
            ])->default('pending')->comment('حالة الدفعة');
            
            // معلومات الدفع
            $table->string('payment_method')->nullable()->comment('طريقة الدفع');
            $table->string('transaction_id')->nullable()->comment('رقم المعاملة');
            $table->string('receipt_image')->nullable()->comment('صورة الإيصال');
            
            // ملاحظات
            $table->text('notes')->nullable()->comment('ملاحظات');
            
            $table->timestamps();
            
            // فهارس
            $table->index(['student_payment_id']);
            $table->index(['status']);
            $table->index(['due_date']);
            $table->index(['paid_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_installments');
    }
}
