-- إن<PERSON><PERSON><PERSON> جدول student_payments_tracking إذا لم يكن موجود
CREATE TABLE IF NOT EXISTS `student_payments_tracking` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `purchase_id` bigint(20) UNSIGNED NOT NULL,
  `student_name` varchar(255) NOT NULL,
  `student_email` varchar(255) NOT NULL,
  `student_phone` varchar(255) NOT NULL,
  `course_id` bigint(20) UNSIGNED DEFAULT NULL,
  `course_name` varchar(255) NOT NULL,
  `course_price` decimal(10,2) NOT NULL,
  `total_paid` decimal(10,2) NOT NULL DEFAULT 0.00,
  `remaining_amount` decimal(10,2) NOT NULL,
  `payment_status` enum('not_started','in_progress','completed','overdue') NOT NULL DEFAULT 'not_started',
  `enrollment_date` date NOT NULL,
  `payment_deadline` date DEFAULT NULL,
  `last_payment_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `student_payments_tracking_purchase_id_index` (`purchase_id`),
  KEY `student_payments_tracking_payment_status_index` (`payment_status`),
  KEY `student_payments_tracking_enrollment_date_index` (`enrollment_date`),
  KEY `student_payments_tracking_is_active_index` (`is_active`),
  CONSTRAINT `student_payments_tracking_purchase_id_foreign` FOREIGN KEY (`purchase_id`) REFERENCES `purchases` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- التحقق من أن جدول payment_installments يحتوي على العمود الصحيح
-- إذا كان العمود purchase_id موجود، نغيره إلى student_payment_id
-- ALTER TABLE `payment_installments` CHANGE `purchase_id` `student_payment_id` bigint(20) UNSIGNED NOT NULL;

-- إذا لم يكن العمود student_payment_id موجود، نضيفه
-- ALTER TABLE `payment_installments` ADD COLUMN `student_payment_id` bigint(20) UNSIGNED NOT NULL AFTER `id`;

-- إضافة فهرس للعمود الجديد
-- ALTER TABLE `payment_installments` ADD INDEX `payment_installments_student_payment_id_index` (`student_payment_id`);
