/* ===================================
   خطوط مخصصة لأكاديمية Leaders Vision
   Custom Fonts for Leaders Vision Academy
   =================================== */

/* تعريف الخطوط الأساسية */
:root {
    --primary-font: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --secondary-font: 'Tajawal', 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --heading-font: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

    /* ألوان محسنة */
    --primary-color: #26c7f9;
    --secondary-color: #18191A;
    --accent-color: #f39c12;
    --text-color: #333;
    --light-text: #666;
    --white: #ffffff;
    --dark: #2c3e50;

    /* مسافات */
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* تطبيق الخط الأساسي على كامل الموقع */
* {
    font-family: var(--primary-font) !important;
}

body {
    font-family: var(--primary-font) !important;
    font-weight: 400;
    line-height: 1.7;
    color: var(--text-color);
    direction: rtl;
    text-align: right;
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font) !important;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--dark);
}

h1 {
    font-size: 2.5rem;
    font-weight: 800;
}

h2 {
    font-size: 2rem;
    font-weight: 700;
}

h3 {
    font-size: 1.75rem;
    font-weight: 600;
}

h4 {
    font-size: 1.5rem;
    font-weight: 600;
}

h5 {
    font-size: 1.25rem;
    font-weight: 500;
}

h6 {
    font-size: 1rem;
    font-weight: 500;
}

/* النصوص */
p {
    font-family: var(--primary-font) !important;
    font-weight: 400;
    line-height: 1.8;
    margin-bottom: 1rem;
    color: var(--text-color);
}

/* الروابط */
a {
    font-family: var(--primary-font) !important;
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--accent-color);
    text-decoration: none;
}

/* الأزرار */
.btn {
    font-family: var(--primary-font) !important;
    font-weight: 600;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: #1e9bc7;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-dark {
    background-color: var(--dark);
    color: var(--white);
}

.btn-dark:hover {
    background-color: #34495e;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

/* حقول الإدخال */
input, textarea, select {
    font-family: var(--primary-font) !important;
    font-weight: 400;
    border-radius: var(--border-radius);
    border: 2px solid #e0e0e0;
    padding: 12px 15px;
    transition: var(--transition);
}

input:focus, textarea:focus, select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(38, 199, 249, 0.25);
    outline: none;
}

/* القوائم */
ul, ol {
    font-family: var(--primary-font) !important;
}

li {
    font-family: var(--primary-font) !important;
    line-height: 1.6;
}

/* البطاقات */
.card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-title {
    font-family: var(--heading-font) !important;
    font-weight: 600;
    color: var(--dark);
}

.card-text {
    font-family: var(--primary-font) !important;
    color: var(--light-text);
    line-height: 1.7;
}

/* شريط التنقل */
.navbar {
    font-family: var(--primary-font) !important;
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-family: var(--heading-font) !important;
    font-weight: 800;
    font-size: 1.5rem;
}

.nav-link {
    font-family: var(--primary-font) !important;
    font-weight: 500;
    transition: var(--transition);
}

/* تحسينات للعناصر المخصصة */
.banner-title {
    font-family: var(--heading-font) !important;
    font-weight: 800;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.course-title {
    font-family: var(--heading-font) !important;
    font-weight: 600;
}

.team-item h4 {
    font-family: var(--heading-font) !important;
    font-weight: 600;
}

/* تحسينات الأداء */
.animate {
    transition: var(--transition);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* تحسينات للطباعة */
@media print {
    * {
        font-family: var(--primary-font) !important;
    }
}

/* تحسينات إضافية للنصوص العربية */
.arabic-text {
    font-family: var(--primary-font) !important;
    font-weight: 400;
    line-height: 1.8;
    letter-spacing: 0.5px;
}

.arabic-heading {
    font-family: var(--heading-font) !important;
    font-weight: 700;
    line-height: 1.4;
}

/* تحسين المسافات للنصوص العربية */
.rtl-spacing {
    word-spacing: 2px;
    letter-spacing: 0.5px;
}

/* تأثيرات بصرية محسنة */
.smooth-transition {
    transition: var(--transition);
}

.hover-effect:hover {
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
}

/* تحسين الألوان */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.text-secondary-custom {
    color: var(--secondary-color) !important;
}

.bg-secondary-custom {
    background-color: var(--secondary-color) !important;
}

/* تحسينات إضافية للمكونات المخصصة */

/* تحسين عرض الدورات */
.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(38, 199, 249, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.course-image:hover .course-overlay {
    opacity: 1;
}

.course-image img {
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.course-image:hover img {
    transform: scale(1.05);
}

/* تحسين عرض المدربين */
.coach-image-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
}

.coach-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(38, 199, 249, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.coach-image-wrapper:hover .coach-overlay {
    opacity: 1;
}

.coach-image {
    width: 100%;
    height: auto;
    transition: var(--transition);
}

.coach-image-wrapper:hover .coach-image {
    transform: scale(1.1);
}

/* تحسين عرض المنشورات */
.post-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(38, 199, 249, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.image-part:hover .post-overlay {
    opacity: 1;
}

.image-part img {
    transition: var(--transition);
}

.image-part:hover img {
    transform: scale(1.05);
}

/* تحسين النماذج */
.form-control {
    font-family: var(--primary-font) !important;
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    padding: 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(38, 199, 249, 0.25);
    background: rgba(255, 255, 255, 0.15);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* تحسين الأزرار */
.btn-primary-custom {
    background: linear-gradient(45deg, var(--primary-color), #1e9bc7);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-primary-custom:hover {
    background: linear-gradient(45deg, #1e9bc7, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(38, 199, 249, 0.3);
}

.btn-primary-custom:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary-custom:hover:before {
    left: 100%;
}

/* تحسين العدادات */
.counter-item {
    padding: 30px;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.counter-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-10px);
}

.counter-number {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(45deg, var(--primary-color), #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسين النشرة البريدية */
.newsletter-form .input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.newsletter-form .form-control {
    border-right: none;
    border-radius: 0;
}

.newsletter-form .btn {
    border-radius: 0;
    border-left: none;
}

/* تحسين الأيقونات */
.fa {
    transition: var(--transition);
}

.hover-effect:hover .fa {
    transform: scale(1.1);
}

/* تحسين التمرير السلس */
html {
    scroll-behavior: smooth;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .counter-number {
        font-size: 2.5rem;
    }

    .course-item {
        flex-direction: column !important;
    }

    .course-image {
        width: 100% !important;
        margin-bottom: 15px;
    }

    .banner-buttons .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
    }

    .contact-features .col-md-4 {
        margin-bottom: 20px;
    }
}

/* تحسين الطباعة */
@media print {
    .hover-effect,
    .smooth-transition,
    .btn,
    .course-overlay,
    .coach-overlay,
    .post-overlay {
        display: none !important;
    }
}

/* تحسين الوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #e0e0e0;
        --light-text: #b0b0b0;
    }
}

/* تحسين إمكانية الوصول */
.btn:focus,
.form-control:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسين الحركة للمستخدمين الذين يفضلون تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
