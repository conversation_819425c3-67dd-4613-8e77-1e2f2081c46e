<?php

namespace App\Helpers;

use App\Models\Institution;

class InstitutionHelper
{
    /**
     * Get institution instance
     */
    public static function getInstance()
    {
        return Institution::first();
    }

    /**
     * Get institution name in Arabic
     */
    public static function getNameAr()
    {
        $institution = self::getInstance();
        return $institution ? $institution->name_ar : 'أكاديمية Leaders Vision';
    }

    /**
     * Get institution name in English
     */
    public static function getNameEn()
    {
        $institution = self::getInstance();
        return $institution ? $institution->name_en : 'Leaders Vision Academy';
    }

    /**
     * Get institution description
     */
    public static function getDescription()
    {
        $institution = self::getInstance();
        return $institution ? $institution->description_ar : 'أكاديمية متخصصة في التكوينات المهنية والدراسات الاقتصادية';
    }

    /**
     * Get institution slogan in Arabic
     */
    public static function getSloganAr()
    {
        $institution = self::getInstance();
        return $institution ? $institution->slogan_ar : 'نصنع قادة المستقبل بالعلم والتقنية';
    }

    /**
     * Get institution slogan in English
     */
    public static function getSloganEn()
    {
        $institution = self::getInstance();
        return $institution ? $institution->slogan_en : 'Building Future Leaders with Science and Technology';
    }

    /**
     * Get primary phone number
     */
    public static function getPhone1()
    {
        $institution = self::getInstance();
        return $institution ? $institution->phone_1 : '0774479525';
    }

    /**
     * Get secondary phone number
     */
    public static function getPhone2()
    {
        $institution = self::getInstance();
        return $institution ? $institution->phone_2 : '0665657400';
    }

    /**
     * Get formatted phone numbers
     */
    public static function getFormattedPhones()
    {
        $phone1 = self::getPhone1();
        $phone2 = self::getPhone2();

        if ($phone2) {
            return $phone1 . ' - ' . $phone2;
        }

        return $phone1;
    }

    /**
     * Get email
     */
    public static function getEmail()
    {
        $institution = self::getInstance();
        return $institution ? $institution->email : '<EMAIL>';
    }

    /**
     * Get website
     */
    public static function getWebsite()
    {
        $institution = self::getInstance();
        return $institution ? $institution->website : 'www.leadersvision.academy';
    }

    /**
     * Get address in Arabic
     */
    public static function getAddressAr()
    {
        $institution = self::getInstance();
        return $institution ? $institution->address_ar : 'عوين زريقة، شارع ب رقم 16';
    }

    /**
     * Get full address
     */
    public static function getFullAddress()
    {
        $institution = self::getInstance();
        if ($institution) {
            return $institution->address_ar . '، ' . $institution->city . '، ' . $institution->state . '، ' . $institution->country;
        }
        return 'عوين زريقة، شارع ب رقم 16، برج بوعريريج، الجزائر';
    }

    /**
     * Get city
     */
    public static function getCity()
    {
        $institution = self::getInstance();
        return $institution ? $institution->city : 'برج بوعريريج';
    }

    /**
     * Get state
     */
    public static function getState()
    {
        $institution = self::getInstance();
        return $institution ? $institution->state : 'برج بوعريريج';
    }

    /**
     * Get country
     */
    public static function getCountry()
    {
        $institution = self::getInstance();
        return $institution ? $institution->country : 'الجزائر';
    }

    /**
     * Get Facebook URL
     */
    public static function getFacebookUrl()
    {
        $institution = self::getInstance();
        return $institution ? $institution->facebook : 'https://www.facebook.com/Leadersvisionacademy';
    }

    /**
     * Get WhatsApp URL
     */
    public static function getWhatsAppUrl()
    {
        $institution = self::getInstance();
        if ($institution && $institution->whatsapp) {
            $phone = str_replace(['+', ' ', '-'], '', $institution->whatsapp);
            if (substr($phone, 0, 3) !== '213') {
                $phone = '213' . ltrim($phone, '0');
            }
            return "https://wa.me/{$phone}";
        }
        return 'https://wa.me/213774479525';
    }

    /**
     * Get logo URL
     */
    public static function getLogoUrl()
    {
        $institution = self::getInstance();
        if ($institution && $institution->logo_path) {
            return asset($institution->logo_path);
        }
        return asset('images/logo.png');
    }

    /**
     * Get currency symbol
     */
    public static function getCurrencySymbol()
    {
        $institution = self::getInstance();
        return $institution ? $institution->currency_symbol : 'د.ج';
    }

    /**
     * Get currency code
     */
    public static function getCurrencyCode()
    {
        $institution = self::getInstance();
        return $institution ? $institution->currency : 'DZD';
    }

    /**
     * Get currency name in Arabic
     */
    public static function getCurrencyNameAr()
    {
        $institution = self::getInstance();
        return $institution ? $institution->currency_name_ar : 'دينار جزائري';
    }

    /**
     * Format price with Algerian Dinar
     */
    public static function formatPrice($amount, $showSymbol = true)
    {
        $symbol = self::getCurrencySymbol();
        $formatted = number_format($amount, 2, '.', ',');

        return $showSymbol ? $formatted . ' ' . $symbol : $formatted;
    }

    /**
     * Get services list
     */
    public static function getServices()
    {
        $institution = self::getInstance();
        if ($institution && $institution->services) {
            return $institution->services;
        }

        return [
            'تكوينات مهنية متخصصة',
            'ورشات تطبيقية',
            'دراسات الجدوى',
            'مرافقة المشاريع'
        ];
    }

    /**
     * Get working hours
     */
    public static function getWorkingHours()
    {
        $institution = self::getInstance();
        if ($institution && $institution->working_hours) {
            return $institution->working_hours;
        }

        return [
            'السبت' => '08:00-17:00',
            'الأحد' => '08:00-17:00',
            'الاثنين' => '08:00-17:00',
            'الثلاثاء' => '08:00-17:00',
            'الأربعاء' => '08:00-17:00',
            'الخميس' => '08:00-17:00',
            'الجمعة' => 'مغلق'
        ];
    }

    /**
     * Get Google Maps URL
     */
    public static function getGoogleMapsUrl()
    {
        $institution = self::getInstance();
        if ($institution && $institution->latitude && $institution->longitude) {
            return "https://www.google.com/maps?q={$institution->latitude},{$institution->longitude}";
        }
        return "https://www.google.com/maps/search/" . urlencode(self::getFullAddress());
    }

    /**
     * Get statistics
     */
    public static function getStatistics()
    {
        $institution = self::getInstance();
        if ($institution) {
            return [
                'students_count' => $institution->students_count,
                'courses_count' => $institution->courses_count,
                'trainers_count' => $institution->trainers_count,
                'graduates_count' => $institution->graduates_count,
            ];
        }

        return [
            'students_count' => 0,
            'courses_count' => 0,
            'trainers_count' => 0,
            'graduates_count' => 0,
        ];
    }
}
