<?php $__env->startSection('content'); ?>

    <div class="content-wrapper">

        <section class="content-header">

            <h1>طلبات الدورات</h1>

            <ol class="breadcrumb">
                <li><a href="<?php echo e(route('dashboard.welcome')); ?>"><i class="fa fa-dashboard"></i> <?php echo app('translator')->get('dashboard.dashboard'); ?></a></li>
                <li class="active">طلبات الدورات</li>
            </ol>
        </section>

        <section class="content">

            <div class="box box-primary">

                <div class="box-header with-border">

                    <h3 class="box-title" style="margin-bottom: 15px">طلبات الدورات <small><?php echo e($purchases->count()); ?></small></h3>

                    

                        <div class="row">

                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" placeholder="<?php echo app('translator')->get('dashboard.search'); ?>" value="<?php echo e(request()->search); ?>">
                            </div>

                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> <?php echo app('translator')->get('dashboard.search'); ?></button>
                                <?php if(auth()->user()->hasPermission('purchases_create')): ?>
                                    <a href="<?php echo e(route('dashboard.purchases.create')); ?>" class="btn btn-primary"><i class="fa fa-plus"></i> <?php echo app('translator')->get('dashboard.add'); ?></a>
                                <?php else: ?>
                                    <a href="#" class="btn btn-primary disabled"><i class="fa fa-plus"></i> <?php echo app('translator')->get('dashboard.add'); ?></a>
                                <?php endif; ?>
                            </div>

                        </div>
                    </form><!-- end of form -->

                </div><!-- end of box header -->

                <div class="box-body">

                    <?php if($purchases->count() > 0): ?>

                        <table class="table table-hover">

                            <thead>
                            <tr>
                                <th>#</th>
                                <th><?php echo app('translator')->get('dashboard.name'); ?></th>
                                <th><?php echo app('translator')->get('dashboard.email'); ?></th>
                                <th><?php echo app('translator')->get('dashboard.phone'); ?></th>
                                <th><?php echo app('translator')->get('dashboard.image'); ?></th>
                                <th>اسم الكورس</th>
                                <th>المبلغ المدفوع</th>
                                <th>حاله الطلب</th>
                                <th><?php echo app('translator')->get('dashboard.action'); ?></th>
                            </tr>
                            </thead>

                            <tbody>
                            <?php $__currentLoopData = $purchases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index=>$purchase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td><?php echo e($purchase->first_name); ?></td>
                                    <td><?php echo e($purchase->email); ?></td>
                                    <td><?php echo e($purchase->phone); ?></td>
                                    <td>
                                        <img data-enlargeable width="100" style="cursor: zoom-in" src="<?php echo e($purchase->image_path); ?>" alt="" width="100">
                                    </td>
                                    <td>
                                        <?php if($purchase->name_course == null): ?>
                                            <?php echo e($purchase->course->name); ?>

                                        <?php else: ?>
                                            <?php echo e($purchase->name_course); ?>

                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                            $purchase->load('course');
                                            $coursePrice = $purchase->getCoursePrice();
                                            $paidAmount = $purchase->paid_amount;
                                            $progress = $purchase->getPaymentProgress();
                                        ?>
                                        <div>
                                            <strong><?php echo e(number_format($paidAmount, 0)); ?> دج</strong>
                                            <?php if($coursePrice > 0): ?>
                                                <small class="text-muted">من <?php echo e(number_format($coursePrice, 0)); ?> دج</small>
                                                <div class="progress progress-xs" style="margin-top: 5px;">
                                                    <div class="progress-bar progress-bar-<?php echo e($progress >= 100 ? 'success' : ($progress >= 50 ? 'warning' : 'danger')); ?>"
                                                         style="width: <?php echo e($progress); ?>%"></div>
                                                </div>
                                                <small class="text-muted"><?php echo e($progress); ?>%</small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($purchase->status == 0): ?>
                                            <p class="text-danger">غير مفعل</p>
                                        <?php else: ?>
                                            <p class="text-success">مفعل</p>
                                        <?php endif; ?>
                                        <?php if($purchase->isFullyPaid()): ?>
                                            <small class="label label-success">مدفوع بالكامل</small>
                                        <?php elseif($paidAmount > 0): ?>
                                            <small class="label label-warning">دفع جزئي</small>
                                        <?php else: ?>
                                            <small class="label label-danger">لم يدفع</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($purchase->status == 0): ?>
                                            <form action="<?php echo e(route('dashboard.purchases.update', $purchase->id)); ?>" method="post" style="display: inline-block">
                                                <?php echo e(csrf_field()); ?>

                                                <?php echo e(method_field('put')); ?>

                                                <input type="text" name="status" value="1" hidden="">
                                                <input type="text" name="course_id" value="<?php echo e($purchase->course_id); ?>" hidden="">
                                                <button type="submit" class="btn btn-danger btn-sm"><i class="fa fa-toggle-off"></i></button>
                                            </form><!-- end of form -->
                                        <?php else: ?>
                                            <form action="<?php echo e(route('dashboard.purchases.update', $purchase->id)); ?>" method="post" style="display: inline-block">
                                                <?php echo e(csrf_field()); ?>

                                                <?php echo e(method_field('put')); ?>

                                                <input type="text" name="status" value="0" hidden="">
                                                <input type="text" name="course_id" value="<?php echo e($purchase->course_id); ?>" hidden="">
                                                <button type="submit" class="btn btn-primary btn-sm"><i class="fa fa-toggle-on"></i> </button>
                                            </form><!-- end of form -->
                                        <?php endif; ?>
                                        <!-- زر عرض التفاصيل -->
                                        <a href="<?php echo e(route('dashboard.purchases.show', $purchase->id)); ?>" class="btn btn-primary btn-sm" title="عرض التفاصيل">
                                            <i class="fa fa-eye"></i>
                                        </a>

                                        <!-- زر عرض الدفعات -->
                                        <a href="<?php echo e(route('dashboard.student-payments.show', $purchase->id)); ?>" class="btn btn-info btn-sm" title="عرض الدفعات">
                                            <i class="fa fa-list"></i>
                                        </a>

                                        <!-- زر إضافة دفعة -->
                                        <a href="<?php echo e(route('dashboard.add-payment-simple.show', $purchase->id)); ?>" class="btn btn-success btn-sm" title="إضافة دفعة">
                                            <i class="fa fa-plus"></i>
                                        </a>

                                        <?php if(auth()->user()->hasPermission('purchases_update')): ?>
                                            <a href="<?php echo e(route('dashboard.purchases.edit', $purchase->id)); ?>" class="btn btn-info btn-sm"><i class="fa fa-edit"></i></a>
                                        <?php else: ?>
                                            <a href="#" class="btn btn-info btn-sm disabled"><i class="fa fa-edit"></i></a>
                                        <?php endif; ?>
                                        <?php if(auth()->user()->hasPermission('purchases_delete')): ?>
                                            <form action="<?php echo e(route('dashboard.purchases.destroy', $purchase->id)); ?>" method="post" style="display: inline-block">
                                                <?php echo e(csrf_field()); ?>

                                                <?php echo e(method_field('delete')); ?>

                                                <button type="submit" class="btn btn-danger delete btn-sm"><i class="fa fa-trash"></i> </button>
                                            </form><!-- end of form -->
                                        <?php else: ?>
                                            <button class="btn btn-danger btn-sm disabled"><i class="fa fa-trash"></i></button>
                                        <?php endif; ?>
                                    </td>
                                </tr>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>

                        </table><!-- end of table -->

                        

                    <?php else: ?>

                        <h2><?php echo app('translator')->get('dashboard.no_data_found'); ?></h2>

                    <?php endif; ?>

                </div><!-- end of box body -->


            </div><!-- end of box -->

        </section><!-- end of content -->

    </div><!-- end of content wrapper -->


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\Academy\resources\views/dashboard/purchases/index.blade.php ENDPATH**/ ?>