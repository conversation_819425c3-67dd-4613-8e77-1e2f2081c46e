<!DOCTYPE html>
<html dir="<?php echo e(LaravelLocalization::getCurrentLocaleDirection()); ?>">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>لوحة التحكم - أكاديمية Leaders Vision</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- Google Fonts - خطوط عربية للوحة التحكم -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    
    <link rel="stylesheet" href="<?php echo e(asset('dashboard_files/css/bootstrap.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('dashboard_files/css/ionicons.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('dashboard_files/css/skin-blue.min.css')); ?>">

    <link rel="stylesheet" href="<?php echo e(asset('dashboard_files/css/font-awesome-rtl.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('dashboard_files/css/AdminLTE-rtl.min.css')); ?>">
    <link href="https://fonts.googleapis.com/css?family=Cairo:400,700" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo e(asset('dashboard_files/css/bootstrap-rtl.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('dashboard_files/css/rtl.css')); ?>">

    <!-- Dashboard Enhancements -->
    <link rel="stylesheet" href="<?php echo e(asset('css/dashboard-enhancements.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/dashboard-leaders-vision.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/sidebar-icons.css')); ?>">

    <style>
        body, h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', sans-serif !important;
        }
    </style>

    <style>
        .mr-2{
            margin-right: 5px;
        }

        .loader {
            border: 5px solid #f3f3f3;
            border-radius: 50%;
            border-top: 5px solid #367FA9;
            width: 60px;
            height: 60px;
            -webkit-animation: spin 1s linear infinite; /* Safari */
            animation: spin 1s linear infinite;
        }

        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }
            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes  spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

    </style>
    
    <script src="<?php echo e(asset('dashboard_files/js/jquery.min.js')); ?>"></script>

    
    <link rel="stylesheet" href="<?php echo e(asset('dashboard_files/plugins/noty/noty.css')); ?>">
    <script src="<?php echo e(asset('dashboard_files/plugins/noty/noty.min.js')); ?>"></script>

    
    <link rel="stylesheet" href="<?php echo e(asset('dashboard_files/plugins/morris/morris.css')); ?>">

    
    <link rel="stylesheet" href="<?php echo e(asset('dashboard_files/plugins/icheck/all.css')); ?>">

    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2-bootstrap-theme/0.1.0-beta.10/select2-bootstrap.min.css">

    
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>

</head>
<body class="hold-transition skin-blue sidebar-mini">

<div class="wrapper">

    <header class="main-header">

        
        <a href="<?php echo e(route('dashboard.welcome')); ?>" class="logo">
            
            <span class="logo-mini"><b>L</b>VA</span>
            <span class="logo-lg"><b>Leaders-Vision</b></span>
        </a>

        <nav class="navbar navbar-static-top">
            <!-- Sidebar toggle button-->
            <a href="#" class="sidebar-toggle" data-toggle="push-menu" role="button">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </a>

            <div class="navbar-custom-menu">
                <ul class="nav navbar-nav">


                    
                    <li class="dropdown user user-menu">

                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                            <img src="<?php echo e(auth()->user()->image_path); ?>" class="user-image" alt="User Image">
                            <span class="hidden-xs"><?php echo e(auth()->user()->name); ?></span>
                        </a>
                        <ul class="dropdown-menu">

                            
                            <li class="user-header">
                                <img src="<?php echo e(auth()->user()->image_path); ?>" class="img-circle" alt="User Image">

                                <p>
                                    <?php echo e(auth()->user()->name); ?>

                                </p>
                            </li>

                            
                            <li class="user-footer">


                                <a href="<?php echo e(route('logout')); ?>" class="btn btn-default btn-flat" onclick="event.preventDefault();
                                                 document.getElementById('logout-form').submit();"><?php echo app('translator')->get('home.logout'); ?></a>

                                <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display: none;">
                                    <?php echo csrf_field(); ?>
                                </form>

                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </nav>

    </header>

    <?php echo $__env->make('layouts.dashboard._aside', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->yieldContent('content'); ?>

    <?php echo $__env->make('partials._session', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</div><!-- end of wrapper -->


<script src="<?php echo e(asset('dashboard_files/js/bootstrap.min.js')); ?>"></script>


<script src="<?php echo e(asset('dashboard_files/plugins/icheck/icheck.min.js')); ?>"></script>


<script src="<?php echo e(asset('dashboard_files/js/fastclick.js')); ?>"></script>


<script src="<?php echo e(asset('dashboard_files/js/adminlte.min.js')); ?>"></script>


<script src="<?php echo e(asset('dashboard_files/plugins/ckeditor/ckeditor.js')); ?>"></script>


<script src="<?php echo e(asset('dashboard_files/js/jquery.number.min.js')); ?>"></script>


<script src="<?php echo e(asset('dashboard_files/js/printThis.js')); ?>"></script>


<script src="https://cdnjs.cloudflare.com/ajax/libs/raphael/2.1.0/raphael-min.js"></script>
<script src="<?php echo e(asset('dashboard_files/plugins/morris/morris.min.js')); ?>"></script>


<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>


<script src="<?php echo e(asset('dashboard_files/js/custom/image_preview.js')); ?>"></script>
<script src="<?php echo e(asset('dashboard_files/js/custom/order.js')); ?>"></script>


<script src="<?php echo e(asset('js/dashboard-enhancements.js')); ?>"></script>

<script>
    $(document).ready(function () {

        $('img[data-enlargeable]').addClass('img-enlargeable').click(function() {
          var src = $(this).attr('src');
          var modal;

          function removeModal() {
            modal.remove();
            $('body').off('keyup.modal-close');
          }
          modal = $('<div>').css({
            background: 'RGBA(0,0,0,.5) url(' + src + ') no-repeat center',
            backgroundSize: 'contain',
            width: '100%',
            height: '100%',
            position: 'fixed',
            zIndex: '10000',
            top: '0',
            left: '0',
            cursor: 'zoom-out'
          }).click(function() {
            removeModal();
          }).appendTo('body');
          //handling ESC
          $('body').on('keyup.modal-close', function(e) {
            if (e.key === 'Escape') {
              removeModal();
            }
          });
        });

        $('.sidebar-menu').tree();

        //icheck
        $('input[type="checkbox"].minimal, input[type="radio"].minimal').iCheck({
            checkboxClass: 'icheckbox_minimal-blue',
            radioClass: 'iradio_minimal-blue'
        });

        //delete
        $('.delete').click(function (e) {

            var that = $(this)

            e.preventDefault();

            var n = new Noty({
                text: "<?php echo app('translator')->get('dashboard.confirm_delete'); ?>",
                type: "warning",
                killer: true,
                buttons: [
                    Noty.button("<?php echo app('translator')->get('dashboard.yes'); ?>", 'btn btn-success mr-2', function () {
                        that.closest('form').submit();
                    }),

                    Noty.button("<?php echo app('translator')->get('dashboard.no'); ?>", 'btn btn-primary mr-2', function () {
                        n.close();
                    })
                ]
            });

            n.show();

        });//end of delete

        // // image preview
        // $(".image").change(function () {
        //
        //     if (this.files && this.files[0]) {
        //         var reader = new FileReader();
        //
        //         reader.onload = function (e) {
        //             $('.image-preview').attr('src', e.target.result);
        //         }
        //
        //         reader.readAsDataURL(this.files[0]);
        //     }
        //
        // });

        CKEDITOR.config.language =  "<?php echo e(app()->getLocale()); ?>";

    });//end of ready



</script>
<?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\Academy\resources\views/layouts/dashboard/app.blade.php ENDPATH**/ ?>