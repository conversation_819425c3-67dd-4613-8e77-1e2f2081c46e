<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCourseDetailsToCoursesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('courses', function (Blueprint $table) {
            // أهداف الدورة
            $table->text('course_objectives')->nullable()->after('description')->comment('أهداف الدورة');
            
            // الفئة المستهدفة
            $table->text('target_audience')->nullable()->after('course_objectives')->comment('الفئة المستهدفة');
            
            // معلومات إضافية مفيدة
            $table->text('course_features')->nullable()->after('target_audience')->comment('مميزات الدورة');
            $table->text('learning_outcomes')->nullable()->after('course_features')->comment('مخرجات التعلم');
            $table->text('prerequisites')->nullable()->after('learning_outcomes')->comment('المتطلبات المسبقة');
            
            // معلومات التدريب
            $table->enum('training_type', ['online', 'offline', 'hybrid'])->default('offline')->after('prerequisites')->comment('نوع التدريب');
            $table->boolean('certificate_provided')->default(true)->after('training_type')->comment('هل تقدم شهادة');
            
            // معلومات الجدولة
            $table->date('start_date')->nullable()->after('certificate_provided')->comment('تاريخ بداية الدورة');
            $table->integer('session_count')->nullable()->after('start_date')->comment('عدد الجلسات');
            $table->integer('session_duration')->nullable()->after('session_count')->comment('مدة الجلسة بالدقائق');
            
            // معلومات المكان
            $table->string('location')->nullable()->after('session_duration')->comment('مكان التدريب');
            $table->text('address')->nullable()->after('location')->comment('العنوان التفصيلي');
            
            // حالة الدورة
            $table->enum('status', ['draft', 'published', 'archived'])->default('published')->after('address')->comment('حالة الدورة');
            $table->boolean('featured')->default(false)->after('status')->comment('دورة مميزة');
            
            // فهارس للبحث
            $table->index(['status']);
            $table->index(['featured']);
            $table->index(['training_type']);
            $table->index(['start_date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn([
                'course_objectives',
                'target_audience', 
                'course_features',
                'learning_outcomes',
                'prerequisites',
                'training_type',
                'certificate_provided',
                'start_date',
                'session_count',
                'session_duration',
                'location',
                'address',
                'status',
                'featured'
            ]);
        });
    }
}
