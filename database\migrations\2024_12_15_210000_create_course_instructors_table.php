<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCourseInstructorsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_instructors', function (Blueprint $table) {
            $table->id();
            
            // ربط مع جدول الدورات
            $table->bigInteger('course_id')->unsigned();
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('cascade');
            
            // ربط مع جدول المستخدمين (المدربين)
            $table->bigInteger('user_id')->unsigned();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            // معلومات إضافية عن دور المدرب في هذه الدورة
            $table->string('role')->default('مدرب')->comment('دور المدرب في الدورة');
            $table->boolean('is_primary')->default(false)->comment('هل هو المدرب الرئيسي');
            $table->text('specialization')->nullable()->comment('تخصص المدرب في هذه الدورة');
            
            $table->timestamps();
            
            // فهارس للبحث السريع
            $table->index(['course_id']);
            $table->index(['user_id']);
            $table->index(['is_primary']);
            
            // منع التكرار - مدرب واحد لا يمكن أن يكون في نفس الدورة مرتين
            $table->unique(['course_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course_instructors');
    }
}
